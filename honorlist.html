<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
<meta charset="utf-8" />
<meta name="format-detection" content="telephone=no" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta name="HandheldFriendly" content="true" />
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
<!--[if lt IE 9]>
    <script type="text/javascript" src="static/js/css3-mediaqueries.js">
    </script>
    <![endif]-->
<meta name="Author" content="易思信网页工作室 www.eczone.net ">
<!--
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
           网站设计制作 by 易思信网页工作室 www.eczone.net   
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
    -->
<title>聚算云技术白皮书 - 算力共享实践</title>
<meta name="keywords" content='算力调度平台,云服务解决方案,边缘计算服务,AI算力服务,算力资源池,聚算云' />
<meta name="description" content='聚算云致力于用共享经济重塑算力分配规则，建立一个连接算力供应方与需求方的智能调度平台，实现算力资源的自由流动和高效利用。' />
<link rel="shortcut icon" href="favicon.ico" />
<link rel="stylesheet" href="static/css/animate.css" type="text/css" >
<link rel="stylesheet" href="static/css/common.css" type="text/css" >
<link rel="stylesheet" href="static/css/style.css" type="text/css" >
<script type="text/javascript" src="static/js/jquery.min.js"></script> 
</head>
<body>
<section class="wrapper">
  <header class="header"> <a href="/" class="logo" title="返回首页"> <img class="hide" src="static/picture/logo.png" alt="聚算云" /> <img class="show" src="static/picture/logo2.png" alt="聚算云" /> </a>
    <ul class="navs">
      <li class=""><a href="/">首页</a></li>
      <li ><a href="page-abouts.html">关于我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业使命与愿景<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业文化<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">团队介绍<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">投资者关系<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="products-services.html">产品与服务<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="solution-cases.html">解决方案<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="solution-cases.html#gaming-cafe">电竞云网吧<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#ai-glasses">AI眼镜<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#digital-transformation">企业数字化转型<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#hpc-research">科研机构高性能计算<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#big-data-analytics">数据分析与大数据处理<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#media-rendering">媒体内容制作与渲染<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="hyyy-hyyy.html">成功案例<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">互联网行业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">人工智能<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">制造业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">金融服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教育与科研<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">政府与公共服务<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">技术与创新<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">技术白皮书<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">研发团队<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">专利与知识产权<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">合作与生态<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">合作伙伴<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">生态体系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">合作模式<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">资源中心<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">产品手册<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教程与指南<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">行业洞察<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">算力知识库<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li><a href="contact-contacts.html">联系我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">销售咨询<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">技术支持<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">媒体联系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">办公地点<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
    </ul>
    <div class="header-menu">
      <div class="header-search"> <span class="iconfont">&nbsp;</span> </div>
      <div class="menu-btn">
        <p>MENU</p>
        <div class="menubtn"> <span></span> </div>
      </div>
      <div class="menu-flex">
        <div class="menu-bg"></div>
        <div class="menu-right">
          <ul class="menu-list">
            <li><a href="/">首页</a></li>
            <li ><a href="page-abouts.html">关于我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业使命与愿景<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业文化<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">团队介绍<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">投资者关系<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="products-services.html">产品与服务<em></em></a>
              <ol class="menu-leval">
                <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="solution-cases.html">解决方案<em></em></a>
              <ol class="menu-leval">
                <li><a href="solution-cases.html#gaming-cafe">电竞云网吧<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#ai-glasses">AI眼镜<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#digital-transformation">企业数字化转型<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#hpc-research">科研机构高性能计算<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#big-data-analytics">数据分析与大数据处理<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#media-rendering">媒体内容制作与渲染<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="hyyy-hyyy.html">成功案例<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">互联网行业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">人工智能<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">制造业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">金融服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教育与科研<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">政府与公共服务<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">技术与创新<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">技术白皮书<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">研发团队<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">专利与知识产权<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">合作与生态<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">合作伙伴<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">生态体系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">合作模式<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">资源中心<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">产品手册<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教程与指南<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">行业洞察<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">算力知识库<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="contact-contacts.html">联系我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">销售咨询<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">技术支持<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">媒体联系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">办公地点<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
          </ul>
          <div class="menu-ip-down">
            <div class="online-shopp"> </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class="main-content-wrap">
    <div class="content-wrap">
      <div class="head-search">
        <form action="search-search.html" method="post">
          <input class="search-ipt" type="text" name="query" placeholder="输入关键词...">
          <input class="search-btn" type="submit" name="submit" value="" title="搜索" aria-label="搜索">
        </form>
      </div>
    </div>
  </div>
  <div class="mobile-body-mask"></div>
  <div class="pbanner">
    <div class="product-hide">
      <figure><img class="pc" src="static/picture/casebanner.jpg" alt="聚算云技术白皮书"/>
        <div class="sjimg casebanner-bg"> </div>
      </figure>
    </div>
    <div class="series global-fix">
      <p class="article-block slidetop">Technical Whitepaper</p>
      <div class="series-title article-block slidetop detay1 syht"> <strong>算力共享实践 · 创新价值共赢</strong> </div>
    </div>
    <div class="sea-down defaul"> <i></i> <span>向下滑动</span> </div>
  </div>
  
  <!--pageTop-->
  <div class="pageTop">
    <div class="w1600 clearfix">
      <div class="pageNav">
        <ul class="navlist clearfix swiper-wrapper">
          <li><a href="#" ><span>技术白皮书</span></a></li>
          <li><a href="#" ><span>研发团队</span></a></li>
          <li class="on"><a href="#" ><span>专利与知识产权</span></a></li>
        </ul>
      </div>
    </div>
  </div>
  <!--pageTop end--> 
  
  <!--page-->
  <div class="page clearfix"> 
    <!--pageInfo-->
    <div class="pageInfo clearfix">
      <div class="pageTit"><span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">Patents & IP</span>
        <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"><span class="syht">聚算云专利与知识产权</span></div>
      </div>
      <div class="ip-intro wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s">
        <p>聚算云高度重视知识产权保护与技术创新，已累计申请并获得数十项算力调度与资源优化相关专利。我们的专利组合覆盖了分布式计算、智能资源调度、边缘计算及AI加速等核心技术领域，为用户提供可靠的技术保障。</p>
      </div>
      
      <!-- 专利统计部分 -->
      <div class="patent-stats-section">
        <div class="stats-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="stat-item">
            <div class="stat-number"><span class="counter">38</span>+</div>
            <div class="stat-label">授权专利</div>
            <svg class="stat-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3m6.82 6L12 12.72 5.18 9 12 5.28 18.82 9M17 16l-5 2.72L7 16v-3.73L12 15l5-2.73V16z"></path>
            </svg>
          </div>
          <div class="stat-item">
            <div class="stat-number"><span class="counter">20</span>+</div>
            <div class="stat-label">PCT国际专利</div>
            <svg class="stat-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"></path>
            </svg>
          </div>
          <div class="stat-item">
            <div class="stat-number"><span class="counter">42</span>+</div>
            <div class="stat-label">软件著作权</div>
            <svg class="stat-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M9.9,19L6.8,15.9L8.2,14.5L9.9,16.2L15.1,11L16.5,12.4L9.9,19Z"></path>
            </svg>
          </div>
          <div class="stat-item">
            <div class="stat-number"><span class="counter">15</span>+</div>
            <div class="stat-label">行业标准</div>
            <svg class="stat-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M6,2C4.89,2 4,2.89 4,4V20A2,2 0 0,0 6,22H10V20.09L12.09,18H6V16H14.09L16.09,14H6V12H18.09L20,10.09V8L14,2H6M13,3.5L18.5,9H13V3.5M20.15,13C20,13 19.86,13.05 19.75,13.16L18.73,14.18L20.82,16.26L21.84,15.25C22.05,15.03 22.05,14.67 21.84,14.46L20.54,13.16C20.43,13.05 20.29,13 20.15,13M18.14,14.77L12,20.92V23H14.08L20.23,16.85L18.14,14.77Z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <!-- 核心专利领域部分 -->
      <div class="patent-fields-section">
        <h3 class="section-title wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">核心专利领域</h3>
        <div class="fields-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="field-item">
            <div class="field-icon-wrapper">
              <svg class="field-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M15,13H16.5V15.82L18.94,17.23L18.19,18.53L15,16.69V13M19,8H5V19H9.67C9.24,18.09 9,17.07 9,16A7,7 0 0,1 16,9C17.07,9 18.09,9.24 19,9.67V8M5,21C3.89,21 3,20.1 3,19V5C3,3.89 3.89,3 5,3H6V1H8V3H16V1H18V3H19A2,2 0 0,1 21,5V11.1C22.24,12.36 23,14.09 23,16A7,7 0 0,1 16,23C14.09,23 12.36,22.24 11.1,21H5M16,11.15A4.85,4.85 0 0,0 11.15,16C11.15,18.68 13.32,20.85 16,20.85A4.85,4.85 0 0,0 20.85,16C20.85,13.32 18.68,11.15 16,11.15Z"></path>
              </svg>
            </div>
            <h4>实时算力调度</h4>
            <p>基于多源异构资源的智能调度系统，实现毫秒级任务分配与资源优化，支持针对不同计算任务的特性进行动态资源分配。</p>
          </div>
          <div class="field-item">
            <div class="field-icon-wrapper">
              <svg class="field-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M6,7H18A5,5 0 0,1 23,12A5,5 0 0,1 18,17C16.36,17 14.91,16.21 14,15H10C9.09,16.21 7.64,17 6,17A5,5 0 0,1 1,12A5,5 0 0,1 6,7M19.75,9.5A1.25,1.25 0 0,0 18.5,10.75A1.25,1.25 0 0,0 19.75,12A1.25,1.25 0 0,0 21,10.75A1.25,1.25 0 0,0 19.75,9.5M17.25,12A1.25,1.25 0 0,0 16,13.25A1.25,1.25 0 0,0 17.25,14.5A1.25,1.25 0 0,0 18.5,13.25A1.25,1.25 0 0,0 17.25,12M5,9V11H3V13H5V15H7V13H9V11H7V9H5Z"></path>
              </svg>
            </div>
            <h4>边缘计算架构</h4>
            <p>针对边缘设备的低延迟计算优化方案，解决移动设备、物联网终端的算力瓶颈问题，实现云-边-端协同计算。</p>
          </div>
          <div class="field-item">
            <div class="field-icon-wrapper">
              <svg class="field-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.29,21H12.12L21,12.12V9.29M19,10.59L10.59,19H7V15.41L15.41,7H19M11.88,3L3,11.88V14.71L14.71,3M19.5,2.06L21.94,4.5L4.5,21.94L2.06,19.5L19.5,2.06Z"></path>
              </svg>
            </div>
            <h4>资源隔离与安全</h4>
            <p>多租户环境下的计算资源安全隔离技术，保障在共享算力环境中的数据安全与隐私保护，适用于金融、医疗等高安全需求场景。</p>
          </div>
          <div class="field-item">
            <div class="field-icon-wrapper">
              <svg class="field-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.5,5.6L5,7L6.4,4.5L5,2L7.5,3.4L10,2L8.6,4.5L10,7L7.5,5.6M19.5,15.4L22,14L20.6,16.5L22,19L19.5,17.6L17,19L18.4,16.5L17,14L19.5,15.4M22,2L20.6,4.5L22,7L19.5,5.6L17,7L18.4,4.5L17,2L19.5,3.4L22,2M13.34,12.78L15.78,10.34L13.66,8.22L11.22,10.66L13.34,12.78M14.37,7.29L16.71,9.63C17.1,10 17.1,10.65 16.71,11.04L5.04,22.71C4.65,23.1 4,23.1 3.63,22.71L1.29,20.37C0.9,20 0.9,19.35 1.29,18.96L12.96,7.29C13.35,6.9 14,6.9 14.37,7.29Z"></path>
              </svg>
            </div>
            <h4>AI加速与优化</h4>
            <p>针对深度学习、机器学习框架的计算加速技术，提高AI模型训练与推理效率，降低大规模AI计算的能耗与成本。</p>
          </div>
          <div class="field-item">
            <div class="field-icon-wrapper">
              <svg class="field-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M3,3H21V5H3V3M3,7H15V9H3V7M3,11H21V13H3V11M3,15H15V17H3V15M3,19H21V21H3V19Z"></path>
              </svg>
            </div>
            <h4>负载预测与均衡</h4>
            <p>基于历史数据与机器学习的负载预测系统，实现资源的提前预留与弹性伸缩，大幅提升整体系统的资源利用率。</p>
          </div>
          <div class="field-item">
            <div class="field-icon-wrapper">
              <svg class="field-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M7,15H17V17H7V15M10.3,11.2L8.4,9.3L7,10.7L10.3,14L17,7.3L15.6,5.9L10.3,11.2Z"></path>
              </svg>
            </div>
            <h4>计算成本优化</h4>
            <p>面向多云环境的成本优化算法，根据计算任务特性和资源价格实现最优资源调度，降低企业IT基础设施总体拥有成本。</p>
          </div>
        </div>
      </div>
      
      <!-- 专利证书展示部分 -->
      <div class="patent-certificates-section">
        <h3 class="section-title wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">专利证书展示</h3>
        <!-- 添加滚动容器和导航按钮 -->
        <div class="certificates-scroll-wrapper wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <!-- 左右导航按钮 -->
          <div class="certificate-nav certificate-nav-prev">
            <svg viewBox="0 0 24 24" class="nav-icon">
              <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </svg>
          </div>
          <div class="certificate-nav certificate-nav-next">
            <svg viewBox="0 0 24 24" class="nav-icon">
              <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
            </svg>
          </div>
          
          <!-- 证书容器 -->
          <div class="certificates-track-container">
            <div class="certificates-container">
              <div class="certificate-item">
                <div class="certificate-image">
                  <img src="static/picture/solution1-tu2.jpg" alt="分布式资源调度算法专利证书" />
                  <div class="certificate-overlay">
                    <div class="certificate-detail">
                      <h5>分布式资源调度算法与系统</h5>
                      <p>专利号: ZL 2019 1 0123456.X</p>
                      <p>授权日期: 2021年3月15日</p>
                    </div>
                  </div>
                </div>
                <div class="certificate-info">
                  <h4>分布式资源调度算法与系统</h4>
                  <p class="certificate-number">ZL 2019 1 0123456.X</p>
                </div>
              </div>
          <div class="certificate-item">
            <div class="certificate-image">
              <img src="static/picture/solution1-tu2.jpg" alt="分布式资源调度算法专利证书" />
              <div class="certificate-overlay">
                <div class="certificate-detail">
                  <h5>分布式资源调度算法与系统</h5>
                  <p>专利号: ZL 2019 1 0123456.X</p>
                  <p>授权日期: 2021年3月15日</p>
                </div>
              </div>
            </div>
            <div class="certificate-info">
              <h4>分布式资源调度算法与系统</h4>
              <p class="certificate-number">ZL 2019 1 0123456.X</p>
            </div>
          </div>
          <div class="certificate-item">
            <div class="certificate-image">
              <img src="static/picture/solution1-tu2.jpg" alt="边缘设备算力优化方法专利证书" />
              <div class="certificate-overlay">
                <div class="certificate-detail">
                  <h5>边缘设备算力优化方法</h5>
                  <p>专利号: ZL 2020 1 0234567.X</p>
                  <p>授权日期: 2021年8月21日</p>
                </div>
              </div>
            </div>
            <div class="certificate-info">
              <h4>边缘设备算力优化方法</h4>
              <p class="certificate-number">ZL 2020 1 0234567.X</p>
            </div>
          </div>
          <div class="certificate-item">
            <div class="certificate-image">
              <img src="static/picture/solution1-tu2.jpg" alt="算力资源共享交易系统专利证书" />
              <div class="certificate-overlay">
                <div class="certificate-detail">
                  <h5>算力资源共享交易系统</h5>
                  <p>专利号: ZL 2020 1 0345678.X</p>
                  <p>授权日期: 2021年11月30日</p>
                </div>
              </div>
            </div>
            <div class="certificate-info">
              <h4>算力资源共享交易系统</h4>
              <p class="certificate-number">ZL 2020 1 0345678.X</p>
            </div>
          </div>
          <div class="certificate-item">
            <div class="certificate-image">
              <img src="static/picture/solution1-tu2.jpg" alt="深度学习训练加速方法专利证书" />
              <div class="certificate-overlay">
                <div class="certificate-detail">
                  <h5>深度学习训练加速方法</h5>
                  <p>专利号: ZL 2020 1 0456789.X</p>
                  <p>授权日期: 2022年1月18日</p>
                </div>
              </div>
            </div>
            <div class="certificate-info">
              <h4>深度学习训练加速方法</h4>
              <p class="certificate-number">ZL 2020 1 0456789.X</p>
            </div>
          </div>
        </div>
        </div>
        </div>
        <!-- 移除查看更多专利按钮 -->
      </div>
      
      <!-- 技术创新时间线 -->
      <div class="innovation-timeline-section">
        <h3 class="section-title wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">技术创新历程</h3>
        
        <!-- 新的时间轴设计 -->
        <div class="timeline-container-wrapper">
          <!-- 左右导航按钮 -->
          <div class="timeline-nav timeline-nav-prev">
            <svg viewBox="0 0 24 24" class="nav-icon">
              <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </svg>
          </div>
          <div class="timeline-nav timeline-nav-next">
            <svg viewBox="0 0 24 24" class="nav-icon">
              <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
            </svg>
          </div>
          
          <!-- 时间轴内容 -->
          <div class="timeline-scroll-container">
            <div class="timeline-track">
              <!-- 时间线 -->
              <div class="timeline-line"></div>
              
              <!-- 时间节点 -->
              <div class="timeline-points">
                <!-- 2018年 -->
                <div class="timeline-point" data-year="2018">
                  <div class="point-pulse"></div>
                  <div class="point-year">2018</div>
                  <div class="point-content">
                    <div class="point-image-container">
                      <img src="static/picture/solution1-tu2.jpg" alt="聚算云成立" class="point-image" />
                      <div class="point-overlay">
                        <span>查看详情</span>
                      </div>
                    </div>
              <h4>聚算云成立</h4>
              <p>团队开始研发分布式算力调度系统的核心框架，致力于打造面向未来的算力共享生态系统。创始团队由来自知名互联网公司和科研机构的专家组成，融合了分布式系统、云计算与人工智能等多个领域的专业知识。</p>
            </div>
          </div>
                
                <!-- 2019年 -->
                <div class="timeline-point" data-year="2019">
                  <div class="point-pulse"></div>
                  <div class="point-year">2019</div>
                  <div class="point-content">
                    <div class="point-image-container">
                      <img src="static/picture/solution1-tu2.jpg" alt="首批专利申请" class="point-image" />
                      <div class="point-overlay">
                        <span>查看详情</span>
                      </div>
                    </div>
              <h4>首批专利申请</h4>
              <p>完成5项核心专利申请，涵盖资源调度与优化算法。这些专利包括了创新性的分布式计算资源动态调度方法、异构计算环境下的任务分配策略以及基于深度学习的计算负载预测技术，为公司的技术壁垒奠定了坚实基础。</p>
            </div>
          </div>
                
                <!-- 2020年 -->
                <div class="timeline-point" data-year="2020">
                  <div class="point-pulse"></div>
                  <div class="point-year">2020</div>
                  <div class="point-content">
                    <div class="point-image-container">
                      <img src="static/picture/solution1-tu2.jpg" alt="边缘计算技术突破" class="point-image" />
                      <div class="point-overlay">
                        <span>查看详情</span>
                      </div>
                    </div>
              <h4>边缘计算技术突破</h4>
              <p>研发边缘计算加速技术，获得8项相关专利授权。团队突破了边缘设备低功耗高性能计算的技术难题，实现了云端与边缘设备之间的智能协同计算，特别适合物联网和智能穿戴设备等场景，极大扩展了算力共享网络的应用范围。</p>
            </div>
          </div>
                
                <!-- 2021年 -->
                <div class="timeline-point" data-year="2021">
                  <div class="point-pulse"></div>
                  <div class="point-year">2021</div>
                  <div class="point-content">
                    <div class="point-image-container">
                      <img src="static/picture/solution1-tu2.jpg" alt="AI加速技术发布" class="point-image" />
                      <div class="point-overlay">
                        <span>查看详情</span>
                      </div>
                    </div>
              <h4>AI加速技术发布</h4>
              <p>发布AI模型训练加速技术，性能提升达350%。这一技术通过创新的分布式训练算法和资源调度策略，显著降低了企业在AI大模型训练方面的成本，同时提高了训练效率。该技术已成功应用于多家AI公司和研究机构的生产环境。</p>
            </div>
          </div>
                
                <!-- 2022年 -->
                <div class="timeline-point" data-year="2022">
                  <div class="point-pulse"></div>
                  <div class="point-year">2022</div>
                  <div class="point-content">
                    <div class="point-image-container">
                      <img src="static/picture/solution1-tu2.jpg" alt="国际专利布局" class="point-image" />
                      <div class="point-overlay">
                        <span>查看详情</span>
                      </div>
                    </div>
              <h4>国际专利布局</h4>
              <p>完成PCT国际专利申请15项，覆盖北美、欧洲与亚太地区。公司加快了全球化技术布局步伐，保护核心技术创新成果，同时开始拓展国际市场，与多家国际科技企业建立了战略合作关系，算力共享理念逐渐获得全球市场认可。</p>
            </div>
          </div>
                
                <!-- 2023年 -->
                <div class="timeline-point" data-year="2023">
                  <div class="point-pulse"></div>
                  <div class="point-year">2023</div>
                  <div class="point-content">
                    <div class="point-image-container">
                      <img src="static/picture/solution1-tu2.jpg" alt="行业标准制定" class="point-image" />
                      <div class="point-overlay">
                        <span>查看详情</span>
                      </div>
                    </div>
              <h4>行业标准制定</h4>
              <p>参与制定3项算力网络国家行业标准，成为技术领导者。公司积极参与算力网络相关国家标准和行业规范的制定工作，推动了算力共享行业的规范化发展，同时进一步巩固了公司在行业中的技术领导地位，为未来的发展奠定了坚实基础。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 详细信息弹窗 -->
        <div class="timeline-modal">
          <div class="modal-overlay"></div>
          <div class="modal-container">
            <div class="modal-close">&times;</div>
            <div class="modal-content">
              <div class="modal-header">
                <h3 class="modal-title"></h3>
                <div class="modal-year"></div>
              </div>
              <div class="modal-body">
                <div class="modal-images">
                  <!-- 简化后的图片容器 -->
                  <div class="modal-image-single">
                    <img src="" alt="" class="modal-main-image">
                    <div class="image-switcher">
                      <span>下一张</span>
                      <svg viewBox="0 0 24 24" class="switcher-icon">
                        <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="modal-description"></div>
                <div class="modal-details">
                  <ul class="detail-list">
                    <!-- 详情列表会通过JavaScript动态插入 -->
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--End--> 
  </div>
  <!--page End--> 
  <!--footer--> 
  <!--footer-->
  <div class="footer clearfix"> 
    <!--footer-nav-->
    <div class="footer-nav">
      <div class="w1600 clearfix">
        <div class="left">
          <div class="leftBox clearfix">
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="solution-cases.html">蒸汽节能解决方案</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="solution-1805-1809.html">ESOS-蒸汽系统节能优化</a></li>
                    <li class="t2"><a href="solution-1805-1810.html">蒸汽疏水阀调查</a></li>
                    <li class="t2"><a href="solution-1805-1811.html">蒸汽系统诊断调查</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="advantage-ys.html">我们的优势</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="advantage-ys.html">一体式蒸汽工程解决方案</a></li>
                    <li class="t2"><a href="advantage-ys.html#adBox2">创新节能技术</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="page-abouts.html">关于我们</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="page-1751-1752.html">公司介绍</a></li>
                    <li class="t2"><a href="page2-1751-1753.html">常问问题</a></li>
                    <li class="t2"><a href="solution-1751-1832.html">服务客户</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="hyyy-hyyy.html">行业应用案例</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="hylist-1808-1814.html">化工</a></li>
                    <li class="t2"><a href="hylist-1808-1815.html">石化</a></li>
                    <li class="t2"><a href="hylist-1808-1816.html">制药</a></li>
                    <li class="t2"><a href="hylist-1808-1817.html">食品饮料</a></li>
                    <li class="t2"><a href="hylist-1808-1818.html">其它</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="rightBox clearfix">
            <table width="100%">
              <tbody>
                <tr>
                  <td><h1> 服务热线 </h1></td>
                </tr>
                <tr>
                  <td class="tel"><h1> <span>+86 13321109027</span> </h1>
                    <p> 诚挚为您服务 </p></td>
                </tr>
              </tbody>
            </table>
            <div class="bottom-share"> <span>关注我们：</span> <a class="iconfont email" href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">&#xe6db;</a> <a class="iconfont qq" href="http://wpa.qq.com/msgrd?v=3&uin=393202489&site=kingtin&menu=yes" target="_blank" rel="noopener noreferrer">&#xe60f;</a> <a class="iconfont weix"  rel="noopener noreferrer">&#xe660;<span class="img"><img src="static/picture/202203011551003232.jpg" alt="" /><br />
              </span></a> </div>
          </div>
        </div>
      </div>
      <div class="bottom-wz">
        <div class="w1600 clearfix">思德乐，提供卓越的蒸汽工程解决方案。</div>
      </div>
    </div>
    <!--footer-nav end-->
    <div class="bq clearfix">
      <div class="w1600 clearfix">
        <div class="bqname"> &copy;2021 佛山市思德乐能源科技有限公司 版权所有 <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">粤ICP备2022022663号</a> </div>
        <div class="beian"> 网站设计: <a href="http://www.king-tin.com/" target="_blank" rel="noopener noreferrer">KINGTIN</a><script charset="UTF-8" id="LA_COLLECT" src="static/js/js-sdk-pro.min.js"></script><script>LA.init({id: "Jh6tR8OE31b5wc6v",ck: "Jh6tR8OE31b5wc6v"})</script> 
        </div>
      </div>
    </div>
  </div>
  <!--End footer-->
  <div class="common-bott">
    <div class="back-top commontop iconfont">&#xe731;</div>
  </div>
</section>
<script type="text/javascript" src="static/js/wow.min2.js"></script> 
<script type="text/javascript" src="static/js/plugin.js"></script> 
<script type="text/javascript" src="static/js/page.js"></script> 
<script>
var scrolling = true;
var timer = null;
$(document).on('mousewheel DOMMouseScroll', function (e) {
        if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
        navigateUp();
        } else {
        if (e.pageY <= w_height && scrolling && !isMobile) {
        clearTimeout(timer);
        scrolling = false;
        timer = setTimeout(function () {
        jQuery("html,body").stop(true, true).animate({ scrollTop: w_height }, 1200, "easeOutQuint")
        }, 100)
        }
        }
});
function navigateUp() {
        scrolling = true;
}
</script>
<style>
.icon-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}
.icon-wrapper {
  text-align: center;
  margin: 0 15px;
}
.svg-icon {
  width: 64px;
  height: 64px;
  fill: #2b64a7;
  margin-bottom: 10px;
}
.icon-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}
.brand-core-box {
  display: flex;
  justify-content: space-around;
  margin: 30px 0;
  flex-wrap: wrap;
}

.brand-core-item {
  text-align: center;
  width: 200px;
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.brand-core-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(43, 100, 167, 0.2);
  background-color: rgba(43, 100, 167, 0.05);
}

.brand-core-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(43, 100, 167, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.4s ease;
}

.brand-core-item:hover:before {
  opacity: 1;
  transform: scale(2);
}

.brand-core-svg {
  width: 64px;
  height: 64px;
  margin-bottom: 15px;
  fill: #2b64a7;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 1;
}

.brand-core-item:hover .brand-core-svg {
  transform: rotate(15deg) scale(1.2);
  fill: #2b64a7;
}

.brand-core-title {
  color: #2b64a7;
  font-size: 18px;
  margin: 10px 0;
  font-weight: bold;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.brand-core-item:hover .brand-core-title {
  transform: scale(1.05);
}

.brand-core-text {
  color: #333;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.brand-core-item:hover .brand-core-text {
  color: #2b64a7;
}

/* 通用样式 */
.ip-intro {
  max-width: 1200px;
  margin: 30px auto;
  text-align: center;
  font-size: 18px;
  line-height: 1.8;
  color: #333;
  padding: 0 20px;
}

.section-title {
  text-align: center;
  color: #2b64a7;
  font-size: 28px;
  margin: 50px 0 30px;
  position: relative;
  padding-bottom: 15px;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 60px;
  height: 3px;
  background: #2b64a7;
  transform: translateX(-50%);
}

/* 专利统计样式 */
.patent-stats-section {
  margin: 50px auto;
  max-width: 1200px;
  padding: 20px;
}

.stats-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.stat-item {
  width: 220px;
  height: 220px;
  background: linear-gradient(135deg, #2b64a7, #3a83d9);
  color: white;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
  text-align: center;
  box-shadow: 0 10px 20px rgba(43, 100, 167, 0.15);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.3);
}

.stat-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('static/picture/circuit-pattern.png') center/cover;
  opacity: 0.1;
  z-index: 1;
}

.stat-number {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 5px;
  position: relative;
  z-index: 2;
}

.stat-label {
  font-size: 18px;
  position: relative;
  z-index: 2;
}

.stat-icon {
  width: 50px;
  height: 50px;
  fill: white;
  opacity: 0.8;
  margin-top: 15px;
  position: relative;
  z-index: 2;
}

/* 核心专利领域样式 */
.patent-fields-section {
  margin: 70px auto;
  max-width: 1200px;
  padding: 20px;
}

.fields-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.field-item {
  width: calc(33% - 30px);
  min-width: 300px;
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.field-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.15);
}

.field-item:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #2b64a7, #5b94d7);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.field-item:hover:after {
  transform: scaleX(1);
}

.field-icon-wrapper {
  width: 60px;
  height: 60px;
  background: rgba(43, 100, 167, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.field-icon {
  width: 30px;
  height: 30px;
  fill: #2b64a7;
}

.field-item h4 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #2b64a7;
  font-weight: 600;
}

.field-item p {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
}

/* 专利证书展示样式 */
.patent-certificates-section {
  margin: 70px auto;
  max-width: 1200px;
  padding: 20px;
  position: relative;
}

/* 证书滚动容器 */
.certificates-scroll-wrapper {
  position: relative;
  margin: 50px 0;
  overflow: hidden;
}

/* 证书轨道容器 */
.certificates-track-container {
  overflow: hidden;
  padding: 10px 0;
  margin: 0 60px;
}

/* 证书容器 - 修改为横向滚动 */
.certificates-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 30px;
  transition: transform 0.3s ease;
}

.certificate-item {
  flex: 0 0 300px;
  min-width: 300px;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.certificate-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.15);
}

/* 导航按钮样式 */
.certificate-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #2B64AD;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.certificate-nav:hover {
  background: rgba(255, 128, 0, 1);
  box-shadow: 0 6px 15px rgba(255, 128, 0, 0.2);
}

.certificate-nav-prev {
  left: 5px;
}

.certificate-nav-next {
  right: 5px;
}

.certificate-nav .nav-icon {
  width: 24px;
  height: 24px;
  fill: white;
}

.certificate-image {
  height: 280px;
  position: relative;
  overflow: hidden;
}

.certificate-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.certificate-item:hover .certificate-image img {
  transform: scale(1.1);
}

.certificate-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(43, 100, 167, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.certificate-item:hover .certificate-overlay {
  opacity: 1;
}

.certificate-detail {
  text-align: center;
  padding: 20px;
  color: white;
}

.certificate-detail h5 {
  font-size: 18px;
  margin-bottom: 10px;
}

.certificate-detail p {
  font-size: 14px;
  margin: 5px 0;
}

.certificate-info {
  padding: 20px;
  text-align: center;
}

.certificate-info h4 {
  font-size: 16px;
  margin-bottom: 5px;
  color: #333;
  font-weight: 600;
}

.certificate-number {
  font-size: 13px;
  color: #777;
}

/* 技术创新时间线新样式 */
.innovation-timeline-section {
  margin: 70px auto;
  max-width: 1200px;
  padding: 0 20px;
  position: relative;
}

/* 时间轴容器 */
.timeline-container-wrapper {
  position: relative;
  margin: 50px 0;
  overflow: hidden;
}

/* 时间轴滚动容器 */
.timeline-scroll-container {
  overflow: hidden;
  padding: 40px 0;
  margin: 0 60px;
}

/* 时间轴轨道 */
.timeline-track {
  position: relative;
  min-height: 350px;
}

/* 时间线 */
.timeline-line {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgba(255,128,0,0.1), rgba(255,128,0,0.7), rgba(255,128,0,0.1));
  z-index: 1;
}

/* 时间点容器 */
.timeline-points {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  z-index: 2;
  padding-top: 30px;
}

/* 单个时间点 */
.timeline-point {
  position: relative;
  flex: 0 0 250px;
  margin-right: 80px;
  transition: all 0.5s ease;
  opacity: 0.7;
  transform: translateY(20px);
  cursor: pointer;
  text-align: center;
}

.timeline-point:hover {
  opacity: 1;
  transform: translateY(0);
}

.timeline-point.active {
  opacity: 1;
  transform: translateY(0);
}

/* 时间点脉冲动画 */
.point-pulse {
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background: #2b64a7;
  border-radius: 50%;
  z-index: 3;
  box-shadow: 0 0 10px rgba(43, 100, 167, 0.8);
}

.point-pulse:before,
.point-pulse:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(43, 100, 167, 0.8);
  transform: translate(-50%, -50%);
  animation: pulse 3s infinite;
}

.point-pulse:after {
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  70% {
    width: 40px;
    height: 40px;
    opacity: 0.4;
  }
  100% {
    width: 50px;
    height: 50px;
    opacity: 0;
  }
}

/* 年份标签 */
.point-year {
  position: absolute;
  top: 8px; /* 将标签位置从15px提高到8px */
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 700;
  color: #2b64a7;
  background: rgba(255, 255, 255, 0.9);
  padding: 5px 15px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(43, 100, 167, 0.2);
  z-index: 4;
  transition: all 0.3s ease;
}

.timeline-point:hover .point-year {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 4px 12px rgba(43, 100, 167, 0.3);
}

/* 时间点内容 */
.point-content {
  background: white;
  border-radius: 10px;
  padding: 15px;
  margin-top: 120px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.timeline-point:hover .point-content {
  box-shadow: 0 8px 25px rgba(43, 100, 167, 0.15);
  transform: translateY(-5px);
}

/* 时间点图片容器 */
.point-image-container {
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 15px;
}

.point-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.timeline-point:hover .point-image {
  transform: scale(1.1);
}

/* 图片悬停覆盖层 */
.point-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(43, 100, 167, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.point-overlay span {
  color: white;
  font-weight: 600;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
}

.timeline-point:hover .point-overlay {
  opacity: 1;
}

/* 内容文本样式 */
.point-content h4 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #2b64a7;
  transition: all 0.3s ease;
}

.point-content p {
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

/* 导航按钮 */
.timeline-nav {
  position: absolute;
  top: 50%;
  width: 46px;
  height: 46px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-50%);
  transition: all 0.3s ease;
}

.timeline-nav:hover {
  background: #2b64a7;
  box-shadow: 0 6px 18px rgba(43, 100, 167, 0.4);
  transform: translateY(-50%) scale(1.05);
}

.timeline-nav svg {
  width: 28px;
  height: 28px;
  fill: #555;
  transition: all 0.3s ease;
}

.timeline-nav:hover svg {
  fill: white;
}

.timeline-nav-prev {
  left: 0;
}

.timeline-nav-next {
  right: 0;
}

/* 弹窗样式 */
.timeline-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
}

.timeline-modal.active {
  display: block;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
    width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  z-index: 1001;
}

.modal-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 80%;
  max-width: 900px;
  max-height: 80vh;
  background: white;
  border-radius: 15px;
  z-index: 1002;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.timeline-modal.active .modal-container {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 28px;
  color: #333;
  cursor: pointer;
  z-index: 1003;
  transition: all 0.3s ease;
}

.modal-close:hover {
  color: rgba(255, 128, 0, 1);
  transform: rotate(90deg);
}

.modal-content {
  overflow-y: auto;
  max-height: 80vh;
}

.modal-header {
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 60px; /* 为关闭按钮留出空间 */
}

.modal-title {
    font-size: 24px;
  color: rgba(255, 128, 0, 1);
  margin: 0;
  max-width: 70%; /* 限制标题宽度 */
}

.modal-year {
  font-size: 18px;
  font-weight: 700;
  color: white;
  background: rgba(255, 128, 0, 1);
  padding: 5px 15px;
  border-radius: 20px;
  margin-left: 15px; /* 增加与标题的间距 */
  flex-shrink: 0; /* 防止年份标签被压缩 */
}

.modal-body {
  padding: 30px;
}

/* 简化的图片展示样式 */
.modal-images {
  margin-bottom: 30px;
}

.modal-image-single {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.modal-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease, opacity 0.3s ease;
  opacity: 1;
}

.image-switcher {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 128, 0, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.image-switcher:hover {
  background: rgba(255, 128, 0, 1);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.image-switcher span {
  font-size: 15px;
  font-weight: 600;
  margin-right: 5px;
}

.switcher-icon {
  width: 18px;
  height: 18px;
  fill: white;
}

/* 模态框描述和详情 */
.modal-description {
  font-size: 16px;
  line-height: 1.8;
  color: #555;
  margin-bottom: 30px;
}

.modal-details {
  background: #f9f9f9;
  border-radius: 10px;
  padding: 20px;
}

.detail-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.detail-list li {
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  font-size: 15px;
  display: flex;
  align-items: flex-start;
}

.detail-list li:last-child {
  border-bottom: none;
}

.detail-list li:before {
  content: '\2022';
  color: rgba(255, 128, 0, 1);
  font-weight: bold;
  display: inline-block;
  width: 1em;
  margin-right: 10px;
}

/* 子项样式 */
.detail-list li.sub-detail-item {
  padding-top: 5px;
  border-bottom: none;
  padding-left: 0;
  margin-top: 5px;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .timeline-point {
    flex: 0 0 200px;
    margin-right: 60px;
  }
}

@media (max-width: 768px) {
  .timeline-point {
    flex: 0 0 180px;
    margin-right: 50px;
  }
  
  .modal-container {
    width: 90%;
  }
  
  .modal-image-carousel {
    height: 300px;
  }
}

@media (max-width: 576px) {
  .timeline-scroll-container {
    margin: 0 30px;
  }
  
  .timeline-point {
    flex: 0 0 160px;
    margin-right: 40px;
  }
  
  .point-image-container {
    height: 120px;
  }
  
  .modal-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .modal-year {
    margin-top: 10px;
  }
  
  .modal-image-carousel {
    height: 200px;
  }
}

/* 背景图片样式 */
.sjimg.casebanner-bg {
  background-image: url(static/images/casebanner.jpg);
}

/* 强调激活的时间点 */
.timeline-point.active .point-pulse {
  box-shadow: 0 0 15px rgba(255, 128, 0, 0.9), 0 0 30px rgba(255, 128, 0, 0.5);
  background: rgba(255, 128, 0, 1);
}

.timeline-point.active .point-pulse:before,
.timeline-point.active .point-pulse:after {
  background: rgba(255, 128, 0, 0.8);
}

.timeline-point.active .point-year {
  background: rgba(255, 128, 0, 1);
  color: white;
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 128, 0, 0.5);
}

.timeline-point.active .point-content {
  box-shadow: 0 8px 25px rgba(255, 128, 0, 0.25);
  transform: translateY(-5px);
}

/* 新增加点击时间点激活效果 */
.timeline-point.active {
  opacity: 1;
  transform: translateY(0);
}
</style>

<script>
// 时间轴数据 - 每个时间点的详细信息
const timelineData = {
  '2018': {
    title: '聚算云成立',
    description: '团队开始研发分布式算力调度系统的核心框架，致力于打造面向未来的算力共享生态系统。创始团队由来自知名互联网公司和科研机构的专家组成，融合了分布式系统、云计算与人工智能等多个领域的专业知识。',
    images: [
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg'
    ],
    details: [
      '组建核心研发团队，启动算力调度平台研发',
      '完成算力资源共享系统架构设计',
      '获得天使轮融资500万元<br>投资方：ABC创投、DEF基金',
      '成立北京研发中心，初步搭建开发环境'
    ]
  },
  '2019': {
    title: '首批专利申请',
    description: '完成5项核心专利申请，涵盖资源调度与优化算法。这些专利包括了创新性的分布式计算资源动态调度方法、异构计算环境下的任务分配策略以及基于深度学习的计算负载预测技术，为公司的技术壁垒奠定了坚实基础。',
    images: [
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg'
    ],
    details: [
      '申请"分布式资源智能调度系统"专利',
      '申请"基于机器学习的计算负载预测方法"专利',
      '申请"异构计算环境下的任务均衡分配系统"专利',
      '开发调度平台1.0版本原型系统',
      '获得A轮融资2000万元'
    ]
  },
  '2020': {
    title: '边缘计算技术突破',
    description: '研发边缘计算加速技术，获得8项相关专利授权。团队突破了边缘设备低功耗高性能计算的技术难题，实现了云端与边缘设备之间的智能协同计算，特别适合物联网和智能穿戴设备等场景，极大扩展了算力共享网络的应用范围。',
    images: [
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg'
    ],
    details: [
      '发布边缘计算加速引擎EdgeBoost',
      '获得"低延迟边缘资源调度系统"等8项专利授权',
      '与多家IoT设备厂商达成技术合作',
      '启动边缘智能研究实验室',
      '获得B轮融资1亿元'
    ]
  },
  '2021': {
    title: 'AI加速技术发布',
    description: '发布AI模型训练加速技术，性能提升达350%。这一技术通过创新的分布式训练算法和资源调度策略，显著降低了企业在AI大模型训练方面的成本，同时提高了训练效率。该技术已成功应用于多家AI公司和研究机构的生产环境。',
    images: [
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg'
    ],
    details: [
      '发布AI训练加速框架AIBoost',
      '开发完成分布式大模型训练系统',
      '建立首个算力共享商业合作案例',
      '员工规模扩大至100人',
      '上海研发中心成立'
    ]
  },
  '2022': {
    title: '国际专利布局',
    description: '完成PCT国际专利申请15项，覆盖北美、欧洲与亚太地区。公司加快了全球化技术布局步伐，保护核心技术创新成果，同时开始拓展国际市场，与多家国际科技企业建立了战略合作关系，算力共享理念逐渐获得全球市场认可。',
    images: [
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg'
    ],
    details: [
      '完成15项PCT国际专利申请',
      '与3家美国科技公司达成技术合作',
      '启动海外市场拓展计划',
      '获得C轮融资3亿元',
      '成立硅谷研发中心'
    ]
  },
  '2023': {
    title: '行业标准制定',
    description: '参与制定3项算力网络国家行业标准，成为技术领导者。公司积极参与算力网络相关国家标准和行业规范的制定工作，推动了算力共享行业的规范化发展，同时进一步巩固了公司在行业中的技术领导地位，为未来的发展奠定了坚实基础。',
    images: [
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg',
      'static/picture/solution1-tu2.jpg'
    ],
    details: [
      '参与制定《算力网络资源调度技术规范》国家标准',
      '参与制定《边缘计算安全要求》行业标准',
      '发布算力共享平台2.0版本',
      '服务客户突破200家',
      '完成D轮融资5亿元'
    ]
  }
};

document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const timelineTrack = document.querySelector('.timeline-track');
  const timelinePoints = document.querySelector('.timeline-points');
  const timelineNavPrev = document.querySelector('.timeline-nav-prev');
  const timelineNavNext = document.querySelector('.timeline-nav-next');
  const modal = document.querySelector('.timeline-modal');
  const modalClose = document.querySelector('.modal-close');
  const modalOverlay = document.querySelector('.modal-overlay');
  const modalTitle = document.querySelector('.modal-title');
  const modalYear = document.querySelector('.modal-year');
  const modalDescription = document.querySelector('.modal-description');
  const modalDetailList = document.querySelector('.detail-list');
  
  let currentSlide = 0;
  let totalSlides = 0;
  let scrollPosition = 0;
  const pointWidth = 330; // 时间点宽度 + 右边距
  let currentActivePointIndex = 0; // 当前激活的时间点索引
  let allTimelinePoints = []; // 所有时间点
  
  // 初始化时间轴点击事件
  function initTimelineEvents() {
    const points = document.querySelectorAll('.timeline-point');
    allTimelinePoints = Array.from(points); // 存储所有时间点的引用
    
    points.forEach((point, index) => {
      // 点击时间点
      point.addEventListener('click', function() {
        // 更新当前激活节点的索引
        currentActivePointIndex = index;
        
        // 移除所有节点的active类
        points.forEach(p => p.classList.remove('active'));
        
        // 为当前点击的节点添加active类
        this.classList.add('active');
        
        // 打开详情模态框
        const year = this.getAttribute('data-year');
        openModal(year);
      });
      
      // 点击查看详情按钮
      const overlay = point.querySelector('.point-overlay');
      overlay.addEventListener('click', function(e) {
        e.stopPropagation();
        
        // 更新当前激活节点的索引
        currentActivePointIndex = index;
        
        // 移除所有节点的active类
        points.forEach(p => p.classList.remove('active'));
        
        // 为当前点击的节点的父节点添加active类
        point.classList.add('active');
        
        const year = point.getAttribute('data-year');
        openModal(year);
      });
      
      // 默认激活第一个点
      if (index === 0) {
        point.classList.add('active');
        currentActivePointIndex = 0;
      }
    });
  }
  
  // 打开模态框
  function openModal(year) {
    const data = timelineData[year];
    
    // 设置模态框内容
    modalTitle.textContent = data.title;
    modalYear.textContent = year;
    modalDescription.textContent = data.description;
    
    // 清空并填充详情列表
    modalDetailList.innerHTML = '';
    data.details.forEach(detail => {
      // 检查是否包含换行符
      if (detail.includes('<br>')) {
        // 如果包含换行符，将内容分割成多行
        const lines = detail.split('<br>');
        const li = document.createElement('li');
        
        // 第一行直接添加到li中
        li.innerHTML = lines[0];
        modalDetailList.appendChild(li);
        
        // 后续行为每行创建新的li元素
        for (let i = 1; i < lines.length; i++) {
          const subLi = document.createElement('li');
          subLi.innerHTML = lines[i];
          // 添加class以区分子项
          subLi.classList.add('sub-detail-item');
          modalDetailList.appendChild(subLi);
        }
          } else {
        // 不包含换行符，按照原来的方式处理
        const li = document.createElement('li');
        li.innerHTML = detail;
        modalDetailList.appendChild(li);
      }
    });
    
    // 设置第一张图片
    const mainImage = document.querySelector('.modal-main-image');
    mainImage.src = data.images[0];
    mainImage.alt = data.title;
    
    // 设置当前图片索引
    currentSlide = 0;
    totalSlides = data.images.length;
    
    // 显示模态框
    modal.classList.add('active');
    document.body.style.overflow = 'hidden'; // 防止背景滚动
  }
  
  // 切换到下一张图片
  function nextImage() {
    currentSlide = (currentSlide + 1) % totalSlides;
    const mainImage = document.querySelector('.modal-main-image');
    const data = timelineData[modalYear.textContent];
    
    // 添加淡出效果
    mainImage.style.opacity = 0;
    
    setTimeout(() => {
      mainImage.src = data.images[currentSlide];
      // 添加淡入效果
      mainImage.style.opacity = 1;
    }, 300);
  }
  
  // 关闭模态框
  function closeModal() {
    modal.classList.remove('active');
    document.body.style.overflow = '';
  }
  
  // 轮播滑动到指定幻灯片
  function goToSlide(index) {
    if (index < 0) {
      index = totalSlides - 1;
    } else if (index >= totalSlides) {
      index = 0;
    }
    
    currentSlide = index;
    carouselContainer.style.transform = `translateX(-${currentSlide * 100}%)`;
    
    // 更新指示点
    const indicators = document.querySelectorAll('.carousel-indicator');
    indicators.forEach((indicator, i) => {
      if (i === currentSlide) {
        indicator.classList.add('active');
          } else {
        indicator.classList.remove('active');
      }
    });
  }
  
  // 切换激活的时间节点
  function changeActivePoint(direction) {
    // 移除当前激活的时间点
    allTimelinePoints[currentActivePointIndex].classList.remove('active');
    
    // 根据方向更新索引
    if (direction === 'prev') {
      currentActivePointIndex = (currentActivePointIndex - 1 + allTimelinePoints.length) % allTimelinePoints.length;
    } else {
      currentActivePointIndex = (currentActivePointIndex + 1) % allTimelinePoints.length;
    }
    
    // 激活新的时间点
    allTimelinePoints[currentActivePointIndex].classList.add('active');
    
    // 确保当前激活的点在可视区域内
    ensureActivePointVisible();
  }
  
  // 确保当前激活的点在可视区域内
  function ensureActivePointVisible() {
    const activePoint = allTimelinePoints[currentActivePointIndex];
    const activePointLeft = activePoint.offsetLeft;
    const timelineTrackWidth = timelineTrack.offsetWidth;
    
    // 如果当前激活点在可视区域左侧之外，需要向左滚动
    if (activePointLeft < scrollPosition) {
      scrollPosition = activePointLeft;
    } 
    // 如果当前激活点在可视区域右侧之外，需要向右滚动
    else if (activePointLeft + activePoint.offsetWidth > scrollPosition + timelineTrackWidth) {
      scrollPosition = activePointLeft + activePoint.offsetWidth - timelineTrackWidth;
    }
    
    // 应用滚动
    timelinePoints.style.transform = `translateX(-${scrollPosition}px)`;
  }
  
  // 时间轴导航
  function navigateTimeline(direction) {
    // 移除当前激活的时间点
    allTimelinePoints[currentActivePointIndex].classList.remove('active');
    
    // 根据方向更新索引
    if (direction === 'prev') {
      currentActivePointIndex = (currentActivePointIndex - 1 + allTimelinePoints.length) % allTimelinePoints.length;
    } else {
      currentActivePointIndex = (currentActivePointIndex + 1) % allTimelinePoints.length;
    }
    
    // 激活新的时间点
    allTimelinePoints[currentActivePointIndex].classList.add('active');
    
    // 获取当前激活节点的位置
    const activePoint = allTimelinePoints[currentActivePointIndex];
    const activePointLeft = activePoint.offsetLeft;
    const timelineTrackWidth = timelineTrack.offsetWidth;
    
    // 计算需要滚动的位置，使当前节点居中显示
    scrollPosition = Math.max(0, activePointLeft - (timelineTrackWidth / 2) + (activePoint.offsetWidth / 2));
    
    // 检查是否超出边界
    const totalWidth = timelinePoints.scrollWidth;
    const visibleWidth = timelineTrack.offsetWidth;
    scrollPosition = Math.min(totalWidth - visibleWidth, scrollPosition);
    
    // 应用滚动
    timelinePoints.style.transform = `translateX(-${scrollPosition}px)`;
  }
  
  // 监听事件
  timelineNavPrev.addEventListener('click', () => navigateTimeline('prev'));
  timelineNavNext.addEventListener('click', () => navigateTimeline('next'));
  modalClose.addEventListener('click', closeModal);
  modalOverlay.addEventListener('click', closeModal);
  
  // 键盘导航
  document.addEventListener('keydown', function(e) {
    if (modal.classList.contains('active')) {
      if (e.key === 'Escape') {
        closeModal();
      } else if (e.key === 'ArrowRight') {
        nextImage();
      }
    }
  });
  
  // 触摸滑动支持（简单实现）
  let touchStartX = 0;
  let touchEndX = 0;
  // 添加新的变量
  let isDragging = false;
  let startPosX = 0;
  let startScrollPos = 0;
  
  // 触摸开始事件 - 更新为完整的触摸事件支持
  timelineTrack.addEventListener('touchstart', e => {
    touchStartX = e.changedTouches[0].screenX;
    startPosX = e.changedTouches[0].clientX;
    startScrollPos = scrollPosition;
    // 不阻止默认行为，保留原生滚动和点击
  });
  
  // 触摸移动事件 - 添加滑动功能
  timelineTrack.addEventListener('touchmove', e => {
    if (e.cancelable) {
      e.preventDefault(); // 防止页面滚动
    }
    const currentX = e.changedTouches[0].clientX;
    const diff = startPosX - currentX;
    
    // 计算新的滚动位置
    const newScrollPosition = startScrollPos + diff;
    
    // 检查边界
    const totalWidth = timelinePoints.scrollWidth;
    const visibleWidth = timelineTrack.offsetWidth;
    const maxScroll = Math.max(0, totalWidth - visibleWidth);
    
    // 应用滚动，确保在边界内
    scrollPosition = Math.max(0, Math.min(maxScroll, newScrollPosition));
    timelinePoints.style.transform = `translateX(-${scrollPosition}px)`;
  });
  
  timelineTrack.addEventListener('touchend', e => {
    touchEndX = e.changedTouches[0].screenX;
    // 如果滑动距离很小，认为是点击而不是滑动
    if (Math.abs(touchEndX - touchStartX) < 10) {
      return;
    }
    handleSwipe();
  });
  
  // 添加鼠标事件处理
  timelineTrack.addEventListener('mousedown', e => {
    isDragging = true;
    startPosX = e.clientX;
    startScrollPos = scrollPosition;
    // 改变鼠标指针样式
    timelineTrack.style.cursor = 'grabbing';
    e.preventDefault(); // 防止选择文本
  });
  
  document.addEventListener('mousemove', e => {
    if (!isDragging) return;
    
    const currentX = e.clientX;
    const diff = startPosX - currentX;
    
    // 计算新的滚动位置
    const newScrollPosition = startScrollPos + diff;
    
    // 检查边界
    const totalWidth = timelinePoints.scrollWidth;
    const visibleWidth = timelineTrack.offsetWidth;
    const maxScroll = Math.max(0, totalWidth - visibleWidth);
    
    // 应用滚动，确保在边界内
    scrollPosition = Math.max(0, Math.min(maxScroll, newScrollPosition));
    timelinePoints.style.transform = `translateX(-${scrollPosition}px)`;
  });
  
  document.addEventListener('mouseup', () => {
    if (isDragging) {
      isDragging = false;
      timelineTrack.style.cursor = 'grab';
    }
  });
  
  // 防止拖动过程中选择文本
  timelineTrack.addEventListener('selectstart', e => {
    if (isDragging) {
      e.preventDefault();
    }
  });
  
  function handleSwipe() {
    if (touchEndX < touchStartX) {
      navigateTimeline('next');
    } else if (touchEndX > touchStartX) {
      navigateTimeline('prev');
    }
  }
  
  // 初始化所有事件
  initTimelineEvents();
  
  // 为图片切换按钮添加点击事件
  const imageSwitcher = document.querySelector('.image-switcher');
  if (imageSwitcher) {
    imageSwitcher.addEventListener('click', nextImage);
  }
  
  // 如果timelineTrack存在，设置鼠标样式
  if (timelineTrack) {
    timelineTrack.style.cursor = 'grab';
  }
  
  // 初始化专利证书展示的滚动功能
  initCertificateScroll();
});

// 初始化专利证书展示的滚动功能
function initCertificateScroll() {
  const certificatesContainer = document.querySelector('.certificates-container');
  const prevBtn = document.querySelector('.certificate-nav-prev');
  const nextBtn = document.querySelector('.certificate-nav-next');
  const certificateItems = document.querySelectorAll('.certificate-item');
  
  if (!certificatesContainer || !prevBtn || !nextBtn || certificateItems.length === 0) return;
  
  let certScrollPosition = 0;
  const itemWidth = certificateItems[0].offsetWidth + 30; // 项目宽度 + 间距
  const containerWidth = certificatesContainer.scrollWidth;
  const visibleWidth = document.querySelector('.certificates-track-container').offsetWidth;
  const maxScroll = Math.max(0, containerWidth - visibleWidth);
  
  // 滚动到指定位置
  function scrollCertificatesTo(position) {
    certScrollPosition = Math.max(0, Math.min(maxScroll, position));
    certificatesContainer.style.transform = `translateX(-${certScrollPosition}px)`;
  }
  
  // 向前滚动
  prevBtn.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡
    scrollCertificatesTo(certScrollPosition - itemWidth);
  });
  
  // 向后滚动
  nextBtn.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡
    scrollCertificatesTo(certScrollPosition + itemWidth);
  });
  
  // 拖动变量
  let certIsDragging = false;
  let certStartPosX = 0;
  let certStartScrollPos = 0;
  let certLastMoveTime = 0;
  let certLastMoveX = 0;
  
  // 鼠标事件
  certificatesContainer.addEventListener('mousedown', (e) => {
    certIsDragging = true;
    certStartPosX = e.clientX;
    certLastMoveX = certStartPosX;
    certLastMoveTime = Date.now();
    certStartScrollPos = certScrollPosition;
    certificatesContainer.style.cursor = 'grabbing';
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡
  });
  
  // 使用命名空间处理document事件，避免冲突
  const handleCertMouseMove = function(e) {
    if (!certIsDragging) return;
    
    const currentX = e.clientX;
    const currentTime = Date.now();
    
    // 记录最后移动的位置和时间，用于计算惯性
    if (currentTime - certLastMoveTime > 10) { // 每10ms记录一次
      certLastMoveX = currentX;
      certLastMoveTime = currentTime;
    }
    
    const diff = certStartPosX - currentX;
    scrollCertificatesTo(certStartScrollPos + diff);
    e.preventDefault();
  };
  
  const handleCertMouseUp = function(e) {
    if (!certIsDragging) return;
    
    certIsDragging = false;
    certificatesContainer.style.cursor = 'grab';
    
    // 计算惯性滑动
    const currentTime = Date.now();
    const moveTime = currentTime - certLastMoveTime;
    
    // 如果最后一次移动时间在100ms内，添加惯性
    if (moveTime < 100) {
      const moveDistance = certStartPosX - certLastMoveX;
      const velocity = moveDistance / moveTime; // 速度 = 距离/时间
      
      // 根据速度大小设置惯性滑动
      if (Math.abs(velocity) > 0.5) {
        const inertiaDistance = velocity * 100; // 惯性距离
        scrollCertificatesTo(certScrollPosition + inertiaDistance);
      }
    }
  };
  
  document.addEventListener('mousemove', handleCertMouseMove);
  document.addEventListener('mouseup', handleCertMouseUp);
  
  // 触摸事件
  let certTouchStartX = 0;
  let certLastTouchMoveTime = 0;
  let certLastTouchX = 0;
  
  certificatesContainer.addEventListener('touchstart', (e) => {
    certStartPosX = e.touches[0].clientX;
    certTouchStartX = certStartPosX;
    certLastTouchX = certStartPosX;
    certLastTouchMoveTime = Date.now();
    certStartScrollPos = certScrollPosition;
    e.stopPropagation(); // 阻止事件冒泡，防止触发其他事件
  });
  
  certificatesContainer.addEventListener('touchmove', (e) => {
    e.stopPropagation(); // 阻止事件冒泡
    const currentX = e.touches[0].clientX;
    const currentTime = Date.now();
    
    // 记录最后移动的位置和时间，用于计算惯性
    if (currentTime - certLastTouchMoveTime > 10) { // 每10ms记录一次
      certLastTouchX = currentX;
      certLastTouchMoveTime = currentTime;
    }
    
    const diff = certStartPosX - currentX;
    
    // 如果滑动幅度足够大，阻止默认行为（页面滚动）
    if (Math.abs(diff) > 10 && e.cancelable) {
      e.preventDefault();
    }
    
    scrollCertificatesTo(certStartScrollPos + diff);
  });
  
  certificatesContainer.addEventListener('touchend', (e) => {
    e.stopPropagation(); // 阻止事件冒泡
    const touchEndX = e.changedTouches[0].clientX;
    const diffX = certTouchStartX - touchEndX;
    
    // 计算惯性滑动
    const currentTime = Date.now();
    const moveTime = currentTime - certLastTouchMoveTime;
    
    // 如果最后一次移动时间在100ms内，添加惯性
    if (moveTime < 100) {
      const moveDistance = certTouchStartX - certLastTouchX;
      const velocity = moveDistance / moveTime; // 速度 = 距离/时间
      
      // 根据速度大小设置惯性滑动
      if (Math.abs(velocity) > 0.5) {
        const inertiaDistance = velocity * 150; // 惯性距离
        scrollCertificatesTo(certScrollPosition + inertiaDistance);
        return;
      }
    }
    
    // 如果没有惯性滑动，检查快速滑动
    if (Math.abs(diffX) > 50) {
      if (diffX > 0) {
        // 向左滑动，显示下一个
        scrollCertificatesTo(certScrollPosition + itemWidth);
      } else {
        // 向右滑动，显示上一个
        scrollCertificatesTo(certScrollPosition - itemWidth);
      }
    }
  });
  
  // 当点击证书时的处理
  certificateItems.forEach(item => {
    item.addEventListener('click', (e) => {
      // 如果正在拖动或最近有移动，则不触发点击事件
      if (certIsDragging || (Date.now() - certLastMoveTime < 100)) {
        e.preventDefault();
        e.stopPropagation();
      }
    });
  });
  
  // 初始化时设置鼠标样式
  certificatesContainer.style.cursor = 'grab';
  
  // 添加窗口大小改变时重新计算布局的处理
  window.addEventListener('resize', () => {
    // 重新计算滚动限制
    const newVisibleWidth = document.querySelector('.certificates-track-container').offsetWidth;
    const newMaxScroll = Math.max(0, containerWidth - newVisibleWidth);
    // 调整当前滚动位置
    scrollCertificatesTo(Math.min(certScrollPosition, newMaxScroll));
  });
}
</script>

</body>
</html>




