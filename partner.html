<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8" />
<meta name="format-detection" content="telephone=no" />
<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta name="HandheldFriendly" content="true" />
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
<!--[if lt IE 9]>
    <script type="text/javascript" src="static/js/css3-mediaqueries.js">
    </script>
    <![endif]-->
<meta name="Author" content="易思信网页工作室 www.eczone.net ">
<!--
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
           网站设计制作 by 易思信网页工作室 www.eczone.net   
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
    -->
<title>聚算云技术白皮书 - 算力共享实践</title>
<meta name="keywords" content='算力调度平台,云服务解决方案,边缘计算服务,AI算力服务,算力资源池,聚算云' />
<meta name="description" content='聚算云致力于用共享经济重塑算力分配规则，建立一个连接算力供应方与需求方的智能调度平台，实现算力资源的自由流动和高效利用。' />
<link rel="shortcut icon" href="favicon.ico" />
<link rel="stylesheet" href="static/css/animate.css" type="text/css" >
<link rel="stylesheet" href="static/css/common.css" type="text/css" >
<link rel="stylesheet" href="static/css/style.css" type="text/css" >
<script type="text/javascript" src="static/js/jquery.min.js"></script> 
</head>
<body>
<section class="wrapper">
  <header class="header"> <a href="/" class="logo"> <img class="hide" src="static/picture/logo.png" alt="" /> <img class="show" src="static/picture/logo2.png" alt="" /> </a>
    <ul class="navs">
      <li class=""><a href="/">首页</a></li>
      <li ><a href="page-abouts.html">关于我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业使命与愿景<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业文化<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">团队介绍<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">投资者关系<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="products-services.html">产品与服务<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="solution-cases.html">解决方案<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="solution-cases.html#gaming-cafe">电竞云网吧<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#ai-glasses">AI眼镜<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#digital-transformation">企业数字化转型<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#hpc-research">科研机构高性能计算<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#big-data-analytics">数据分析与大数据处理<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#media-rendering">媒体内容制作与渲染<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="hyyy-hyyy.html">成功案例<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">互联网行业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">人工智能<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">制造业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">金融服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教育与科研<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">政府与公共服务<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">技术与创新<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">技术白皮书<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">研发团队<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">专利与知识产权<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">合作与生态<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">合作伙伴<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">生态体系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">合作模式<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">资源中心<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">产品手册<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教程与指南<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">行业洞察<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">算力知识库<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li><a href="contact-contacts.html">联系我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">销售咨询<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">技术支持<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">媒体联系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">办公地点<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
    </ul>
    <div class="header-menu">
      <div class="header-search"> <span class="iconfont">&nbsp;</span> </div>
      <div class="menu-btn">
        <p>MENU</p>
        <div class="menubtn"> <span></span> </div>
      </div>
      <div class="menu-flex">
        <div class="menu-bg"></div>
        <div class="menu-right">
          <ul class="menu-list">
            <li><a href="/">首页</a></li>
            <li ><a href="page-abouts.html">关于我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="page2-1751-1753.html">常问问题</a></li>
                <li><a href="solution-1751-1832.html">服务客户</a></li>
              </ol>
            </li>
            <li ><a href="products-services.html">产品与服务<em></em></a>
              <ol class="menu-leval">
                <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="solution-cases.html">解决方案<em></em></a>
              <ol class="menu-leval">
                <li><a href="solution-cases.html#gaming-cafe">电竞云网吧<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#ai-glasses">AI眼镜<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#digital-transformation">企业数字化转型<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#hpc-research">科研机构高性能计算<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#big-data-analytics">数据分析与大数据处理<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#media-rendering">媒体内容制作与渲染<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="hyyy-hyyy.html">成功案例<em></em></a>
              <ol class="menu-leval">
                <li><a href="hylist-1808-1814.html">化工</a></li>
                <li><a href="hylist-1808-1815.html">石化</a></li>
                <li><a href="hylist-1808-1816.html">制药</a></li>
                <li><a href="hylist-1808-1817.html">食品饮料</a></li>
                <li><a href="hylist-1808-1818.html">其它</a></li>
              </ol>
            </li>
            <li ><a href="#">技术与创新<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">技术白皮书<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">研发团队<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">专利与知识产权<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">合作与生态<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">合作伙伴<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">生态体系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">合作模式<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">资源中心<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">产品手册<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教程与指南<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">行业洞察<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">算力知识库<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="contact-contacts.html">联系我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">销售咨询<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">技术支持<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">媒体联系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">办公地点<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
          </ul>
          <div class="menu-ip-down">
            <div class="online-shopp"> </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class="main-content-wrap">
    <div class="content-wrap">
      <div class="head-search">
        <form action="search-search.html" method="post">
          <input class="search-ipt" type="text" name="query" placeholder="输入关键词...">
          <input class="search-btn" type="submit" name="submit" value="搜索" title="搜索" aria-label="搜索">
        </form>
      </div>
    </div>
  </div>
  <div class="mobile-body-mask"></div>
  <div class="pbanner">
    <div class="product-hide">
      <figure><img class="pc" src="static/picture/casebanner.jpg" alt="聚算云技术白皮书"/>
        <div class="sjimg" style="background-image:url(static/images/casebanner.jpg);"> </div>
      </figure>
    </div>
    <div class="series global-fix">
      <p class="article-block slidetop">Technical Whitepaper</p>
      <div class="series-title article-block slidetop detay1 syht"> <strong>算力共享实践 · 创新价值共赢</strong> </div>
    </div>
    <div class="sea-down defaul"> <i></i> <span>向下滑动</span> </div>
  </div>
  
  <!--pageTop-->
  <div class="pageTop">
    <div class="w1600 clearfix">
      <div class="pageNav">
        <ul class="navlist clearfix swiper-wrapper">
          <li class="on"><a href="#partners" ><span>合作伙伴</span></a></li>
          <li><a href="#ecosystem" ><span>生态体系</span></a></li>
          <li><a href="#cooperation" ><span>合作模式</span></a></li>
          <li><a href="#join-us" ><span>加入我们</span></a></li>
        </ul>
      </div>
    </div>
  </div>
  <!--pageTop end--> 
  
  <!--page-->
  <div class="page clearfix"> 
    <!--pageInfo-->
    <div class="pageInfo clearfix" id="partner-section">
      <div class="pageTit"><span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">Partners</span>
        <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"><span class="syht">核心合作伙伴</span></div>
      </div>
      <div class="partner-intro wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s">
        <p>聚算云与业界领先的科技公司、研究机构和行业用户紧密合作，共同打造开放、创新的算力生态系统。通过整合各方优势资源，我们致力于为客户提供更高效、更智能的算力服务解决方案。</p>
      </div>
      
      <!-- 核心合作伙伴 - 改为轮播形式 -->
      <div class="core-partners">
        <div class="partners-carousel-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="partners-carousel">
            <div class="partners-carousel-inner">
              <div class="core-partner-item">
                <div class="partner-logo">
                  <img src="static/picture/partner1.jpg" alt="合作伙伴标志" />
                  <div class="partner-overlay">
                    <div class="overlay-content">
                      <h4>云服务合作</h4>
                      <p>提供全球领先的云计算基础设施</p>
                    </div>
                  </div>
                </div>
                <div class="partner-info">
                  <h4>云科技</h4>
                  <span class="partner-type">技术合作伙伴</span>
                </div>
              </div>
              <div class="core-partner-item">
                <div class="partner-logo">
                  <img src="static/picture/partner2.jpg" alt="合作伙伴标志" />
                  <div class="partner-overlay">
                    <div class="overlay-content">
                      <h4>芯片技术合作</h4>
                      <p>提供高性能AI加速芯片解决方案</p>
                    </div>
                  </div>
                </div>
                <div class="partner-info">
                  <h4>智芯科技</h4>
                  <span class="partner-type">硬件合作伙伴</span>
                </div>
              </div>
              <div class="core-partner-item">
                <div class="partner-logo">
                  <img src="static/picture/partner3.jpg" alt="合作伙伴标志" />
                  <div class="partner-overlay">
                    <div class="overlay-content">
                      <h4>大规模分布式计算</h4>
                      <p>全球领先的分布式计算技术提供商</p>
                    </div>
                  </div>
                </div>
                <div class="partner-info">
                  <h4>分布科技</h4>
                  <span class="partner-type">技术合作伙伴</span>
                </div>
              </div>
              <div class="core-partner-item">
                <div class="partner-logo">
                  <img src="static/picture/solution1-tu2.jpg" alt="合作伙伴标志" />
                  <div class="partner-overlay">
                    <div class="overlay-content">
                      <h4>AI算法技术</h4>
                      <p>领先的人工智能算法服务提供商</p>
                    </div>
                  </div>
                </div>
                <div class="partner-info">
                  <h4>智算科技</h4>
                  <span class="partner-type">AI技术伙伴</span>
                </div>
              </div>
              <div class="core-partner-item">
                <div class="partner-logo">
                  <img src="static/picture/solution1-tu2.jpg" alt="合作伙伴标志" />
                  <div class="partner-overlay">
                    <div class="overlay-content">
                      <h4>数据存储解决方案</h4>
                      <p>高性能存储系统服务提供商</p>
                    </div>
                  </div>
                </div>
                <div class="partner-info">
                  <h4>存储科技</h4>
                  <span class="partner-type">基础设施伙伴</span>
                </div>
              </div>
              <div class="core-partner-item">
                <div class="partner-logo">
                  <img src="static/picture/solution1-tu2.jpg" alt="合作伙伴标志" />
                  <div class="partner-overlay">
                    <div class="overlay-content">
                      <h4>网络架构优化</h4>
                      <p>专注于高性能网络架构设计与实现</p>
                    </div>
                  </div>
                </div>
                <div class="partner-info">
                  <h4>网联科技</h4>
                  <span class="partner-type">网络技术伙伴</span>
                </div>
              </div>
              <div class="core-partner-item">
                <div class="partner-logo">
                  <img src="static/picture/solution1-tu2.jpg" alt="合作伙伴标志" />
                  <div class="partner-overlay">
                    <div class="overlay-content">
                      <h4>安全防护技术</h4>
                      <p>数据安全与隐私保护解决方案专家</p>
                    </div>
                  </div>
                </div>
                <div class="partner-info">
                  <h4>安信科技</h4>
                  <span class="partner-type">安全技术伙伴</span>
                </div>
              </div>
            </div>
          </div>
          <button class="carousel-control carousel-prev" title="上一页" aria-label="上一页">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"></path>
            </svg>
          </button>
          <button class="carousel-control carousel-next" title="下一页" aria-label="下一页">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"></path>
            </svg>
          </button>
          <div class="carousel-indicators">
            <span class="indicator active"></span>
            <span class="indicator"></span>
            <span class="indicator"></span>
          </div>
        </div>
      </div>
      
      <!-- 合作优势 -->
      <div class="partnership-benefits">
        <div class="pageInfo clearfix">
      <div class="pageTit"><span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">advantages</span>
        <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"><span class="syht">合作优势</span></div>
      </div>
        <div class="benefits-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="benefit-item">
            <div class="benefit-icon">
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12,6A3,3 0 0,0 9,9A3,3 0 0,0 12,12A3,3 0 0,0 15,9A3,3 0 0,0 12,6M6,8.17A2.5,2.5 0 0,0 3.5,10.67A2.5,2.5 0 0,0 6,13.17C6.88,13.17 7.65,12.71 8.09,12.03C7.42,11.18 7,10.15 7,9C7,8.8 7,8.6 7.04,8.4C6.72,8.25 6.37,8.17 6,8.17M18,8.17C17.63,8.17 17.28,8.25 16.96,8.4C17,8.6 17,8.8 17,9C17,10.15 16.58,11.18 15.91,12.03C16.35,12.71 17.12,13.17 18,13.17A2.5,2.5 0 0,0 20.5,10.67A2.5,2.5 0 0,0 18,8.17M12,14C10,14 6,15 6,17V19H18V17C18,15 14,14 12,14M4.67,14.97C3,15.26 1,16.04 1,17.33V19H4V17C4,16.22 4.29,15.53 4.67,14.97M19.33,14.97C19.71,15.53 20,16.22 20,17V19H23V17.33C23,16.04 21,15.26 19.33,14.97Z"></path>
              </svg>
            </div>
            <h4>共建生态</h4>
            <p>携手打造开放、创新的算力生态系统，实现资源共享、优势互补</p>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.81,14.12L5.64,11.29L8.17,10.79C11.39,6.41 17.55,4.22 19.78,4.22C19.78,6.45 17.59,12.61 13.21,15.83L12.71,18.36L9.88,21.19L9.17,17.66C7.76,17.66 7.76,17.66 7.05,16.95C6.34,16.24 6.34,16.24 6.34,14.83L2.81,14.12M5.64,16.95L7.05,18.36L4.39,21.03H2.97V19.61L5.64,16.95M4.22,15.54L5.46,15.71L3,18.16V16.74L4.22,15.54M8.29,18.54L8.46,19.78L7.26,21H5.84L8.29,18.54M13,9.5A1.5,1.5 0 0,0 11.5,11A1.5,1.5 0 0,0 13,12.5A1.5,1.5 0 0,0 14.5,11A1.5,1.5 0 0,0 13,9.5Z"></path>
              </svg>
            </div>
            <h4>技术创新</h4>
            <p>联合研发前沿技术，共同突破算力调度与优化的技术瓶颈</p>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10Z"></path>
              </svg>
            </div>
            <h4>商业共赢</h4>
            <p>打造多方互利的价值网络，拓展市场空间，降低资源成本</p>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M15,12H16.5V16.25L19.36,17.94L18.61,19.16L15,17V12M16,9C16.69,9 17.37,9.1 18,9.29V4.7L15,5.86V9.07C15.33,9 15.66,9 16,9M23,16A7,7 0 0,1 16,23C13,23 10.4,21.08 9.42,18.4L8,17.9L2.66,19.97L2.5,20A0.5,0.5 0 0,1 2,19.5V4.38C2,4.15 2.15,3.97 2.36,3.9L8,2L14,4.1L19.34,2.03L19.5,2A0.5,0.5 0 0,1 20,2.5V10.25C21.81,11.5 23,13.62 23,16M9,16C9,13.21 10.63,10.8 13,9.67V5.87L9,4.47V16.13H9C9,16.09 9,16.04 9,16M16,11A5,5 0 0,0 11,16A5,5 0 0,0 16,21A5,5 0 0,0 21,16A5,5 0 0,0 16,11M4,5.46V17.31L7,16.15V4.45L4,5.46Z"></path>
              </svg>
            </div>
            <h4>行业赋能</h4>
            <p>整合行业资源，提供场景化解决方案，加速产业智能化升级</p>
          </div>
        </div>
      </div>
      
      <!-- 合作流程 -->
      <div class="partnership-process">
        <div class="pageTit"><span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">Process</span>
          <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"><span class="syht">合作流程</span></div>
        </div>
        <div class="process-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="process-item">
            <div class="process-number">01</div>
            <div class="process-info">
              <h4>需求对接</h4>
              <p>了解合作意向与需求，探讨合作可行性</p>
            </div>
          </div>
          <div class="process-arrow">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"></path>
            </svg>
          </div>
          <div class="process-item">
            <div class="process-number">02</div>
            <div class="process-info">
              <h4>方案制定</h4>
              <p>基于双方优势资源，设计合作方案</p>
            </div>
          </div>
          <div class="process-arrow">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"></path>
            </svg>
          </div>
          <div class="process-item">
            <div class="process-number">03</div>
            <div class="process-info">
              <h4>协议签署</h4>
              <p>明确合作内容、权责与目标</p>
            </div>
          </div>
          <div class="process-arrow">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"></path>
            </svg>
          </div>
          <div class="process-item">
            <div class="process-number">04</div>
            <div class="process-info">
              <h4>项目执行</h4>
              <p>实施合作计划，定期沟通与调整</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 合作邀请 - 修改底部间距 -->
      <div class="partnership-invitation">
        <div class="invitation-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s">
          <div class="invitation-content">
            <h3>诚邀合作 共赢未来</h3>
            <p>无论您是技术提供商、云服务商、硬件厂商还是行业用户，都欢迎与我们共同探索算力网络的无限可能</p>
            <a href="contact-contacts.html" class="btn-contact">联系我们</a>
          </div>
          <div class="invitation-bg"></div>
        </div>
      </div>
    </div>
    <!--End--> 
  </div>
  <!--page End--> 
  <!--footer--> 
  <!--footer-->
  <div class="footer clearfix"> 
    <!--footer-nav-->
    <div class="footer-nav">
      <div class="w1600 clearfix">
        <div class="left">
          <div class="leftBox clearfix">
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="solution-cases.html">蒸汽节能解决方案</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="solution-1805-1809.html">ESOS-蒸汽系统节能优化</a></li>
                    <li class="t2"><a href="solution-1805-1810.html">蒸汽疏水阀调查</a></li>
                    <li class="t2"><a href="solution-1805-1811.html">蒸汽系统诊断调查</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="advantage-ys.html">我们的优势</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="advantage-ys.html">一体式蒸汽工程解决方案</a></li>
                    <li class="t2"><a href="advantage-ys.html#adBox2">创新节能技术</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="page-abouts.html">关于我们</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="page-1751-1752.html">公司介绍</a></li>
                    <li class="t2"><a href="page2-1751-1753.html">常问问题</a></li>
                    <li class="t2"><a href="solution-1751-1832.html">服务客户</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="hyyy-hyyy.html">行业应用案例</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="hylist-1808-1814.html">化工</a></li>
                    <li class="t2"><a href="hylist-1808-1815.html">石化</a></li>
                    <li class="t2"><a href="hylist-1808-1816.html">制药</a></li>
                    <li class="t2"><a href="hylist-1808-1817.html">食品饮料</a></li>
                    <li class="t2"><a href="hylist-1808-1818.html">其它</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="rightBox clearfix">
            <table width="100%">
              <tbody>
                <tr>
                  <td><h1> 服务热线 </h1></td>
                </tr>
                <tr>
                  <td class="tel"><h1> <span>+86 13321109027</span> </h1>
                    <p> 诚挚为您服务 </p></td>
                </tr>
              </tbody>
            </table>
            <div class="bottom-share"> <span>关注我们：</span> <a class="iconfont email" href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">&#xe6db;</a> <a class="iconfont qq" href="http://wpa.qq.com/msgrd?v=3&uin=393202489&site=kingtin&menu=yes" target="_blank" rel="noopener noreferrer">&#xe60f;</a> <a class="iconfont weix"  rel="noopener noreferrer">&#xe660;<span class="img"><img src="static/picture/202203011551003232.jpg" alt="" /><br />
              </span></a> </div>
          </div>
        </div>
      </div>
      <div class="bottom-wz">
        <div class="w1600 clearfix">思德乐，提供卓越的蒸汽工程解决方案。</div>
      </div>
    </div>
    <!--footer-nav end-->
    <div class="bq clearfix">
      <div class="w1600 clearfix">
        <div class="bqname"> &copy;2021 佛山市思德乐能源科技有限公司 版权所有 <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">粤ICP备2022022663号</a> </div>
        <div class="beian"> 网站设计: <a href="http://www.king-tin.com/" target="_blank" rel="noopener noreferrer">KINGTIN</a><script charset="UTF-8" id="LA_COLLECT" src="static/js/js-sdk-pro.min.js"></script><script>LA.init({id: "Jh6tR8OE31b5wc6v",ck: "Jh6tR8OE31b5wc6v"})</script> 
        </div>
      </div>
    </div>
  </div>
  <!--End footer-->
  <div class="common-bott">
    <div class="back-top commontop iconfont">&#xe731;</div>
  </div>
</section>
<script type="text/javascript" src="static/js/wow.min2.js"></script> 
<script type="text/javascript" src="static/js/plugin.js"></script> 
<script type="text/javascript" src="static/js/page.js"></script> 
<script>
var scrolling = true;
var timer = null;
$(document).on('mousewheel DOMMouseScroll', function (e) {
        if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
        navigateUp();
        } else {
        if (e.pageY <= w_height && scrolling && !isMobile) {
        clearTimeout(timer);
        scrolling = false;
        timer = setTimeout(function () {
        jQuery("html,body").stop(true, true).animate({ scrollTop: w_height }, 1200, "easeOutQuint")
        }, 100)
        }
        }
});
function navigateUp() {
        scrolling = true;
}
</script>
<style>
.icon-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}
.icon-wrapper {
  text-align: center;
  margin: 0 15px;
}
.svg-icon {
  width: 64px;
  height: 64px;
  fill: #2b64a7;
  margin-bottom: 10px;
}
.icon-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}
.brand-core-box {
  display: flex;
  justify-content: space-around;
  margin: 30px 0;
  flex-wrap: wrap;
}

.brand-core-item {
  text-align: center;
  width: 200px;
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.brand-core-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(43, 100, 167, 0.2);
  background-color: rgba(43, 100, 167, 0.05);
}

.brand-core-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(43, 100, 167, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.4s ease;
}

.brand-core-item:hover:before {
  opacity: 1;
  transform: scale(2);
}

.brand-core-svg {
  width: 64px;
  height: 64px;
  margin-bottom: 15px;
  fill: #2b64a7;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 1;
}

.brand-core-item:hover .brand-core-svg {
  transform: rotate(15deg) scale(1.2);
  fill: #2b64a7;
}

.brand-core-title {
  color: #2b64a7;
  font-size: 18px;
  margin: 10px 0;
  font-weight: bold;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.brand-core-item:hover .brand-core-title {
  transform: scale(1.05);
}

.brand-core-text {
  color: #333;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.brand-core-item:hover .brand-core-text {
  color: #2b64a7;
}

/* 通用样式 */
.partner-intro {
  max-width: 1200px;
  margin: 30px auto;
  text-align: center;
  font-size: 18px;
  line-height: 1.8;
  color: #333;
  padding: 0 20px;
}

.section-title {
  text-align: center;
  color: #2b64a7;
  font-size: 28px;
  margin: 50px 0 30px;
  position: relative;
  padding-bottom: 15px;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 60px;
  height: 3px;
  background: #2b64a7;
  transform: translateX(-50%);
}

/* 核心合作伙伴样式 - 修改为轮播形式 */
.core-partners {
  margin: 50px auto 70px;
  max-width: 1200px;
  padding: 0 20px;
}

.partners-carousel-container {
  position: relative;
  padding: 0 60px;
}

.partners-carousel {
  overflow: hidden;
  position: relative;
  width: 100%;
}

.partners-carousel-inner {
  display: flex;
  transition: transform 0.5s ease;
}

.core-partner-item {
  flex: 0 0 calc(33.33% - 30px);
  min-width: 280px;
  margin: 0 15px;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.core-partner-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.15);
}

.partner-logo {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.partner-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.core-partner-item:hover .partner-logo img {
  transform: scale(1.1);
}

.partner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(43, 100, 167, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.core-partner-item:hover .partner-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  padding: 20px;
  color: white;
}

.overlay-content h4 {
  font-size: 20px;
  margin-bottom: 10px;
}

.overlay-content p {
  font-size: 14px;
}

.partner-info {
  padding: 20px;
}

.partner-info h4 {
  font-size: 18px;
  margin-bottom: 5px;
  color: #333;
}

.partner-type {
  display: inline-block;
  font-size: 14px;
  padding: 3px 10px;
  border-radius: 15px;
  color: white;
  background: #2b64a7;
}

/* 轮播控制按钮样式 */
.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  background: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  z-index: 10;
}

.carousel-control:hover {
  background: #2b64a7;
}

.carousel-control svg {
  width: 24px;
  height: 24px;
  fill: #2b64a7;
  transition: all 0.3s ease;
}

.carousel-control:hover svg {
  fill: white;
}

.carousel-prev {
  left: 0;
}

.carousel-next {
  right: 0;
}

/* 轮播指示器样式 */
.carousel-indicators {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 10px;
}

.indicator {
  width: 10px;
  height: 10px;
  background: #ddd;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #2b64a7;
  transform: scale(1.2);
}

/* 合作优势样式 */
.partnership-benefits {
  margin: 70px auto;
  max-width: 1200px;
  padding: 0 20px;
}

.benefits-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.benefit-item {
  width: calc(25% - 30px);
  min-width: 250px;
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.1);
}

.benefit-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #2b64a7, rgba(255, 128, 0, 1));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.benefit-item:hover:before {
  transform: scaleX(1);
}

.benefit-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  background: rgba(43, 100, 167, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.benefit-item:hover .benefit-icon {
  background: rgba(43, 100, 167, 0.2);
}

.benefit-icon svg {
  width: 40px;
  height: 40px;
  fill: #2b64a7;
}

.benefit-item h4 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.benefit-item p {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

/* 合作流程样式 */
.partnership-process {
  margin: 70px auto;
  max-width: 1200px;
  padding: 0 20px;
}

.process-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.process-item {
  width: 200px;
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  position: relative;
  z-index: 1;
}

.process-number {
  font-size: 36px;
  font-weight: 700;
  color: rgba(43, 100, 167, 0.15);
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.process-item:hover .process-number {
  color: rgba(255, 128, 0, 0.7);
}

.process-info h4 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
}

.process-info p {
  font-size: 14px;
  color: #666;
}

.process-arrow {
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.process-arrow svg {
  width: 30px;
  height: 30px;
  fill: #2b64a7;
}

/* 合作邀请样式 - 调整底部间距 */
.partnership-invitation {
  margin: 70px auto 100px;
  max-width: 1200px;
  padding: 0 20px;
}

.invitation-container {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  background: linear-gradient(135deg, #2b64a7, #1e4675);
  min-height: 250px;
  color: white;
  padding-bottom: 30px;
}

.invitation-content {
  position: relative;
  z-index: 2;
  padding: 50px;
  max-width: 600px;
}

.invitation-content h3 {
  font-size: 28px;
  margin-bottom: 20px;
}

.invitation-content p {
  font-size: 16px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.btn-contact {
  display: inline-block;
  padding: 12px 30px;
  background: rgba(255, 128, 0, 1);
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 5px 15px rgba(255, 128, 0, 0.3);
  margin-bottom: 20px;
}

.btn-contact:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(255, 128, 0, 0.4);
  background: rgba(255, 128, 0, 0.9);
  color: white;
}

.invitation-bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: url('static/picture/partner-bg.jpg') center/cover;
  opacity: 0.2;
}

.invitation-bg:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #2b64a7 0%, transparent 100%);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .partners-carousel-container {
    padding: 0 50px;
  }
  
  .core-partner-item {
    flex: 0 0 calc(50% - 30px);
  }
  
  .benefit-item {
    width: calc(50% - 30px);
  }
  
  .process-container {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .process-arrow {
    transform: rotate(90deg);
    margin: 10px 0;
  }
  
  .invitation-content {
    max-width: 100%;
    padding: 30px;
  }
  
  .invitation-bg {
    display: none;
  }
}

@media (max-width: 768px) {
  .partners-carousel-container {
    padding: 0 40px;
  }
  
  .core-partner-item {
    flex: 0 0 calc(100% - 30px);
  }
  
  .benefit-item {
    width: 100%;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .invitation-container {
    min-height: auto;
  }
  
  .invitation-content h3 {
    font-size: 24px;
  }
  
  .btn-contact {
    margin-bottom: 30px;
  }
}

@media (max-width: 480px) {
  .partners-carousel-container {
    padding: 0 30px;
  }
  
  .carousel-control {
    width: 36px;
    height: 36px;
  }
  
  .carousel-control svg {
    width: 20px;
    height: 20px;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // 合作伙伴轮播功能
  const carousel = document.querySelector('.partners-carousel-inner');
  const prevButton = document.querySelector('.carousel-prev');
  const nextButton = document.querySelector('.carousel-next');
  const indicators = document.querySelectorAll('.indicator');
  const partnerItems = document.querySelectorAll('.core-partner-item');
  
  let currentIndex = 0;
  let itemWidth = 0;
  let itemsPerView = 3; // 默认桌面端显示3个
  
  // 根据窗口大小调整每页显示的项目数
  function updateItemsPerView() {
    if (window.innerWidth < 768) {
      itemsPerView = 1;
    } else if (window.innerWidth < 992) {
      itemsPerView = 2;
    } else {
      itemsPerView = 3;
    }
    
    // 计算每个项目的宽度并更新
    itemWidth = carousel.clientWidth / itemsPerView;
    partnerItems.forEach(item => {
      item.style.flex = `0 0 ${itemWidth - 30}px`;
    });
    
    // 更新轮播状态
    updateCarousel();
  }
  
  // 更新轮播状态，移动到当前索引位置
  function updateCarousel() {
    carousel.style.transform = `translateX(-${currentIndex * itemWidth}px)`;
    
    // 更新指示器
    indicators.forEach((indicator, index) => {
      indicator.classList.toggle('active', Math.floor(currentIndex / itemsPerView) === index);
    });
    
    // 更新按钮状态
    prevButton.disabled = currentIndex === 0;
    prevButton.style.opacity = currentIndex === 0 ? 0.5 : 1;
    
    nextButton.disabled = currentIndex >= partnerItems.length - itemsPerView;
    nextButton.style.opacity = currentIndex >= partnerItems.length - itemsPerView ? 0.5 : 1;
  }
  
  // 切换到下一张
  function moveNext() {
    if (currentIndex < partnerItems.length - itemsPerView) {
      currentIndex++;
      updateCarousel();
    }
  }
  
  // 切换到上一张
  function movePrev() {
    if (currentIndex > 0) {
      currentIndex--;
      updateCarousel();
    }
  }
  
  // 绑定按钮事件
  prevButton.addEventListener('click', movePrev);
  nextButton.addEventListener('click', moveNext);
  
  // 点击指示器跳转到对应页
  indicators.forEach((indicator, index) => {
    indicator.addEventListener('click', function() {
      currentIndex = index * itemsPerView;
      updateCarousel();
    });
  });
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateItemsPerView);
  
  // 初始化
  updateItemsPerView();
  
  // 鼠标悬停动画效果增强
  const benefitItems = document.querySelectorAll('.benefit-item');
  
  benefitItems.forEach(item => {
    item.addEventListener('mouseover', function() {
      this.querySelector('.benefit-icon svg').style.transform = 'rotate(20deg) scale(1.2)';
      this.querySelector('.benefit-icon svg').style.transition = 'all 0.5s ease';
    });
    
    item.addEventListener('mouseout', function() {
      this.querySelector('.benefit-icon svg').style.transform = 'rotate(0) scale(1)';
    });
  });
});
</script>
</body>
</html>
