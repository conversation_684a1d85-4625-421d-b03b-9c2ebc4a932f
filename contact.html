<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8" />
<meta name="format-detection" content="telephone=no" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta name="HandheldFriendly" content="true" />
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
<!--[if lt IE 9]>
    <script type="text/javascript" src="static/js/css3-mediaqueries.js">
    </script>
    <![endif]-->
<meta name="Author" content="易思信网页工作室 www.eczone.net ">
<!--
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
           网站设计制作 by 易思信网页工作室 www.eczone.net   
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
    -->
<title>聚算云技术白皮书 - 算力共享实践</title>
<meta name="keywords" content='算力调度平台,云服务解决方案,边缘计算服务,AI算力服务,算力资源池,聚算云' />
<meta name="description" content='聚算云致力于用共享经济重塑算力分配规则，建立一个连接算力供应方与需求方的智能调度平台，实现算力资源的自由流动和高效利用。' />
<link rel="shortcut icon" href="favicon.ico" />
<link rel="stylesheet" href="static/css/animate.css" type="text/css" >
<link rel="stylesheet" href="static/css/common.css" type="text/css" >
<link rel="stylesheet" href="static/css/style.css" type="text/css" >
<script type="text/javascript" src="static/js/jquery.min.js"></script> 
<style>
/* 修复浮动图标显示问题 */
/* 直接移除背景并设置图标颜色 */
[class*="float-"],
[class*="fixed-"],
.rightside,
.online-service,
.fixed-contact,
.float-menu,
.side-bar,
.float-tools,
.left-sidebar,
.right-sidebar {
  background: none !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

/* 设置图标颜色 */
[class*="float-"] svg,
[class*="fixed-"] svg,
.rightside svg,
.online-service svg,
.fixed-contact svg,
.float-menu svg,
.side-bar svg,
.float-tools svg,
.left-sidebar svg,
.right-sidebar svg {
  fill: #2b64a7 !important;
  width: 24px !important;
  height: 24px !important;
}

/* 设置所有可能的浮动图标的样式 */
.float-icon,
.fixed-icon,
.side-icon,
.tool-icon,
.service-icon,
.support-icon,
.contact-icon {
  background: none !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  width: auto !important;
  height: auto !important;
}

/* 针对所有椭圆形图标容器 */
[style*="border-radius:50%"],
[style*="border-radius: 50%"] {
  border-radius: 0 !important;
  background: none !important;
}
</style>
</head>
<body>
<section class="wrapper">
  <header class="header"> <a href="/" class="logo"> <img class="hide" src="static/picture/logo.png" alt="" /> <img class="show" src="static/picture/logo2.png" alt="" /> </a>
    <ul class="navs">
      <li class=""><a href="/">首页</a></li>
      <li ><a href="page-abouts.html">关于我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业使命与愿景<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业文化<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">团队介绍<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">投资者关系<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="products-services.html">产品与服务<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="solution-cases.html">解决方案<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="solution-cases.html#gaming-cafe">电竞云网吧<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#ai-glasses">AI眼镜<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#digital-transformation">企业数字化转型<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#hpc-research">科研机构高性能计算<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#big-data-analytics">数据分析与大数据处理<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#media-rendering">媒体内容制作与渲染<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="hyyy-hyyy.html">成功案例<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">互联网行业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">人工智能<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">制造业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">金融服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教育与科研<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">政府与公共服务<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">技术与创新<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">技术白皮书<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">研发团队<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">专利与知识产权<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">合作与生态<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">合作伙伴<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">生态体系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">合作模式<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">资源中心<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">产品手册<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教程与指南<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">行业洞察<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">算力知识库<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li><a href="contact-contacts.html">联系我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="contact.html#sales-consult">销售咨询<i class="iconfont">&#xe624;</i></a></li>
            <li><a href="contact.html#tech-support">技术支持<i class="iconfont">&#xe624;</i></a></li>
            <li><a href="contact.html#media-contact">媒体联系<i class="iconfont">&#xe624;</i></a></li>
            <li><a href="contact.html#join-us">加入我们<i class="iconfont">&#xe624;</i></a></li>
            <li><a href="contact.html#office-location">办公地点<i class="iconfont">&#xe624;</i></a></li>
          </ol>
        </div>
      </li>
      <li class=""><a href="/">登陆/注册</a></li>
    </ul>
    <div class="header-menu">
      <div class="header-search"> <span class="iconfont">&nbsp;</span> </div>
      <div class="menu-btn">
        <p>MENU</p>
        <div class="menubtn"> <span></span> </div>
      </div>
      <div class="menu-flex">
        <div class="menu-bg"></div>
        <div class="menu-right">
          <ul class="menu-list">
            <li><a href="/">首页</a></li>
            <li ><a href="page-abouts.html">关于我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业使命与愿景<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业文化<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">团队介绍<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">投资者关系<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="products-services.html">产品与服务<em></em></a>
              <ol class="menu-leval">
                <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="solution-cases.html">解决方案<em></em></a>
              <ol class="menu-leval">
                <li><a href="solution-cases.html#gaming-cafe">电竞云网吧<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#ai-glasses">AI眼镜<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#digital-transformation">企业数字化转型<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#hpc-research">科研机构高性能计算<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#big-data-analytics">数据分析与大数据处理<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#media-rendering">媒体内容制作与渲染<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="hyyy-hyyy.html">成功案例<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">互联网行业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">人工智能<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">制造业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">金融服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教育与科研<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">政府与公共服务<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">技术与创新<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">技术白皮书<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">研发团队<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">专利与知识产权<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">合作与生态<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">合作伙伴<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">生态体系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">合作模式<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">资源中心<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">产品手册<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教程与指南<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">行业洞察<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">算力知识库<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="contact-contacts.html">联系我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="contact.html#sales-consult">销售咨询<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="contact.html#tech-support">技术支持<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="contact.html#media-contact">媒体联系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="contact.html#join-us">加入我们<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="contact.html#office-location">办公地点<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
          </ul>
          <div class="menu-ip-down">
            <div class="online-shopp"> </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class="main-content-wrap">
    <div class="content-wrap">
      <div class="head-search">
        <form action="search-search.html" method="post">
          <input class="search-ipt" type="text" name="query" placeholder="输入关键词...">
          <input class="search-btn" type="submit" name="submit" value="" title="搜索" aria-label="搜索">
        </form>
      </div>
    </div>
  </div>
  <div class="mobile-body-mask"></div>
  <div class="pbanner">
    <div class="product-hide">
      <figure><img class="pc" src="static/picture/casebanner.jpg" alt="聚算云技术白皮书"/>
        <div class="sjimg" style="background-image:url(static/images/casebanner.jpg);"> </div>
      </figure>
    </div>
    <div class="series global-fix">
      <p class="article-block slidetop">Technical Whitepaper</p>
      <div class="series-title article-block slidetop detay1 syht"> <strong>算力共享实践 · 创新价值共赢</strong> </div>
    </div>
    <div class="sea-down defaul"> <i></i> <span>向下滑动</span> </div>
  </div>
  
  <!--pageTop-->
  <div class="pageTop">
    <div class="w1600 clearfix">
      <div class="pageNav">
        <ul class="navlist clearfix swiper-wrapper">
          <li class="on"><a href="#" ><span>技术白皮书</span></a></li>
          <li><a href="#" ><span>研发团队</span></a></li>
          <li><a href="#" ><span>专利与知识产权</span></a></li>
        </ul>
      </div>
    </div>
  </div>
  <!--pageTop end--> 
  
  <!--page-->
  <div class="page clearfix"> 
    <!--pageInfo-->
    <div class="pageInfo clearfix">
      <div class="contact-container">
        <div class="contact-header">
          <h2 class="section-title">联系我们</h2>
          <p class="section-subtitle">随时为您提供专业的算力服务支持</p>
        </div>
        
        <div class="contact-content">
          <!-- 联系信息卡片 -->
          <div class="contact-card">
            <div class="contact-info">
              <h3 class="info-title">联系方式</h3>
              
              <!-- 销售咨询 -->
              <div id="sales-consult" class="info-item">
                <div class="info-content">
                  <h4>销售咨询</h4>
                  <p>邮箱：<EMAIL></p>
                  <p>电话：+86 10 8888 7777</p>
                  <p>工作时间：周一至周五 9:00-18:00</p>
                </div>
              </div>
              
              <!-- 技术支持 -->
              <div id="tech-support" class="info-item">
                <div class="info-content">
                  <h4>技术支持</h4>
                  <p>邮箱：<EMAIL></p>
                  <p>电话：+86 10 8888 6666</p>
                  <p>工作时间：全天候24/7支持</p>
                </div>
              </div>
              
              <!-- 媒体联系 -->
              <div id="media-contact" class="info-item">
                <div class="info-content">
                  <h4>媒体联系</h4>
                  <p>邮箱：<EMAIL></p>
                  <p>电话：+86 10 8888 5555</p>
                  <p>微信：jusuanPR</p>
                </div>
              </div>
              
              <!-- 总部地址 -->
              <div id="office-location" class="info-item">
                <div class="info-content">
                  <h4>总部地址</h4>
                  <p>北京市海淀区科技园区88号聚算云大厦</p>
                  <p>邮编：100080</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 联系表单 -->
          <div class="contact-form-card">
            <h3 class="form-title">发送您的需求</h3>
            <form class="contact-form">
              <div class="form-group">
                <label for="name">姓名</label>
                <input type="text" id="name" name="name" placeholder="请输入您的姓名" required>
              </div>
              
              <div class="form-group">
                <label for="company">公司</label>
                <input type="text" id="company" name="company" placeholder="请输入您的公司名称" required>
              </div>
              
              <div class="form-group">
                <label for="email">邮箱</label>
                <input type="email" id="email" name="email" placeholder="请输入您的邮箱" required>
              </div>
              
              <div class="form-group">
                <label for="phone">电话</label>
                <input type="tel" id="phone" name="phone" placeholder="请输入您的联系电话" required>
              </div>
              
              <div class="form-group full-width">
                <label for="subject">主题</label>
                <select id="subject" name="subject" required>
                  <option value="" disabled selected>请选择咨询主题</option>
                  <option value="sales">销售咨询</option>
                  <option value="support">技术支持</option>
                  <option value="cooperation">合作洽谈</option>
                  <option value="other">其他问题</option>
                </select>
              </div>
              
              <div class="form-group full-width">
                <label for="message">留言内容</label>
                <textarea id="message" name="message" rows="5" placeholder="请详细描述您的需求或问题" required></textarea>
              </div>
              
              <div class="form-submit">
                <button type="submit" class="submit-btn" title="提交表单" aria-label="提交表单">
                  <span class="btn-text">提交</span>
                  <svg viewBox="0 0 24 24" class="btn-icon">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                  </svg>
                </button>
              </div>
            </form>
          </div>
        </div>
        
        
        
        <!-- 分公司板块 -->
        <div class="branch-offices-section">
          <h3 class="section-title">分公司网络</h3>
          <div class="branch-container">
            <div class="branch-info">
              <div class="branch-details">
                <h4 class="branch-name">上海总部</h4>
                <div class="branch-contact-info">
                  <div class="info-item">
                    <span class="info-text">上海市浦东新区张江高科技园区博云路2号浦软大厦6楼</span>
                  </div>
                  <div class="info-item">
                    <span class="info-text"><EMAIL></span>
                  </div>
                  <div class="info-item">
                    <span class="info-text">021-12345678</span>
                  </div>
                  <div class="info-item">
                    <span class="info-text">张三 (区域经理)</span>
                  </div>
                </div>
                <!-- 百度地图容器 -->
                <div id="baiduMap" class="baidu-map"></div>
              </div>
            </div>
            
            <div class="branch-list">
              <div class="branch-item active" data-branch="shanghai">上海总部</div>
              <div class="branch-item" data-branch="beijing">北京分公司</div>
              <div class="branch-item" data-branch="guangzhou">广州分公司</div>
              <div class="branch-item" data-branch="shenzhen">深圳分公司</div>
              <div class="branch-item" data-branch="chengdu">成都分公司</div>
              <div class="branch-item" data-branch="wuhan">武汉分公司</div>
              <div class="branch-item" data-branch="xian">西安分公司</div>
              <div class="branch-item" data-branch="hangzhou">杭州分公司</div>
            </div>
          </div>
        </div>
        
        <!-- 招聘信息板块 -->
        <div id="join-us" class="recruitment-section">
          <h3 class="recruitment-title">加入我们</h3>
          <p class="recruitment-subtitle">用科技改变世界，与聚算云共同成长</p>
          
          <div class="recruitment-carousel-container">
            <button class="carousel-button prev" title="上一个" aria-label="上一个">
              <svg viewBox="0 0 24 24" class="carousel-btn-icon">
                <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
              </svg>
            </button>
            
            <div class="recruitment-carousel">
              <div class="recruitment-cards">
                <!-- 职位卡片1 -->
                <div class="job-card">
                  <div class="job-header">
                    <div class="job-title-container">
                      <svg viewBox="0 0 24 24" class="job-icon">
                        <path d="M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.9-1.99 2L2 19c0 1.11.89 2 1.99 2h16c1.11 0 2-.9 2-2V8c0-1.11-.89-2-2-2zm-6 0h-4V4h4v2z"/>
                      </svg>
                      <h4 class="job-title">高级算法工程师</h4>
                    </div>
                    <span class="job-badge">热招中</span>
                  </div>
                  
                  <div class="job-details">
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                      </svg>
                      <div class="job-description">
                        <h5>工作职责</h5>
                        <ul>
                          <li>负责AI算力调度相关算法研发</li>
                          <li>优化资源分配策略，提升系统性能</li>
                          <li>参与算力共享平台核心架构设计</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V8z"/>
                      </svg>
                      <div class="job-requirements">
                        <h5>任职要求</h5>
                        <ul>
                          <li>计算机、人工智能相关专业硕士以上学历</li>
                          <li>精通机器学习和深度学习算法</li>
                          <li>有分布式计算或云计算经验优先</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-count">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                      </svg>
                      <div class="job-positions">
                        <h5>招聘人数</h5>
                        <p>3人</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="job-apply">
                    <a href="#" class="apply-button" title="申请职位" aria-label="申请职位" rel="noopener noreferrer">
                      <span>立即申请</span>
                      <svg viewBox="0 0 24 24" class="apply-icon">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                      </svg>
                    </a>
                  </div>
                </div>
                
                <!-- 职位卡片2 -->
                <div class="job-card">
                  <div class="job-header">
                    <div class="job-title-container">
                      <svg viewBox="0 0 24 24" class="job-icon">
                        <path d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16.01H3V4.99h18v14.02zM8 16h2.5l1.5 1.5 1.5-1.5H16v-2.5l1.5-1.5-1.5-1.5V8h-2.5L12 6.5 10.5 8H8v2.5L6.5 12 8 13.5V16zm4-7c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3z"/>
                      </svg>
                      <h4 class="job-title">前端开发工程师</h4>
                    </div>
                    <span class="job-badge">急聘</span>
                  </div>
                  
                  <div class="job-details">
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                      </svg>
                      <div class="job-description">
                        <h5>工作职责</h5>
                        <ul>
                          <li>负责算力平台前端开发与优化</li>
                          <li>设计并实现用户友好的界面和交互体验</li>
                          <li>开发响应式、高性能的Web应用</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V8z"/>
                      </svg>
                      <div class="job-requirements">
                        <h5>任职要求</h5>
                        <ul>
                          <li>精通HTML5/CSS3/JavaScript</li>
                          <li>熟悉React/Vue等现代前端框架</li>
                          <li>有大型Web应用开发经验</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-count">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                      </svg>
                      <div class="job-positions">
                        <h5>招聘人数</h5>
                        <p>2人</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="job-apply">
                    <a href="#" class="apply-button" title="申请职位" aria-label="申请职位" rel="noopener noreferrer">
                      <span>立即申请</span>
                      <svg viewBox="0 0 24 24" class="apply-icon">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                      </svg>
                    </a>
                  </div>
                </div>
                
                <!-- 职位卡片3 -->
                <div class="job-card">
                  <div class="job-header">
                    <div class="job-title-container">
                      <svg viewBox="0 0 24 24" class="job-icon">
                        <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                      </svg>
                      <h4 class="job-title">后端开发工程师</h4>
                    </div>
                    <span class="job-badge">社招</span>
                  </div>
                  
                  <div class="job-details">
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                      </svg>
                      <div class="job-description">
                        <h5>工作职责</h5>
                        <ul>
                          <li>负责算力平台后端服务开发与维护</li>
                          <li>设计高并发、高可用的系统架构</li>
                          <li>优化算力资源调度系统性能</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V8z"/>
                      </svg>
                      <div class="job-requirements">
                        <h5>任职要求</h5>
                        <ul>
                          <li>精通Java/Go/Python等后端语言</li>
                          <li>熟悉分布式系统、微服务架构</li>
                          <li>有云计算平台开发经验优先</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-count">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                      </svg>
                      <div class="job-positions">
                        <h5>招聘人数</h5>
                        <p>5人</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="job-apply">
                    <a href="#" class="apply-button" title="申请职位" aria-label="申请职位" rel="noopener noreferrer">
                      <span>立即申请</span>
                      <svg viewBox="0 0 24 24" class="apply-icon">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                      </svg>
                    </a>
                  </div>
                </div>
                
                <!-- 职位卡片4 -->
                <div class="job-card">
                  <div class="job-header">
                    <div class="job-title-container">
                      <svg viewBox="0 0 24 24" class="job-icon">
                        <path d="M13 13v8h8v-8h-8zM3 21h8v-8H3v8zM3 3v8h8V3H3zm13.66-1.31L11 7.34 16.66 13l5.66-5.66-5.66-5.65z"/>
                      </svg>
                      <h4 class="job-title">AI研究科学家</h4>
                    </div>
                    <span class="job-badge">高薪</span>
                  </div>
                  
                  <div class="job-details">
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                      </svg>
                      <div class="job-description">
                        <h5>工作职责</h5>
                        <ul>
                          <li>负责算力智能调度算法研究</li>
                          <li>开发创新AI模型提升资源利用率</li>
                          <li>推动前沿AI技术在算力领域的应用</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V8z"/>
                      </svg>
                      <div class="job-requirements">
                        <h5>任职要求</h5>
                        <ul>
                          <li>人工智能/计算机科学博士学位</li>
                          <li>在顶级AI会议发表过论文</li>
                          <li>有资源调度或强化学习研究经验</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-count">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                      </svg>
                      <div class="job-positions">
                        <h5>招聘人数</h5>
                        <p>2人</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="job-apply">
                    <a href="#" class="apply-button" title="申请职位" aria-label="申请职位" rel="noopener noreferrer">
                      <span>立即申请</span>
                      <svg viewBox="0 0 24 24" class="apply-icon">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                      </svg>
                    </a>
                  </div>
                </div>
                <div class="job-card">
                  <div class="job-header">
                    <div class="job-title-container">
                      <svg viewBox="0 0 24 24" class="job-icon">
                        <path d="M13 13v8h8v-8h-8zM3 21h8v-8H3v8zM3 3v8h8V3H3zm13.66-1.31L11 7.34 16.66 13l5.66-5.66-5.66-5.65z"/>
                      </svg>
                      <h4 class="job-title">AI研究科学家</h4>
                    </div>
                    <span class="job-badge">高薪</span>
                  </div>
                  
                  <div class="job-details">
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                      </svg>
                      <div class="job-description">
                        <h5>工作职责</h5>
                        <ul>
                          <li>负责算力智能调度算法研究</li>
                          <li>开发创新AI模型提升资源利用率</li>
                          <li>推动前沿AI技术在算力领域的应用</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-item">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3V8z"/>
                      </svg>
                      <div class="job-requirements">
                        <h5>任职要求</h5>
                        <ul>
                          <li>人工智能/计算机科学博士学位</li>
                          <li>在顶级AI会议发表过论文</li>
                          <li>有资源调度或强化学习研究经验</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div class="job-count">
                      <svg viewBox="0 0 24 24" class="job-detail-icon">
                        <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                      </svg>
                      <div class="job-positions">
                        <h5>招聘人数</h5>
                        <p>2人</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="job-apply">
                    <a href="#" class="apply-button" title="申请职位" aria-label="申请职位" rel="noopener noreferrer">
                      <span>立即申请</span>
                      <svg viewBox="0 0 24 24" class="apply-icon">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            
            <button class="carousel-button next" title="下一个" aria-label="下一个">
              <svg viewBox="0 0 24 24" class="carousel-btn-icon">
                <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
              </svg>
            </button>
          </div>
          
          <div class="carousel-dots">
            <span class="dot active"></span>
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
          
          <div class="view-all-jobs">
            <a href="#" class="view-all-link" title="查看所有职位" aria-label="查看所有职位" rel="noopener noreferrer">
              查看所有职位
              <svg viewBox="0 0 24 24" class="view-all-icon">
                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
    <!--End--> 
  </div>
  <!--page End--> 
  <!--footer--> 
  <!--footer-->
  <div class="footer clearfix"> 
    <!--footer-nav-->
    <div class="footer-nav">
      <div class="w1600 clearfix">
        <div class="left">
          <div class="leftBox clearfix">
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="solution-cases.html">蒸汽节能解决方案</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="solution-1805-1809.html">ESOS-蒸汽系统节能优化</a></li>
                    <li class="t2"><a href="solution-1805-1810.html">蒸汽疏水阀调查</a></li>
                    <li class="t2"><a href="solution-1805-1811.html">蒸汽系统诊断调查</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="advantage-ys.html">我们的优势</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="advantage-ys.html">一体式蒸汽工程解决方案</a></li>
                    <li class="t2"><a href="advantage-ys.html#adBox2">创新节能技术</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="page-abouts.html">关于我们</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="page-1751-1752.html">公司介绍</a></li>
                    <li class="t2"><a href="page2-1751-1753.html">常问问题</a></li>
                    <li class="t2"><a href="solution-1751-1832.html">服务客户</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="hyyy-hyyy.html">行业应用案例</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="hylist-1808-1814.html">化工</a></li>
                    <li class="t2"><a href="hylist-1808-1815.html">石化</a></li>
                    <li class="t2"><a href="hylist-1808-1816.html">制药</a></li>
                    <li class="t2"><a href="hylist-1808-1817.html">食品饮料</a></li>
                    <li class="t2"><a href="hylist-1808-1818.html">其它</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="rightBox clearfix">
            <table width="100%">
              <tbody>
                <tr>
                  <td><h1> 服务热线 </h1></td>
                </tr>
                <tr>
                  <td class="tel"><h1> <span>+86 13321109027</span> </h1>
                    <p> 诚挚为您服务 </p></td>
                </tr>
              </tbody>
            </table>
            <div class="bottom-share"> <span>关注我们：</span> <a class="iconfont email" href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">&#xe6db;</a> <a class="iconfont qq" href="http://wpa.qq.com/msgrd?v=3&uin=393202489&site=kingtin&menu=yes" target="_blank" rel="noopener noreferrer">&#xe60f;</a> <a class="iconfont weix"  rel="noopener noreferrer">&#xe660;<span class="img"><img src="static/picture/202203011551003232.jpg" alt="" /><br />
              </span></a> </div>
          </div>
        </div>
      </div>
      <div class="bottom-wz">
        <div class="w1600 clearfix">思德乐，提供卓越的蒸汽工程解决方案。</div>
      </div>
    </div>
    <!--footer-nav end-->
    <div class="bq clearfix">
      <div class="w1600 clearfix">
        <div class="bqname"> &copy;2021 佛山市思德乐能源科技有限公司 版权所有 <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">粤ICP备2022022663号</a> </div>
        <div class="beian"> 网站设计: <a href="http://www.king-tin.com/" target="_blank" rel="noopener noreferrer">KINGTIN</a><script charset="UTF-8" id="LA_COLLECT" src="static/js/js-sdk-pro.min.js"></script><script>LA.init({id: "Jh6tR8OE31b5wc6v",ck: "Jh6tR8OE31b5wc6v"})</script> 
        </div>
      </div>
    </div>
  </div>
  <!--End footer-->
  <div class="common-bott">
    <div class="back-top commontop iconfont">&#xe731;</div>
  </div>
</section>
<script type="text/javascript" src="static/js/wow.min2.js"></script> 
<script type="text/javascript" src="static/js/plugin.js"></script> 
<script type="text/javascript" src="static/js/page.js"></script> 
<script>
var scrolling = true;
var timer = null;
$(document).on('mousewheel DOMMouseScroll', function (e) {
        if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
        navigateUp();
        } else {
        if (e.pageY <= w_height && scrolling && !isMobile) {
        clearTimeout(timer);
        scrolling = false;
        timer = setTimeout(function () {
        jQuery("html,body").stop(true, true).animate({ scrollTop: w_height }, 1200, "easeOutQuint")
        }, 100)
        }
        }
});
function navigateUp() {
        scrolling = true;
}
</script>

<!-- 招聘轮播功能的JavaScript代码 -->
<script>
  $(document).ready(function() {
    // 获取轮播元素
    const carousel = $('.recruitment-cards');
    const carouselItems = $('.job-card');
    const prevBtn = $('.carousel-button.prev');
    const nextBtn = $('.carousel-button.next');
    const dots = $('.dot');
    
    // 轮播配置
    let currentIndex = 0;
    const itemCount = carouselItems.length;
    let cardWidth;
    let visibleItems;
    let autoplayTimer;
    const autoplayDelay = 5000; // 自动播放间隔，单位毫秒
    
    // 根据屏幕宽度决定显示卡片数量
    function updateCarouselLayout() {
      const carouselWidth = $('.recruitment-carousel').width();
      
      // 响应式设置可见卡片数量
      if (window.innerWidth >= 992) {
        visibleItems = 3;
      } else if (window.innerWidth >= 768) {
        visibleItems = 2;
      } else {
        visibleItems = 1;
      }
      
      // 计算卡片宽度
      cardWidth = carouselWidth / visibleItems;
      
      // 设置卡片宽度
      carouselItems.css('min-width', cardWidth - 30 + 'px');
      
      // 重新定位轮播
      goToSlide(currentIndex);
    }
    
    // 滑动到指定索引的卡片
    function goToSlide(index) {
      // 确保索引在合理范围内
      if (index < 0) {
        index = 0;
      } else if (index > itemCount - visibleItems) {
        index = itemCount - visibleItems;
      }
      
      currentIndex = index;
      
      // 移动轮播
      carousel.css('transform', `translateX(-${currentIndex * cardWidth}px)`);
      
      // 更新指示点状态
      dots.removeClass('active');
      dots.eq(Math.min(currentIndex, dots.length - 1)).addClass('active');
    }
    
    // 上一个
    function prevSlide() {
      goToSlide(currentIndex - 1);
    }
    
    // 下一个
    function nextSlide() {
      goToSlide(currentIndex + 1);
      
      // 如果已经到最后，循环回第一个
      if (currentIndex >= itemCount - visibleItems) {
        setTimeout(() => {
          carousel.css('transition', 'none');
          goToSlide(0);
          setTimeout(() => {
            carousel.css('transition', 'transform 0.5s ease-in-out');
          }, 50);
        }, 500);
      }
    }
    
    // 自动播放
    function startAutoplay() {
      stopAutoplay();
      autoplayTimer = setInterval(nextSlide, autoplayDelay);
    }
    
    // 停止自动播放
    function stopAutoplay() {
      if (autoplayTimer) {
        clearInterval(autoplayTimer);
      }
    }
    
    // 点击事件
    prevBtn.on('click', function() {
      prevSlide();
      stopAutoplay();
      startAutoplay(); // 点击后重新开始自动播放计时
    });
    
    nextBtn.on('click', function() {
      nextSlide();
      stopAutoplay();
      startAutoplay(); // 点击后重新开始自动播放计时
    });
    
    // 点击指示点
    dots.on('click', function() {
      const dotIndex = $(this).index();
      goToSlide(dotIndex);
      stopAutoplay();
      startAutoplay(); // 点击后重新开始自动播放计时
    });
    
    // 鼠标悬停时停止自动播放
    $('.recruitment-carousel-container').on('mouseenter', stopAutoplay);
    
    // 鼠标离开时恢复自动播放
    $('.recruitment-carousel-container').on('mouseleave', startAutoplay);
    
    // 窗口大小改变时更新布局
    $(window).on('resize', updateCarouselLayout);
    
    // 初始化
    updateCarouselLayout();
    startAutoplay();
  });
</script>

<!-- 修复轮播功能的脚本 -->
<script>
// 等待页面完全加载后再初始化轮播
window.addEventListener('load', function() {
  setTimeout(function() {
    initJobCarousel();
  }, 500);
});

function initJobCarousel() {
  // 确保元素存在
  if ($('.recruitment-cards').length === 0 || $('.job-card').length === 0) {
    console.log("轮播元素未找到，1秒后重试");
    setTimeout(initJobCarousel, 1000);
    return;
  }
  
  console.log("初始化轮播");
  
  // 获取轮播元素
  const carousel = $('.recruitment-cards');
  const carouselItems = $('.job-card');
  const prevBtn = $('.carousel-button.prev');
  const nextBtn = $('.carousel-button.next');
  const dots = $('.carousel-dots .dot');
  
  // 设置初始样式
  carousel.css('transition', 'transform 0.5s ease-in-out');
  
  // 轮播配置
  let currentIndex = 0;
  const itemCount = carouselItems.length;
  let cardWidth;
  let visibleItems;
  let autoplayTimer;
  const autoplayDelay = 4000; // 自动播放间隔，单位毫秒
  
  // 根据屏幕宽度决定显示卡片数量并计算宽度
  function updateCarouselLayout() {
    const carouselWidth = $('.recruitment-carousel').width();
    console.log("轮播容器宽度:", carouselWidth);
    
    // 响应式设置可见卡片数量
    if (window.innerWidth >= 992) {
      visibleItems = 3;
    } else if (window.innerWidth >= 768) {
      visibleItems = 2;
    } else {
      visibleItems = 1;
    }
    
    // 计算每个卡片的宽度
    cardWidth = Math.floor(carouselWidth / visibleItems);
    console.log("卡片宽度:", cardWidth, "可见卡片数:", visibleItems);
    
    // 设置每个卡片的宽度
    carouselItems.each(function() {
      $(this).css({
        'min-width': (cardWidth - 30) + 'px',
        'max-width': (cardWidth - 30) + 'px'
      });
    });
    
    // 重新定位轮播
    goToSlide(currentIndex, false);
  }
  
  // 滑动到指定索引的卡片
  function goToSlide(index, animated = true) {
    // 确保索引在合理范围内
    if (index < 0) {
      index = 0;
    } else if (index > itemCount - visibleItems) {
      index = itemCount - visibleItems;
    }
    
    currentIndex = index;
    console.log("移动到索引:", currentIndex);
    
    // 设置过渡效果
    if (!animated) {
      carousel.css('transition', 'none');
    } else {
      carousel.css('transition', 'transform 0.5s ease-in-out');
    }
    
    // 移动轮播
    const moveX = currentIndex * cardWidth;
    carousel.css('transform', `translateX(-${moveX}px)`);
    console.log("移动距离:", moveX);
    
    // 更新指示点状态
    dots.removeClass('active');
    dots.eq(Math.min(currentIndex, dots.length - 1)).addClass('active');
    
    // 如果禁用了动画，稍后重新启用
    if (!animated) {
      setTimeout(function() {
        carousel.css('transition', 'transform 0.5s ease-in-out');
      }, 50);
    }
  }
  
  // 上一个
  function prevSlide() {
    if (currentIndex > 0) {
      goToSlide(currentIndex - 1);
    } else {
      // 如果是第一个，则跳到最后
      goToSlide(itemCount - visibleItems);
    }
  }
  
  // 下一个
  function nextSlide() {
    if (currentIndex < itemCount - visibleItems) {
      goToSlide(currentIndex + 1);
    } else {
      // 如果是最后一个，则跳回第一个
      goToSlide(0);
    }
  }
  
  // 自动播放
  function startAutoplay() {
    stopAutoplay();
    autoplayTimer = setInterval(nextSlide, autoplayDelay);
  }
  
  // 停止自动播放
  function stopAutoplay() {
    if (autoplayTimer) {
      clearInterval(autoplayTimer);
      autoplayTimer = null;
    }
  }
  
  // 确保点击事件只绑定一次
  prevBtn.off('click').on('click', function() {
    console.log("点击上一个按钮");
    prevSlide();
    stopAutoplay();
    startAutoplay();
  });
  
  nextBtn.off('click').on('click', function() {
    console.log("点击下一个按钮");
    nextSlide();
    stopAutoplay();
    startAutoplay();
  });
  
  // 点击指示点
  dots.off('click').on('click', function() {
    const dotIndex = $(this).index();
    console.log("点击指示点:", dotIndex);
    goToSlide(dotIndex);
    stopAutoplay();
    startAutoplay();
  });
  
  // 鼠标悬停时停止自动播放
  $('.recruitment-carousel-container').off('mouseenter').on('mouseenter', function() {
    console.log("鼠标进入，暂停自动播放");
    stopAutoplay();
  });
  
  // 鼠标离开时恢复自动播放
  $('.recruitment-carousel-container').off('mouseleave').on('mouseleave', function() {
    console.log("鼠标离开，恢复自动播放");
    startAutoplay();
  });
  
  // 窗口大小改变时更新布局
  $(window).off('resize.jobCarousel').on('resize.jobCarousel', function() {
    console.log("窗口大小改变，更新布局");
    updateCarouselLayout();
  });
  
  // 初始化
  updateCarouselLayout();
  console.log("开始自动播放");
  startAutoplay();
}
</script>
<style>
.icon-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}
.icon-wrapper {
  text-align: center;
  margin: 0 15px;
}
.svg-icon {
  width: 64px;
  height: 64px;
  fill: #2b64a7;
  margin-bottom: 10px;
}
.icon-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}
.brand-core-box {
  display: flex;
  justify-content: space-around;
  margin: 30px 0;
  flex-wrap: wrap;
}

.brand-core-item {
  text-align: center;
  width: 200px;
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.brand-core-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(43, 100, 167, 0.2);
  background-color: rgba(43, 100, 167, 0.05);
}

.brand-core-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(43, 100, 167, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.4s ease;
}

.brand-core-item:hover:before {
  opacity: 1;
  transform: scale(2);
}

.brand-core-svg {
  width: 64px;
  height: 64px;
  margin-bottom: 15px;
  fill: #2b64a7;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 1;
}

.brand-core-item:hover .brand-core-svg {
  transform: rotate(15deg) scale(1.2);
  fill: #2b64a7;
}

.brand-core-title {
  color: #2b64a7;
  font-size: 18px;
  margin: 10px 0;
  font-weight: bold;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.brand-core-item:hover .brand-core-title {
  transform: scale(1.05);
}

.brand-core-text {
  color: #333;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.brand-core-item:hover .brand-core-text {
  color: #2b64a7;
}

/* 联系页面样式 */
.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 50px 20px;
}

/* 头部样式 */
.contact-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-title {
  color: #2b64a7;
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #2b64a7, rgba(255, 128, 0, 1));
  border-radius: 3px;
}

.section-subtitle {
  color: #666;
  font-size: 18px;
  margin-top: 20px;
}

/* 内容区域样式 */
.contact-content {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 50px;
}

/* 联系信息卡片 */
.contact-card {
  flex: 1;
  min-width: 300px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-5px);
}

.contact-info {
  padding: 30px;
}

.info-title {
  color: #2b64a7;
  font-size: 24px;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  margin-bottom: 25px;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  transition: background-color 0.3s ease;
}

.info-svg {
  width: 18px;
  height: 18px;
  fill: #2b64a7;
}

.info-content {
  flex: 1;
}

.info-content h4 {
  color: #333;
  font-size: 18px;
  margin: 0 0 10px;
}

.info-content p {
  color: #666;
  font-size: 14px;
  margin: 5px 0;
  line-height: 1.5;
}

/* 联系表单卡片 */
.contact-form-card {
  flex: 1;
  min-width: 300px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  padding: 30px;
  transition: transform 0.3s ease;
}

.contact-form-card:hover {
  transform: translateY(-5px);
}

.form-title {
  color: #2b64a7;
  font-size: 24px;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.form-group {
  flex: 0 0 calc(50% - 10px);
}

.full-width {
  flex: 0 0 100%;
}

.form-group label {
  display: block;
  color: #333;
  font-size: 14px;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2b64a7;
  box-shadow: 0 0 0 2px rgba(43, 100, 167, 0.2);
}

.form-submit {
  flex: 0 0 100%;
  margin-top: 20px;
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 25px;
  background-color: #2b64a7;
  border: none;
  border-radius: 5px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background-color: rgba(255, 128, 0, 1);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(43, 100, 167, 0.3);
}

.btn-text {
  margin-right: 10px;
}

.btn-icon {
  width: 18px;
  height: 18px;
  fill: white;
}

/* 社交媒体样式 */
.social-media {
  margin-bottom: 50px;
  text-align: center;
}

.social-title {
  color: #2b64a7;
  font-size: 24px;
  margin-bottom: 20px;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.social-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background: #2b64a7;
}

.social-svg {
  width: 24px;
  height: 24px;
  fill: #2b64a7;
  transition: all 0.3s ease;
}

.social-icon:hover .social-svg {
  fill: white;
}

/* 合作伙伴样式 */
.partners-section {
  padding: 30px 0;
  text-align: center;
}

.partners-title {
  color: #2b64a7;
  font-size: 24px;
  margin-bottom: 30px;
}

.partners-logos {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.partner-logo {
  width: 160px;
  height: 80px;
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.partner-logo:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(43, 100, 167, 0.1);
}

.partner-logo img {
  max-width: 100%;
  max-height: 100%;
}

/* 招聘信息区域样式 */
.recruitment-section {
  padding: 50px 0;
  text-align: center;
  background: linear-gradient(180deg, rgba(43, 100, 167, 0.03) 0%, rgba(255, 255, 255, 0) 100%);
  border-radius: 15px;
  margin-top: 50px;
  position: relative;
  overflow: hidden;
}

.recruitment-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(43, 100, 167, 0.03) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: -1;
  animation: pulse 15s infinite linear;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

.recruitment-title {
  color: #2b64a7;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.recruitment-title:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #2b64a7, rgba(255, 128, 0, 1));
  border-radius: 3px;
}

.recruitment-subtitle {
  color: #666;
  font-size: 16px;
  margin: 20px 0 40px;
}

/* 招聘轮播容器 */
.recruitment-carousel-container {
  position: relative;
  max-width: 1100px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recruitment-carousel {
  width: 100%;
  max-width: 900px;
  overflow: hidden;
  margin: 0 20px;
}

.recruitment-cards {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

/* 职位卡片样式 */
.job-card {
  min-width: 300px;
  width: 100%;
  margin: 0 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  text-align: left;
  border-top: 4px solid transparent;
  border-image: linear-gradient(90deg, #2b64a7, rgba(255, 128, 0, 1));
  border-image-slice: 1;
  position: relative;
}

.job-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(43, 100, 167, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.job-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(43, 100, 167, 0.15);
}

.job-card:hover::before {
  opacity: 1;
}

/* 职位卡片头部 */
.job-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.job-title-container {
  display: flex;
  align-items: center;
}

.job-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  fill: #2b64a7;
}

.job-title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.job-badge {
  display: inline-block;
  padding: 4px 10px;
  background-color: #2b64a7;
  border-radius: 15px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.job-badge:hover {
  background-color: rgba(255, 128, 0, 1);
}

/* 职位详情 */
.job-details {
  flex: 1;
  padding: 20px;
}

.job-item {
  display: flex;
  margin-bottom: 15px;
}

.job-detail-icon {
  width: 22px;
  height: 22px;
  margin-right: 10px;
  margin-top: 4px;
  flex-shrink: 0;
  fill: #2b64a7;
}

.job-description,
.job-requirements,
.job-positions {
  flex: 1;
}

.job-description h5,
.job-requirements h5,
.job-positions h5 {
  color: #2b64a7;
  font-size: 15px;
  margin: 0 0 8px;
}

.job-description ul,
.job-requirements ul {
  padding-left: 15px;
  margin: 0;
}

.job-description li,
.job-requirements li,
.job-positions p {
  color: #555;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 5px;
}

/* 申请按钮 */
.job-apply {
  padding: 0 20px 20px;
}

.apply-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 0;
  background-color: #2b64a7;
  border: none;
  border-radius: 6px;
  color: white!important;
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.apply-button:hover {
  background-color: rgba(255, 128, 0, 1);
  box-shadow: 0 5px 15px rgba(43, 100, 167, 0.3);
}

.apply-button:hover::before {
  left: 100%;
}

.apply-button span {
  margin-right: 8px;
}

.apply-icon {
  width: 16px;
  height: 16px;
  fill: white;
}

/* 轮播控制按钮 */
.carousel-button {
  background: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1;
}

.carousel-button:hover {
  background: #2b64a7;
}

.carousel-btn-icon {
  width: 24px;
  height: 24px;
  fill: #2b64a7;
  transition: all 0.3s ease;
}

.carousel-button:hover .carousel-btn-icon {
  fill: white;
}

/* 轮播指示点 */
.carousel-dots {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #e0e0e0;
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  width: 24px;
  border-radius: 5px;
  background-color: #2b64a7;
}

/* 查看所有职位链接 */
.view-all-jobs {
  margin-top: 30px;
}

.view-all-link {
  display: inline-flex;
  align-items: center;
  color: #2b64a7;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.view-all-link:hover {
  color: rgba(255, 128, 0, 1);
}

.view-all-icon {
  width: 18px;
  height: 18px;
  margin-left: 5px;
  fill: currentColor;
  transition: transform 0.3s ease;
}

.view-all-link:hover .view-all-icon {
  transform: translateX(5px);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .contact-content {
    flex-direction: column;
  }
  
  .section-title {
    font-size: 30px;
  }
  
  .section-subtitle {
    font-size: 16px;
  }

  .job-card {
    min-width: 280px;
  }
}

@media (max-width: 768px) {
  .form-group {
    flex: 0 0 100%;
  }
  
  .partners-logos {
    gap: 20px;
  }
  
  .partner-logo {
    width: 130px;
    height: 65px;
  }
  
  .section-title, .recruitment-title {
    font-size: 26px;
  }

  .recruitment-carousel {
    max-width: 90%;
  }
  
  .carousel-button {
    width: 40px;
    height: 40px;
  }
  
  .job-card {
    min-width: 260px;
    margin: 0 10px;
  }
}

@media (max-width: 480px) {
  .contact-container {
    padding: 30px 15px;
  }
  
  .info-item {
    flex-direction: column;
  }
  
  .info-icon {
    margin-bottom: 15px;
  }
  
  .partner-logo {
    width: 110px;
    height: 55px;
  }
  
  .section-title, .recruitment-title {
    font-size: 22px;
  }
  
  .social-icon {
    width: 40px;
    height: 40px;
  }
  
  .social-svg {
    width: 20px;
    height: 20px;
  }
  
  .job-card {
    min-width: calc(100% - 20px);
  }
  
  .job-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .job-badge {
    margin-top: 10px;
  }
}

/* 分公司板块样式 */
.branch-offices-section {
  margin: 50px 0;
  padding: 0 20px;
}

/* 百度地图样式 */
.baidu-map {
  width: 100%;
  height: 250px;
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28px;
  margin-bottom: 30px;
  color: #2b64a7;
  text-align: center;
  position: relative;
  font-weight: 600;
}

.section-title:after {
  content: "";
  display: block;
  width: 60px;
  height: 3px;
  background-color: #2b64a7;
  margin: 10px auto 0;
}

.branch-container {
  display: flex;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  min-height: 300px;
}

.branch-info {
  flex: 2;
  padding: 30px;
  border-right: 1px solid #eee;
  position: relative;
}

.branch-details {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.branch-name {
  font-size: 24px;
  color: #2b64a7;
  margin-bottom: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.branch-contact-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  transition: background-color 0.3s ease;
}

.info-svg {
  width: 18px;
  height: 18px;
  fill: #2b64a7;
}

.info-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

.map-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
  padding: 8px 16px;
  background-color: #2b64a7;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.map-link:hover {
  background-color: rgba(255, 128, 0, 1);
}

.map-svg {
  width: 16px;
  height: 16px;
  fill: white;
}

.branch-list {
  flex: 1;
  background-color: #f8f9fa;
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.branch-item {
  padding: 15px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-size: 16px;
  color: #333;
}

.branch-item:hover {
  color: rgba(255, 128, 0, 1);
  background-color: rgba(43, 100, 167, 0.05);
}

.branch-item.active {
  color: white;
  background-color: #2b64a7;
  font-weight: 500;
}

.branch-item.active:hover {
  background-color: rgba(255, 128, 0, 1);
}

.branch-item.active:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: rgba(255, 128, 0, 1);
}

/* 修改按钮和渐变颜色 */
.submit-btn, 
.location-icon,
.contact-icon,
.email-icon,
.phone-icon,
.search-btn {
  background-color: #2b64a7;
  transition: background-color 0.3s ease;
}

.submit-btn:hover, 
.location-icon:hover,
.contact-icon:hover,
.email-icon:hover,
.phone-icon:hover,
.search-btn:hover,
.social-icon:hover {
  background-color: rgba(255, 128, 0, 1);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .branch-container {
    flex-direction: column;
  }
  
  .branch-info {
    border-right: none;
    border-bottom: 1px solid #eee;
  }
  
  .branch-list {
    flex-direction: row;
    overflow-x: auto;
    padding: 15px 0;
  }
  
  .branch-item {
    padding: 10px 20px;
    white-space: nowrap;
  }
  
  .branch-item.active:before {
    width: 100%;
    height: 3px;
    bottom: 0;
    top: auto;
  }
}

@media (max-width: 768px) {
  .branch-container {
    min-height: auto;
  }
  
  .branch-name {
    font-size: 20px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .info-icon {
    margin-bottom: 5px;
  }
}

/* JavaScript代码会放在页面底部 */
</style>

<script>
// 分公司数据
const branchData = {
  shanghai: {
    name: "上海总部",
    address: "上海市浦东新区张江高科技园区博云路2号浦软大厦6楼",
    email: "<EMAIL>",
    phone: "021-12345678",
    contact: "张三 (区域经理)",
    latitude: 31.203405,
    longitude: 121.607564
  },
  beijing: {
    name: "北京分公司",
    address: "北京市海淀区中关村科技园区8号楼1001室",
    email: "<EMAIL>",
    phone: "010-87654321",
    contact: "李四 (区域经理)",
    latitude: 40.008857,
    longitude: 116.338674
  },
  guangzhou: {
    name: "广州分公司",
    address: "广州市天河区珠江新城冼村路2号科技大厦5楼",
    email: "<EMAIL>",
    phone: "020-98765432",
    contact: "王五 (区域经理)",
    latitude: 23.125178,
    longitude: 113.327138
  },
  shenzhen: {
    name: "深圳分公司",
    address: "深圳市南山区科技园中区15栋A座5楼",
    email: "<EMAIL>",
    phone: "0755-23456789",
    contact: "赵六 (区域经理)",
    latitude: 22.540470,
    longitude: 113.934989
  },
  chengdu: {
    name: "成都分公司",
    address: "成都市高新区天府大道1700号环球中心E1区10楼",
    email: "<EMAIL>",
    phone: "028-34567890",
    contact: "钱七 (区域经理)",
    latitude: 30.588759,
    longitude: 104.064388
  },
  wuhan: {
    name: "武汉分公司",
    address: "武汉市东湖新技术开发区光谷大道77号光谷金融中心3楼",
    email: "<EMAIL>",
    phone: "027-45678901",
    contact: "孙八 (区域经理)",
    latitude: 30.498882,
    longitude: 114.414271
  },
  xian: {
    name: "西安分公司",
    address: "西安市高新区科技路33号高新国际商务中心15楼",
    email: "<EMAIL>",
    phone: "029-56789012",
    contact: "周九 (区域经理)",
    latitude: 34.219557,
    longitude: 108.889542
  },
  hangzhou: {
    name: "杭州分公司",
    address: "杭州市西湖区西溪路556号西溪创意园B座8楼",
    email: "<EMAIL>",
    phone: "0571-67890123",
    contact: "吴十 (区域经理)",
    latitude: 30.274084,
    longitude: 120.155070
  }
};

// 分公司切换逻辑
document.addEventListener('DOMContentLoaded', function() {
  const branchItems = document.querySelectorAll('.branch-item');
  const branchDetails = document.querySelector('.branch-details');
  let map;
  let marker;
  
  // 初始化百度地图
  function initMap() {
    // 创建百度地图实例
    map = new BMap.Map("baiduMap");
    
    // 创建默认中心点（上海总部）
    const defaultBranch = branchData.shanghai;
    const defaultPoint = new BMap.Point(defaultBranch.longitude, defaultBranch.latitude);
    
    // 初始化地图，设置中心点和缩放级别
    map.centerAndZoom(defaultPoint, 15);
    
    // 添加控件
    map.addControl(new BMap.NavigationControl());
    map.addControl(new BMap.ScaleControl());
    
    // 创建标记
    marker = new BMap.Marker(defaultPoint);
    map.addOverlay(marker);
    
    // 创建信息窗口
    const infoWindow = new BMap.InfoWindow(`<h4 style="margin:0;font-size:16px;color:#2b64a7;">${defaultBranch.name}</h4><p style="margin:5px 0 0;font-size:13px;">${defaultBranch.address}</p>`);
    marker.addEventListener("click", function() {
      map.openInfoWindow(infoWindow, defaultPoint);
    });
    
    // 默认打开信息窗口
    map.openInfoWindow(infoWindow, defaultPoint);
  }
  
  // 更新地图位置
  function updateMap(branch) {
    const point = new BMap.Point(branch.longitude, branch.latitude);
    map.centerAndZoom(point, 15);
    
    // 移除旧标记
    map.clearOverlays();
    
    // 添加新标记
    marker = new BMap.Marker(point);
    map.addOverlay(marker);
    
    // 创建信息窗口
    const infoWindow = new BMap.InfoWindow(`<h4 style="margin:0;font-size:16px;color:#2b64a7;">${branch.name}</h4><p style="margin:5px 0 0;font-size:13px;">${branch.address}</p>`);
    marker.addEventListener("click", function() {
      map.openInfoWindow(infoWindow, point);
    });
    
    // 默认打开信息窗口
    map.openInfoWindow(infoWindow, point);
  }
  
  // 加载百度地图API
  function loadBaiduMapScript() {
    const script = document.createElement('script');
    script.src = "https://api.map.baidu.com/api?v=3.0&ak=iCc6Mdvj7xaW2UMmvb4n5k275MgVbqld&callback=initBaiduMap";
    document.body.appendChild(script);
    
    // 当API加载完成后初始化地图
    window.initBaiduMap = function() {
      initMap();
    };
  }
  
  // 加载百度地图API
  loadBaiduMapScript();
  
  // 设置点击事件
  branchItems.forEach(item => {
    item.addEventListener('click', function() {
      // 移除所有激活状态
      branchItems.forEach(i => i.classList.remove('active'));
      
      // 添加当前激活状态
      this.classList.add('active');
      
      // 获取分公司数据
      const branchId = this.getAttribute('data-branch');
      const branch = branchData[branchId];
      
      // 淡出效果
      branchDetails.style.opacity = 0;
      
      setTimeout(() => {
        // 更新分公司信息
        document.querySelector('.branch-name').textContent = branch.name;
        document.querySelectorAll('.info-text')[0].textContent = branch.address;
        document.querySelectorAll('.info-text')[1].textContent = branch.email;
        document.querySelectorAll('.info-text')[2].textContent = branch.phone;
        document.querySelectorAll('.info-text')[3].textContent = branch.contact;
        
        // 更新地图
        if (map) {
          updateMap(branch);
        }
        
        // 淡入效果
        branchDetails.style.opacity = 1;
      }, 300);
    });
  });
});
</script>
</body>
</html>
