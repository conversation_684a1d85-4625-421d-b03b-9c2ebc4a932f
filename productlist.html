<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8" />
<meta name="format-detection" content="telephone=no" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta name="HandheldFriendly" content="true" />
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
<!--[if lt IE 9]>
    <script type="text/javascript" src="static/js/css3-mediaqueries.js">
    </script>
    <![endif]-->
    <meta name="Author" content="易思信网页工作室 www.eczone.net ">
    <!--
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
           网站设计制作 by 易思信网页工作室 www.eczone.net   
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
    --> 
      <title>服务模式 - 让算力如水般自由流动 - 聚算云科技</title>

<meta name="keywords" content='算力租赁服务,算力投资服务,算力接入服务,算力托管,算力投资,聚算云' />

<meta name="description" content='聚算云提供三大核心服务模式：算力租赁服务、算力投资服务和算力接入服务，为不同客户群体提供专业的算力解决方案。' />
    <link rel="shortcut icon" href="favicon.ico" />
    <link rel="stylesheet" href="static/css/animate.css" type="text/css" >
    <link rel="stylesheet" href="static/css/common.css" type="text/css" >
    <link rel="stylesheet" href="static/css/style.css" type="text/css" >
    <script type="text/javascript" src="static/js/jquery.min.js"></script>   
    <style type="text/css">
        /* SVG图标样式 */
        .icon {
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;
        }
        
        /* 产品板块公共样式 */
        .product-section {
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }
        
        .product-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 40px;
        }
        
        .product-content.reversed {
            flex-direction: row-reverse;
        }
        
        .product-left, .product-right {
            width: 48%;
        }
        
        .product-left img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .product-subtitle {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .product-desc {
            font-size: 16px;
            line-height: 1.8;
            color: #666;
            margin-bottom: 30px;
        }
        
        .feature-list {
            margin-bottom: 30px;
        }
        
        .feature-list li {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .feature-list li i {
            color: #2b64a7;
            margin-right: 10px;
            font-size: 18px;
        }
        
        .product-btn {
            margin-top: 30px;
        }
        
        .btn-more {
            display: inline-block;
            padding: 12px 30px;
            background: #2b64a7;
            color: #fff !important;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-more:hover {
            transform: none;
            box-shadow: none;
            color: #fff !important;
        }
        
        /* 添加以下覆盖所有可能的悬停效果 */
        .product-section *:hover {
            transform: none !important;
            box-shadow: none !important;
        }
        
        .pageAdvList1 li a:hover {
            background-color: transparent !important;
        }
        
        .solution-card, .feature-box, .grid-item, .trust-feature {
            transition: none;
        }
        
        /* 云服务解决方案样式 */
        .solution-cards {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .solution-card {
            width: 31%;
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .solution-card:hover {
            transform: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .card-icon {
            margin-bottom: 15px;
        }
        
        .card-icon i {
            font-size: 36px;
            color: #2b64a7;
        }
        
        .card-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .card-desc {
            color: #666;
            font-size: 14px;
        }
        
        /* 云服务解决方案移动端布局 */
        .mobile-content {
            display: none;
        }
        
        .cloud-case {
            background-color: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }
        
        .cloud-case-image {
            width: 100%;
        }
        
        .cloud-case-image img {
            max-width: 100%;
            display: block;
        }
        
        .cloud-case-content {
            padding: 30px;
        }
        
        .cloud-case-content h3 {
            font-size: 22px;
            margin-bottom: 15px;
            color: #333;
        }
        
        .cloud-case-content p {
            font-size: 16px;
            line-height: 1.7;
            color: #666;
            margin-bottom: 30px;
        }
        
        /* 边缘计算服务样式 */
        .product-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 50px;
            font-size: 18px;
            color: #666;
        }
        
        .edge-computing-wrapper {
            margin-top: 50px;
        }
        
        .edge-case {
            display: flex;
            align-items: center;
            margin-bottom: 60px;
            background-color: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        }
        
        .case-image {
            width: 40%;
        }
        
        .case-image img {
            max-width: 100%;
            display: block;
        }
        
        .case-content {
            width: 60%;
            padding: 30px;
        }
        
        .case-content h4 {
            font-size: 22px;
            margin-bottom: 15px;
            color: #333;
        }
        
        .case-content p {
            font-size: 16px;
            line-height: 1.7;
            color: #666;
        }
        
        .edge-features {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }
        
        .feature-box {
            width: 23%;
            text-align: center;
            padding: 30px 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .feature-box:hover {
            transform: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .feature-icon {
            margin-bottom: 15px;
        }
        
        .feature-icon i {
            font-size: 36px;
            color: #2b64a7;
        }
        
        .feature-box h5 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #333;
        }
        
        .feature-box p {
            color: #666;
            font-size: 14px;
        }
        
        /* AI算力服务样式 */
        .ai-service-wrapper {
            padding: 30px 0;
        }
        
        .ai-service-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 50px;
        }
        
        .ai-service-intro h3 {
            font-size: 28px;
            margin-bottom: 20px;
            color: #333;
        }
        
        .ai-service-intro p {
            font-size: 16px;
            line-height: 1.8;
            color: #666;
        }
        
        .ai-service-stats {
            display: flex;
            justify-content: center;
            margin-bottom: 60px;
        }
        
        .stat-item {
            width: 200px;
            text-align: center;
            margin: 0 30px;
        }
        
        .stat-number {
            font-size: 48px;
            font-weight: bold;
            color: #2b64a7;
            margin-bottom: 10px;
            line-height: 1;
        }
        
        .stat-number span {
            font-size: 24px;
        }
        
        .stat-desc {
            font-size: 16px;
            color: #666;
        }
        
        .ai-service-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .grid-item {
            width: 48%;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .grid-item:hover {
            transform: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .grid-item i {
            font-size: 36px;
            color: #2b64a7;
            margin-bottom: 15px;
            display: block;
        }
        
        .grid-item h5 {
            font-size: 20px;
            margin-bottom: 15px;
            color: #333;
        }
        
        .grid-item p {
            font-size: 16px;
            color: #666;
        }
        
        /* 算力资源池样式 */
        .resource-pool-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 50px;
        }
        
        .pool-left {
            width: 55%;
        }
        
        .pool-right {
            width: 40%;
        }
        
        .pool-right img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        }
        
        .pool-left h3 {
            font-size: 28px;
            margin-bottom: 20px;
            color: #333;
        }
        
        .pool-left p {
            font-size: 16px;
            line-height: 1.8;
            color: #666;
            margin-bottom: 30px;
        }
        
        .trust-features {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        
        .trust-feature {
            width: 48%;
            display: flex;
            align-items: flex-start;
            margin-bottom: 25px;
        }
        
        .trust-feature .feature-icon {
            margin-right: 15px;
            margin-bottom: 0;
        }
        
        .trust-feature .feature-icon i {
            font-size: 26px;
            color: #2b64a7;
        }
        
        .feature-text h5 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #333;
        }
        
        .feature-text p {
            font-size: 14px;
            color: #666;
            margin-bottom: 0;
        }
        
        /* 大屏幕响应式布局 */
        @media screen and (max-width: 1200px) and (min-width: 1025px) {
            .service-grid {
                max-width: 900px;
                gap: 15px;
            }
            
            .service-card {
                padding: 30px 20px;
            }
            
            .card-number {
                font-size: 100px;
                top: 20px;
            }
            
            .service-01 .card-number {
                left: 20px;
            }
            
            .service-03 .card-number {
                right: 20px;
            }
            
            .card-content h2 {
                font-size: 20px;
            }
            
            .card-content p {
                font-size: 13px;
            }
                }

        /* 竞争优势板块样式 */
        .advantages-container {
            margin-top: 60px;
            padding: 0 20px;
        }
        
        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .advantage-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 40px 35px;
            box-shadow: 0 12px 35px rgba(0,0,0,0.08), 0 4px 15px rgba(0,0,0,0.04);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.8);
            height: 280px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        
        .advantage-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(43, 100, 167, 0.02) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        .advantage-card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: 0 25px 60px rgba(43, 100, 167, 0.15), 0 8px 25px rgba(0,0,0,0.1);
            border-color: rgba(43, 100, 167, 0.2);
        }
        
        .advantage-card:hover::before {
            opacity: 1;
        }
        
        /* 不同卡片的特色背景 */
        .advantage-01 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .advantage-02 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .advantage-03 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .card-number {
            font-size: 120px;
            font-weight: 900;
            line-height: 0.8;
            opacity: 0.15;
            position: absolute;
            top: 20px;
            left: 25px;
            z-index: 1;
            font-family: 'Arial Black', Arial, sans-serif;
        }
        
        .advantage-01 .card-number {
            color: rgba(255, 255, 255, 0.2);
        }
        
        .advantage-02 .card-number {
            color: rgba(255, 255, 255, 0.2);
        }
        
        .advantage-03 .card-number {
            color: rgba(255, 255, 255, 0.2);
        }
        
        .card-content {
            position: relative;
            z-index: 2;
            padding-top: 80px;
        }
        
        .advantage-card h3 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -0.5px;
        }
        
        .advantage-card p {
            font-size: 16px;
            line-height: 1.7;
            margin: 0;
            opacity: 0.95;
        }
        
        .advantage-card:hover h3 {
            transform: translateY(-3px);
            transition: transform 0.3s ease;
        }
        
        .advantage-card:hover p {
            transform: translateY(-2px);
            transition: transform 0.3s ease;
        }

        /* 桌面端大屏幕优化 */
        @media screen and (min-width: 1025px) {
            .business-model-grid {
                grid-template-columns: 1fr 1fr;
                gap: 50px;
                max-width: 1400px;
            }
            
            .business-model-section {
                padding: 50px 40px;
            }
            
            .model-title {
                font-size: 32px;
                margin-bottom: 40px;
            }
        }
        
        /* 平板端布局 - 1列 */
        @media screen and (max-width: 1024px) and (min-width: 769px) {
            .business-model-container {
                padding: 0 30px;
            }
            
            .business-model-grid {
                grid-template-columns: 1fr !important;
                gap: 40px;
                max-width: 700px;
            }
            
            .business-model-section {
                padding: 40px 35px;
                border-radius: 22px;
            }
            
            .model-title {
                font-size: 28px !important;
                margin-bottom: 35px !important;
                line-height: 1.2 !important;
                /* 解决标题遮挡问题 */
                white-space: nowrap;
                overflow: visible;
                text-overflow: clip;
            }
            
            .model-title::after {
                width: 70px !important;
                bottom: -12px !important;
            }
            
            .model-title::before {
                width: 100px !important;
                bottom: -9px !important;
            }
            
            .model-cards {
                gap: 25px;
            }
            
            .model-card {
                padding: 28px 25px;
            }
            
            .model-card h4 {
                font-size: 20px;
            }
            
            .model-card p {
                font-size: 15px;
            }
            
            .icon-img {
                width: 52px !important;
                height: 52px !important;
            }
        }

        /* iPad端布局 - 一行2个 */
        @media screen and (max-width: 1024px) and (min-width: 769px) {
            .service-grid {
                grid-template-columns: 1fr 1fr !important;
                grid-template-rows: repeat(3, 280px) !important;
                gap: 20px;
                max-width: 800px;
                padding: 0 20px;
            }
            
            .service-card {
                padding: 25px 20px !important;
                min-height: auto !important;
            }
            
            .card-number {
                font-size: 80px !important;
                top: 15px !important;
            }
            
            .service-01 .card-number,
            .service-02 .card-number,
            .service-03 .card-number {
                left: 20px !important;
            }
            
            .service-01,
            .service-02,
            .service-03 {
                text-align: left !important;
            }
            
            .card-content {
                max-width: 85% !important;
                padding-top: 60px !important;
            }
            
            .card-content h2 {
                font-size: 18px !important;
                margin-bottom: 12px !important;
            }
            
            .card-content p {
                font-size: 12px !important;
                line-height: 1.5 !important;
            }
        }

        /* 响应式布局 */
        @media screen and (max-width: 1200px) {
            .w1600 {
                width: 90%;
            }
            
            .product-content, .resource-pool-content {
                flex-direction: column;
            }
            
            .product-content.reversed {
                flex-direction: column;
            }
            
            .product-left, .product-right, .pool-left, .pool-right {
                width: 100% !important;
                margin-bottom: 30px;
            }
            
            /* 确保图片在所有设备上完整显示 */
            .product-left img, .pool-right img {
                max-width: 100%;
                height: auto;
                display: block;
                margin: 0 auto;
            }
            
            .edge-case {
                flex-direction: column;
            }
            
            .case-image, .case-content {
                width: 100%;
            }
            
            .edge-features, .ai-service-stats {
                flex-wrap: wrap;
            }
            
            .feature-box {
                width: 48%;
                margin-bottom: 20px;
            }
            
            .stat-item {
                width: 30%;
                margin: 0 10px 20px;
            }
            
            /* 为云服务解决方案添加特定的移动端样式 */
            .solution-cards {
                gap: 15px;
            }
            
            /* 重置图片最大宽度 */
            img {
                max-width: 100%;
                height: auto;
            }
        }
        
        /* 特别添加专门应对1201px以下屏幕的样式 */
        @media screen and (max-width: 1201px) {
            .product-left, .product-right {
                width: 100% !important;
            }
            
            .product-content {
                flex-direction: column;
            }
        }
        
        /* 手机端布局 - 1列 */
        @media screen and (max-width: 768px) {
            .business-model-container {
                margin-top: 40px;
                padding: 0 20px;
            }
            
            .business-model-grid {
                grid-template-columns: 1fr !important;
                gap: 35px;
                max-width: 100%;
            }
            
            .business-model-section {
                padding: 35px 25px;
                border-radius: 20px;
            }
            
            .model-title {
                font-size: 24px !important;
                margin-bottom: 30px !important;
                line-height: 1.3 !important;
                /* 解决标题遮挡问题 */
                white-space: nowrap;
                overflow: visible;
                text-overflow: clip;
                letter-spacing: 0px;
            }
            
            .model-title::after {
                width: 60px !important;
                bottom: -10px !important;
                height: 3px !important;
            }
            
            .model-title::before {
                width: 90px !important;
                bottom: -8px !important;
            }
            
            .model-cards {
                gap: 22px;
            }
            
            .model-card {
                padding: 25px 20px;
                border-radius: 15px;
            }
            
            .model-card h4 {
                font-size: 18px !important;
                margin-bottom: 12px !important;
            }
            
            .model-card p {
                font-size: 14px !important;
                line-height: 1.7 !important;
            }
            
            .icon-img {
                width: 45px !important;
                height: 45px !important;
            }
            
            .card-icon::before {
                width: 60px !important;
                height: 60px !important;
            }
        }
        
        /* 小屏手机端优化 */
        @media screen and (max-width: 480px) {
            .business-model-container {
                padding: 0 15px;
            }
            
            .business-model-section {
                padding: 30px 20px;
            }
            
            .model-title {
                font-size: 22px !important;
                margin-bottom: 25px !important;
                /* 进一步优化标题显示 */
                white-space: normal;
                word-break: keep-all;
                text-align: center;
            }
            
            .model-title::after {
                width: 50px !important;
                bottom: -8px !important;
            }
            
            .model-title::before {
                width: 80px !important;
                bottom: -6px !important;
            }
            
            .model-card {
                padding: 22px 18px;
            }
            
            .model-card h4 {
                font-size: 17px !important;
            }
            
            .model-card p {
                font-size: 13px !important;
                line-height: 1.6 !important;
            }
            
            .icon-img {
                width: 42px !important;
                height: 42px !important;
            }
        }

        /* 竞争优势响应式布局 */
        
        /* 桌面端大屏幕 */
        @media screen and (min-width: 1025px) {
            .advantages-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 40px;
                max-width: 1200px;
            }
            
            .advantage-card {
                height: 280px;
                padding: 40px 35px;
            }
            
            .card-number {
                font-size: 120px;
                top: 20px;
                left: 25px;
            }
            
            .card-content {
                padding-top: 80px;
            }
            
            .advantage-card h3 {
                font-size: 28px;
            }
            
            .advantage-card p {
                font-size: 16px;
            }
        }
        
        /* 平板端布局 - 1列 */
        @media screen and (max-width: 1024px) and (min-width: 769px) {
            .advantages-container {
                padding: 0 30px;
            }
            
            .advantages-grid {
                grid-template-columns: 1fr !important;
                gap: 30px;
                max-width: 600px;
            }
            
            .advantage-card {
                height: auto;
                min-height: 200px;
                padding: 35px 30px;
                display: flex;
                flex-direction: row;
                align-items: center;
            }
            
            .card-number {
                font-size: 80px !important;
                position: relative !important;
                top: auto !important;
                left: auto !important;
                margin-right: 30px;
                opacity: 0.3 !important;
                flex-shrink: 0;
            }
            
            .card-content {
                padding-top: 0 !important;
                flex: 1;
            }
            
            .advantage-card h3 {
                font-size: 24px !important;
                margin-bottom: 15px !important;
            }
            
            .advantage-card p {
                font-size: 15px !important;
                line-height: 1.6 !important;
            }
        }
        
        /* 手机端布局 - 1列 */
        @media screen and (max-width: 768px) {
            .advantages-container {
                margin-top: 40px;
                padding: 0 20px;
            }
            
            .advantages-grid {
                grid-template-columns: 1fr !important;
                gap: 25px;
            }
            
            .advantage-card {
                height: auto;
                min-height: 180px;
                padding: 30px 25px;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
            }
            
            .card-number {
                font-size: 60px !important;
                position: relative !important;
                top: auto !important;
                left: auto !important;
                margin-bottom: 15px;
                opacity: 0.25 !important;
                align-self: flex-end;
            }
            
            .card-content {
                padding-top: 0 !important;
                width: 100%;
            }
            
            .advantage-card h3 {
                font-size: 22px !important;
                margin-bottom: 15px !important;
            }
            
            .advantage-card p {
                font-size: 14px !important;
                line-height: 1.6 !important;
            }
        }
        
        /* 小屏手机端优化 */
        @media screen and (max-width: 480px) {
            .advantages-container {
                padding: 0 15px;
            }
            
            .advantage-card {
                padding: 25px 20px;
                min-height: 160px;
            }
            
            .card-number {
                font-size: 50px !important;
                margin-bottom: 10px;
            }
            
            .advantage-card h3 {
                font-size: 20px !important;
                margin-bottom: 12px !important;
            }
            
            .advantage-card p {
                font-size: 13px !important;
                line-height: 1.5 !important;
            }
        }

        /* 手机端布局 - 一行1个 */
        @media screen and (max-width: 768px) {
            .service-grid-container {
                margin-top: 30px !important;
                padding: 0 10px !important;
            }
            
            .service-grid {
                grid-template-columns: 1fr !important;
                grid-template-rows: repeat(6, auto) !important;
                gap: 20px !important;
                padding: 0 !important;
                max-width: 100% !important;
            }
            
            .service-card {
                min-height: auto !important;
                padding: 30px 25px !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: flex-start !important;
                position: relative !important;
                border-radius: 15px !important;
            }
            
            /* 有文字的服务卡片 */
            .service-01,
            .service-02,
            .service-03 {
                min-height: 320px !important;
                padding: 35px 25px 30px !important;
            }
            
            /* 图片卡片 */
            .service-datacenter,
            .service-puzzle,
            .service-robot {
                min-height: 200px !important;
                padding: 0 !important;
                position: relative !important;
                overflow: hidden !important;
            }
            
            /* 手机端图片背景优化 */
            .datacenter-bg,
            .puzzle-bg,
            .robot-bg {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-size: cover !important;
                background-position: center center !important;
                border-radius: inherit !important;
            }
            
            .card-number {
                font-size: 80px !important;
                top: 20px !important;
                opacity: 0.3 !important;
                font-weight: 100 !important;
            }
            
            .service-01 .card-number,
            .service-02 .card-number,
            .service-03 .card-number {
                left: 20px !important;
            }
            
            .service-01,
            .service-02,
            .service-03 {
                text-align: left !important;
            }
            
            .card-content {
                max-width: 100% !important;
                padding-top: 70px !important;
                z-index: 2 !important;
                position: relative !important;
            }
            
            .card-content h2 {
                font-size: 24px !important;
                margin-bottom: 20px !important;
                line-height: 1.3 !important;
                font-weight: bold !important;
            }
            
            .card-content p {
                font-size: 16px !important;
                line-height: 1.7 !important;
                opacity: 0.95 !important;
                text-align: justify !important;
            }
            
        }
        
        @media screen and (max-width: 992px) {
            .product-section {
                padding: 50px 0;
            }
            
            /* 云服务解决方案移动端样式 */
            /* 隐藏桌面布局，显示移动端布局 */
            #investment-service .product-content {
                display: none;
            }
            
            #investment-service .mobile-content {
                display: block !important;
            }
            
            .solution-cards {
                flex-direction: column;
            }
            
            .solution-card {
                width: 100%;
                margin-bottom: 20px;
            }
            
            .feature-box, .grid-item, .trust-feature {
                width: 100%;
            }
            
            .ai-service-stats {
                flex-direction: column;
                align-items: center;
            }
            
            .stat-item {
                width: 80%;
                margin-bottom: 30px;
            }
            
            /* 云服务解决方案移动端布局优化 */
            .cloud-case-content {
                padding: 20px;
            }
            
            .cloud-case-content h3 {
                font-size: 20px;
            }
            
            .cloud-case-content p {
                font-size: 14px;
                margin-bottom: 20px;
            }
        }
        
        /* 商业模式超小屏幕优化 */
        @media screen and (max-width: 480px) {
            .business-model-container {
                margin-top: 25px;
            }
            
            .business-model-grid {
                gap: 20px;
                padding: 0 10px;
            }
            
            .business-model-section {
                padding: 20px 15px;
                border-radius: 12px;
            }
            
            .model-title {
                font-size: 18px;
                margin-bottom: 18px;
            }
            
            .model-title::after {
                width: 50px;
                height: 2px;
            }
            
            .model-cards {
                gap: 15px;
            }
            
            .model-card {
                padding: 15px;
                border-radius: 10px;
            }
            
            .model-card h4 {
                font-size: 15px;
                margin-bottom: 8px;
            }
            
            .model-card p {
                font-size: 12px;
                line-height: 1.5;
            }
            
            .card-icon {
                margin-bottom: 12px;
            }
            
            .card-icon .icon-img {
                width: 35px !important;
                height: 35px !important;
            }
        }

        /* 超小屏幕优化 */
        @media screen and (max-width: 480px) {
            .service-grid-container {
                padding: 0 8px !important;
                margin-top: 25px !important;
            }
            
            .service-grid {
                gap: 15px !important;
                grid-template-columns: 1fr !important;
                grid-template-rows: repeat(6, auto) !important;
            }
            
            .service-card {
                padding: 25px 20px !important;
                border-radius: 12px !important;
            }
            
            /* 有文字的服务卡片 */
            .service-01,
            .service-02,
            .service-03 {
                min-height: 280px !important;
                padding: 30px 20px 25px !important;
            }
            
            /* 图片卡片 */
            .service-datacenter,
            .service-puzzle,
            .service-robot {
                min-height: 180px !important;
                padding: 0 !important;
                position: relative !important;
                overflow: hidden !important;
            }
            
            .card-number {
                font-size: 70px !important;
                top: 15px !important;
                opacity: 0.25 !important;
            }
            
            .service-01 .card-number,
            .service-02 .card-number,
            .service-03 .card-number {
                left: 15px !important;
            }
            
            .card-content {
                padding-top: 60px !important;
            }
            
            .card-content h2 {
                font-size: 20px !important;
                margin-bottom: 16px !important;
                line-height: 1.3 !important;
            }
            
            .card-content p {
                font-size: 14px !important;
                line-height: 1.6 !important;
            }
            
            .product-subtitle {
                font-size: 20px;
            }
            
            .product-desc {
                font-size: 14px;
            }
            
            .solution-card {
                padding: 15px;
            }
            
            .card-title {
                font-size: 16px;
            }
            
            .card-desc {
                font-size: 12px;
            }
            
            .btn-more {
                padding: 10px 20px;
                font-size: 14px;
            }
            
            .edge-case .case-content {
                padding: 20px;
            }
            
            .case-content h4 {
                font-size: 18px;
            }
            
            .case-content p {
                font-size: 14px;
            }
            
            .ai-service-intro h3 {
                font-size: 22px;
            }
            
            .stat-number {
                font-size: 36px;
            }
            
            .stat-number span {
                font-size: 18px;
            }
            
            .pool-left h3 {
                font-size: 22px;
            }
        }
        
        /* 极小屏幕优化 (iPhone SE等) */
        @media screen and (max-width: 375px) {
            .service-grid-container {
                padding: 0 5px !important;
                margin-top: 20px !important;
            }
            
            .service-grid {
                gap: 12px !important;
                grid-template-columns: 1fr !important;
                grid-template-rows: repeat(6, auto) !important;
            }
            
            /* 有文字的服务卡片 */
            .service-01,
            .service-02,
            .service-03 {
                min-height: 260px !important;
                padding: 25px 15px 20px !important;
            }
            
            .card-number {
                font-size: 60px !important;
                top: 12px !important;
            }
            
            .service-01 .card-number,
            .service-02 .card-number,
            .service-03 .card-number {
                left: 12px !important;
            }
            
            .card-content {
                padding-top: 55px !important;
            }
            
            .card-content h2 {
                font-size: 18px !important;
                margin-bottom: 14px !important;
            }
            
            .card-content p {
                font-size: 13px !important;
                line-height: 1.5 !important;
            }
        }
        
        /* 产品矩阵板块样式 */
        .product-matrix-section {
            padding: 80px 0;
            background-color: #f8f9fa;
            position: relative;
            overflow: hidden;
        }
        
        .matrix-subtitle {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 60px;
        }
        
        .matrix-subtitle p {
            font-size: 18px;
            line-height: 1.8;
            color: #666;
        }
        
        .matrix-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 40px;
        }
        
        .matrix-left, .matrix-right {
            flex: 1;
            max-width: 380px;
        }
        
        .matrix-center {
            flex: 0 0 400px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .matrix-item {
            margin-bottom: 40px;
            position: relative;
            padding-left: 60px;
        }
        
        .matrix-item:last-child {
            margin-bottom: 0;
        }
        
        .item-number {
            position: absolute;
            left: 0;
            top: 0;
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #2B64AD, #5B94D7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 4px 10px rgba(43, 100, 167, 0.2);
        }
        
        .item-number.red {
            background: linear-gradient(135deg, #FF8000, #FFB366);
            box-shadow: 0 4px 10px rgba(255, 128, 0, 0.2);
        }
        
        .matrix-item h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #333;
            font-weight: 600;
        }
        
        .matrix-item p {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }
        
        /* 中间图形样式 */
        .matrix-graphic {
            width: 100%;
            max-width: 400px;
            position: relative;
        }
        
        .matrix-image-container {
            position: relative;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .matrix-main-image {
            width: 100%;
            max-width: 400px;
            height: auto;
            display: block;
            animation: slowRotate 20s linear infinite;
        }
        
        /* 数字标记覆盖层 */
        .number-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .number-marker {
            position: absolute;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            animation: pulseGlow 2s ease-in-out infinite;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        }
        
        /* 数字标记位置和颜色 */
        .marker-01 {
            top: 8%;
            left: 12%;
            background: linear-gradient(135deg, #7BA7D7, #5B94D7);
        }
        
        .marker-02 {
            top: 8%;
            right: 12%;
            background: linear-gradient(135deg, #FF8000, #FFB366);
        }
        
        .marker-03 {
            top: 50%;
            left: 2%;
            background: linear-gradient(135deg, #2B64AD, #4A7BC8);
        }
        
        .marker-04 {
            top: 50%;
            right: 2%;
            background: linear-gradient(135deg, #D42C2C, #FF6B6B);
        }
        
        .marker-05 {
            bottom: 8%;
            left: 12%;
            background: linear-gradient(135deg, #5B94D7, #7BA7D7);
        }
        
        .marker-06 {
            bottom: 8%;
            right: 12%;
            background: linear-gradient(135deg, #FFB366, #FFCC99);
        }
        
        /* 流动效果覆盖层 */
        .flow-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .flow-particle {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            opacity: 0.8;
            box-shadow: 0 0 6px rgba(255, 255, 255, 0.6);
        }
        
        .flow-particle-1 {
            background: radial-gradient(circle, #5B94D7, #2B64AD);
            top: 20%;
            left: 15%;
            animation: flowPath1 6s linear infinite;
        }
        
        .flow-particle-2 {
            background: radial-gradient(circle, #FF8000, #D42C2C);
            top: 20%;
            right: 15%;
            animation: flowPath2 7s linear infinite;
        }
        
        .flow-particle-3 {
            background: radial-gradient(circle, #7BA7D7, #5B94D7);
            top: 50%;
            left: 10%;
            animation: flowPath3 5s linear infinite;
        }
        
        .flow-particle-4 {
            background: radial-gradient(circle, #FFB366, #FF8000);
            top: 50%;
            right: 10%;
            animation: flowPath4 8s linear infinite;
        }
        
        .flow-particle-5 {
            background: radial-gradient(circle, #4A7BC8, #2B64AD);
            bottom: 20%;
            left: 15%;
            animation: flowPath5 6.5s linear infinite;
        }
        
        .flow-particle-6 {
            background: radial-gradient(circle, #FFCC99, #FFB366);
            bottom: 20%;
            right: 15%;
            animation: flowPath6 7.5s linear infinite;
        }
        
        /* 动画定义 */
        @keyframes slowRotate {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        @keyframes pulseGlow {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            }
            50% {
                transform: scale(1.1);
                box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            }
        }
        
        @keyframes flowPath1 {
            0% {
                transform: translate(0, 0) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            25% {
                transform: translate(80px, 30px) scale(1.2);
            }
            50% {
                transform: translate(150px, 80px) scale(1);
            }
            75% {
                transform: translate(200px, 150px) scale(1.2);
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translate(250px, 200px) scale(0);
                opacity: 0;
            }
        }
        
        @keyframes flowPath2 {
            0% {
                transform: translate(0, 0) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            25% {
                transform: translate(-80px, 30px) scale(1.2);
            }
            50% {
                transform: translate(-150px, 80px) scale(1);
            }
            75% {
                transform: translate(-200px, 150px) scale(1.2);
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translate(-250px, 200px) scale(0);
                opacity: 0;
            }
        }
        
        @keyframes flowPath3 {
            0% {
                transform: translate(0, 0) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            33% {
                transform: translate(120px, -20px) scale(1.2);
            }
            66% {
                transform: translate(180px, 40px) scale(1);
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translate(220px, 80px) scale(0);
                opacity: 0;
            }
        }
        
        @keyframes flowPath4 {
            0% {
                transform: translate(0, 0) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            33% {
                transform: translate(-120px, -20px) scale(1.2);
            }
            66% {
                transform: translate(-180px, 40px) scale(1);
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translate(-220px, 80px) scale(0);
                opacity: 0;
            }
        }
        
        @keyframes flowPath5 {
            0% {
                transform: translate(0, 0) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            25% {
                transform: translate(60px, -50px) scale(1.2);
            }
            50% {
                transform: translate(140px, -100px) scale(1);
            }
            75% {
                transform: translate(180px, -150px) scale(1.2);
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translate(220px, -200px) scale(0);
                opacity: 0;
            }
        }
        
        @keyframes flowPath6 {
            0% {
                transform: translate(0, 0) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            25% {
                transform: translate(-60px, -50px) scale(1.2);
            }
            50% {
                transform: translate(-140px, -100px) scale(1);
            }
            75% {
                transform: translate(-180px, -150px) scale(1.2);
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translate(-220px, -200px) scale(0);
                opacity: 0;
            }
        }
        
        /* 响应式布局 */
        @media screen and (max-width: 1200px) {
            .matrix-content {
                flex-direction: column;
                gap: 60px;
            }
            
            .matrix-left, .matrix-right {
                max-width: 600px;
                width: 100%;
            }
            
            .matrix-center {
                order: -1;
                margin-bottom: 40px;
            }
            
            .matrix-graphic {
                max-width: 350px;
            }
        }
        
        @media screen and (max-width: 768px) {
            .product-matrix-section {
                padding: 50px 0;
            }
            
            .matrix-subtitle p {
                font-size: 16px;
            }
            
            .matrix-item {
                padding-left: 50px;
            }
            
            .item-number {
                width: 38px;
                height: 38px;
                font-size: 14px;
            }
            
            .matrix-item h3 {
                font-size: 18px;
            }
            
            .matrix-item p {
                font-size: 13px;
            }
            
            .matrix-graphic {
                max-width: 300px;
            }
        }
        
        @media screen and (max-width: 480px) {
            .matrix-item {
                padding-left: 45px;
                margin-bottom: 30px;
            }
            
            .item-number {
                width: 35px;
                height: 35px;
            }
            
            .matrix-item h3 {
                font-size: 16px;
            }
            
            .matrix-graphic {
                max-width: 250px;
            }
        }
        
        /* 服务模式网格布局样式 */
        .service-grid-container {
            margin-top: 50px;
            padding: 0 20px;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 300px);
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .service-card {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        /* 01 算力租赁服务 - 蓝色背景 */
        .service-01 {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            padding: 40px 30px;
            text-align: right;
        }
        
        /* 02 算力投资服务 - 红色背景 */
        .service-02 {
            background: linear-gradient(135deg, #c31432, #e53e3e);
            padding: 40px 30px;
            text-align: center;
        }
        
        /* 03 算力接入服务 - 蓝色背景 */
        .service-03 {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            padding: 40px 30px;
            text-align: left;
        }
        
        /* 数据中心背景 */
        .service-datacenter {
            background: linear-gradient(135deg, #667eea, #764ba2);
            position: relative;
        }
        
        /* 智能拼图背景 */
        .service-puzzle {
            background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
            position: relative;
        }
        
        /* AI机器人背景 */
        .service-robot {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            position: relative;
        }
        
        .card-number {
            position: absolute;
            top: 30px;
            font-size: 120px;
            font-weight: 100;
            opacity: 0.3;
            line-height: 1;
            z-index: 1;
        }
        
        .service-01 .card-number {
            left: 30px;
        }
        
        .service-02 .card-number {
            left: 50%;
            transform: translateX(-50%);
        }
        
        .service-03 .card-number {
            right: 30px;
        }
        
        .card-content {
            position: relative;
            z-index: 2;
            max-width: 80%;
        }
        
        .card-content h2 {
            font-size: 24px;
            margin-bottom: 15px;
            font-weight: bold;
            line-height: 1.2;
        }
        
        .card-content p {
            font-size: 14px;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        /* 背景图片区域样式 - 布满方式 */
        .puzzle-bg, .datacenter-bg, .robot-bg {
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            position: absolute;
            top: 0;
            left: 0;
            border-radius: inherit;
            overflow: hidden;
        }
        
        /* 确保图片卡片容器为相对定位 */
        .service-datacenter,
        .service-puzzle,
        .service-robot {
            position: relative;
            overflow: hidden;
        }
        

        
        /* 插入真实图片 - 布满显示 */
        .datacenter-bg {
            background-image: url('static/picture/platform-illustration.png');
            background-size: cover;
            background-position: center center;
            background-attachment: scroll;
        }
        
        .puzzle-bg {
            background-image: url('static/picture/cloud-solution.png');
            background-size: cover;
            background-position: center center;
            background-attachment: scroll;
        }
        
        .robot-bg {
            background-image: url('static/picture/edge-case.png');
            background-size: cover;
            background-position: center center;
            background-attachment: scroll;
        }

        /* 商业模式板块样式 - 优化布局 */
        .business-model-container {
            margin-top: 60px;
            padding: 0 20px;
        }
        
        .business-model-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .business-model-section {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
            border-radius: 25px;
            padding: 50px 40px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.08), 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(43, 100, 167, 0.1);
        }
        
        .business-model-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #2b64a7, #5B94D7, #7BA7D7, #2b64a7);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        .business-model-section::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(43, 100, 167, 0.03) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        .business-model-section:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 60px rgba(43, 100, 167, 0.2), 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .business-model-section:hover::after {
            opacity: 1;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .model-title {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(135deg, #2b64a7, #5B94D7, #7BA7D7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            letter-spacing: -0.5px;
        }
        
        .model-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #2b64a7, #5B94D7, #7BA7D7);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(43, 100, 167, 0.3);
        }
        
        .model-title::before {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(43, 100, 167, 0.2), transparent);
        }
        
        .model-cards {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .model-card {
            background: linear-gradient(135deg, #ffffff 0%, #fdfdfd 100%);
            border-radius: 18px;
            padding: 35px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.06), 0 1px 8px rgba(0,0,0,0.05);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(43, 100, 167, 0.08);
            position: relative;
            overflow: hidden;
        }
        
        .model-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(180deg, #2b64a7, #5B94D7, #7BA7D7);
            transform: scaleY(0);
            transform-origin: top;
            transition: transform 0.5s ease;
        }
        
        .model-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: linear-gradient(135deg, rgba(43, 100, 167, 0.02) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        .model-card:hover {
            transform: translateY(-8px) translateX(5px) scale(1.02);
            box-shadow: 0 20px 50px rgba(43, 100, 167, 0.12), 0 5px 20px rgba(0,0,0,0.08);
            border-color: rgba(43, 100, 167, 0.2);
        }
        
        .model-card:hover::before {
            transform: scaleY(1);
        }
        
        .model-card:hover::after {
            opacity: 1;
        }
        
        .card-icon {
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            position: relative;
        }
        
        .card-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 70px;
            height: 70px;
            background: radial-gradient(circle, rgba(43, 100, 167, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            opacity: 0;
            transition: all 0.5s ease;
        }
        
        .icon-img {
            width: 55px;
            height: 55px;
            object-fit: contain;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            filter: drop-shadow(0 4px 12px rgba(43, 100, 167, 0.15));
            position: relative;
            z-index: 2;
        }
        
        .model-card:hover .card-icon::before {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2);
        }
        
        .model-card:hover .icon-img {
            transform: scale(1.15) rotate(5deg);
            filter: drop-shadow(0 8px 20px rgba(43, 100, 167, 0.25)) brightness(1.1);
        }
        
        .model-card h4 {
            font-size: 22px;
            font-weight: 700;
            background: linear-gradient(135deg, #2b64a7, #333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            line-height: 1.3;
            letter-spacing: -0.3px;
            transition: all 0.3s ease;
        }
        
        .model-card:hover h4 {
            transform: translateX(3px);
        }
        
        .model-card p {
            font-size: 16px;
            line-height: 1.8;
            color: #555;
            margin: 0;
            transition: all 0.3s ease;
        }
        
        .model-card:hover p {
            color: #444;
            transform: translateX(2px);
        }

        /* 产品列表页样式 */
        .product-section {
            padding: 80px 0;
            position: relative;
        }

        .product-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 40px;
        }

        .product-content.reversed {
            flex-direction: row-reverse;
        }

        .product-left, .product-right {
            width: 48%;
        }

        .product-left img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        /* 导航项活动状态样式 */
        .pageAdvList1 a.active,
        .pageAdvList1 a:hover {
            background-color: transparent;
            color: #2b64a7;
            transform: none;
            box-shadow: none;
        }
        
        .pageAdvList1 a.active h1,
        .pageAdvList1 a:hover h1,
        .pageAdvList1 a.active p,
        .pageAdvList1 a:hover p {
            color: inherit;
        }
    </style>
    <!-- SVG图标定义 -->
    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
        <!-- 算力调度平台图标 -->
        <symbol id="icon-scheduling" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M3,3H21V5H3V3M3,7H15V9H3V7M3,11H21V13H3V11M3,15H15V17H3V15M3,19H21V21H3V19Z" />
        </symbol>
        <symbol id="icon-optimization" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M21,16.5C21,16.88 20.79,17.21 20.47,17.38L12.57,21.82C12.41,21.94 12.21,22 12,22C11.79,22 11.59,21.94 11.43,21.82L3.53,17.38C3.21,17.21 3,16.88 3,16.5V7.5C3,7.12 3.21,6.79 3.53,6.62L11.43,2.18C11.59,2.06 11.79,2 12,2C12.21,2 12.41,2.06 12.57,2.18L20.47,6.62C20.79,6.79 21,7.12 21,7.5V16.5M12,4.15L6.04,7.5L12,10.85L17.96,7.5L12,4.15M5,15.91L11,19.29V12.58L5,9.21V15.91M19,15.91V9.21L13,12.58V19.29L19,15.91Z" />
        </symbol>
        <symbol id="icon-network" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M15,20A1,1 0 0,0 14,19H13V17H17A2,2 0 0,0 19,15V5A2,2 0 0,0 17,3H7A2,2 0 0,0 5,5V15A2,2 0 0,0 7,17H11V19H10A1,1 0 0,0 9,20H2V22H9A1,1 0 0,0 10,23H14A1,1 0 0,0 15,22H22V20H15M7,15V5H17V15H7Z" />
        </symbol>
        <symbol id="icon-management" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
        </symbol>
        
        <!-- 云服务解决方案图标 -->
        <symbol id="icon-billing" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M3,15H21V13H3V15M3,19H21V17H3V19M3,11H21V9H3V11M3,5V7H21V5H3Z" />
        </symbol>
        <symbol id="icon-devices" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M21,16H3V4H21M21,2H3C1.89,2 1,2.89 1,4V16A2,2 0 0,0 3,18H10V20H8V22H16V20H14V18H21A2,2 0 0,0 23,16V4C23,2.89 22.1,2 21,2Z" />
        </symbol>
        <symbol id="icon-service" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M10.5,15.97L10.91,18.41C10.65,18.55 10.23,18.68 9.67,18.8C9.1,18.93 8.43,19 7.66,19C5.45,18.96 3.79,18.3 2.68,17.04C1.56,15.77 1,14.16 1,12.21C1.05,9.9 1.72,8.13 3,6.89C4.32,5.64 5.96,5 7.94,5C8.69,5 9.34,5.07 9.88,5.19C10.42,5.31 10.82,5.44 11.08,5.59L10.5,8.08L9.44,7.74C9.04,7.64 8.58,7.59 8.05,7.59C6.89,7.58 5.93,7.95 5.18,8.69C4.42,9.42 4.03,10.54 4,12.03C4,13.39 4.37,14.45 5.08,15.23C5.79,16 6.79,16.4 8.07,16.41L9.4,16.29C9.83,16.21 10.19,16.1 10.5,15.97M11,11H13V9H15V11H17V13H15V15H13V13H11V11Z" />
        </symbol>
        
        <!-- 边缘计算服务图标 -->
        <symbol id="icon-latency" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
        </symbol>
        <symbol id="icon-bandwidth" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M13,9.5H18V7.5H13V9.5M13,16.5H18V14.5H13V16.5M19,21H5A2,2 0 0,1 3,19V5A2,2 0 0,1 5,3H19A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21M6,11H11V6H6V11M7,7H10V10H7V7M6,18H11V13H6V18M7,14H10V17H7V14Z" />
        </symbol>
        <symbol id="icon-privacy" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" />
        </symbol>
        <symbol id="icon-elastic" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M17,17H7V14L3,18L7,22V19H19V13H17M7,7H17V10L21,6L17,2V5H5V11H7V7Z" />
        </symbol>
        
        <!-- AI算力服务图标 -->
        <symbol id="icon-training" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M5,9.5L7.5,14H2.5L5,9.5M3,4H7V8H3V4M5,20A2,2 0 0,0 7,18A2,2 0 0,0 5,16A2,2 0 0,0 3,18A2,2 0 0,0 5,20M9,5V7H21V5H9M9,19H21V17H9V19M9,13H21V11H9V13Z" />
        </symbol>
        <symbol id="icon-inference" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M7.5,2C5.71,3.15 4.5,5.18 4.5,7.5C4.5,9.82 5.71,11.85 7.53,13C4.46,13 2,10.54 2,7.5A5.5,5.5 0 0,1 7.5,2M19.07,3.5L20.5,4.93L4.93,20.5L3.5,19.07L19.07,3.5M12.89,5.93L11.41,5L9.97,6L10.39,4.3L9,3.24L10.75,3.12L11.33,1.47L12,3.1L13.73,3.13L12.38,4.26L12.89,5.93M9.59,9.54L8.43,8.81L7.31,9.59L7.65,8.27L6.56,7.44L7.92,7.35L8.37,6.06L8.88,7.33L10.24,7.36L9.19,8.23L9.59,9.54M19,13.5A5.5,5.5 0 0,1 13.5,19C12.28,19 11.15,18.6 10.24,17.93L17.93,10.24C18.6,11.15 19,12.28 19,13.5M14.6,20.08L17.37,18.93L17.13,22.28L14.6,20.08M18.93,17.38L20.08,14.61L22.28,17.15L18.93,17.38M20.08,12.42L18.94,9.64L22.28,9.88L20.08,12.42M9.63,18.93L12.4,20.08L9.87,22.27L9.63,18.93Z" />
        </symbol>
        <symbol id="icon-data" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M12,3C7.58,3 4,4.79 4,7C4,9.21 7.58,11 12,11C16.42,11 20,9.21 20,7C20,4.79 16.42,3 12,3M4,9V12C4,14.21 7.58,16 12,16C16.42,16 20,14.21 20,12V9C20,11.21 16.42,13 12,13C7.58,13 4,11.21 4,9M4,14V17C4,19.21 7.58,21 12,21C16.42,21 20,19.21 20,17V14C20,16.21 16.42,18 12,18C7.58,18 4,16.21 4,14Z" />
        </symbol>
        <symbol id="icon-security" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M12,12H19C18.47,16.11 15.72,19.78 12,20.92V12H5V6.3L12,3.19M12,1L3,5V11C3,16.55 6.84,21.73 12,23C17.16,21.73 21,16.55 21,11V5L12,1Z" />
        </symbol>
        
        <!-- 算力资源池图标 -->
        <symbol id="icon-income" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M4,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M4,6V18H11V6H4M20,18V6H18.76C19,6.54 18.95,7.07 18.95,7.13C18.88,7.8 18.41,8.5 18.24,8.74L15.91,11.3L19.23,11.65L19.24,12.56C19.24,12.83 19.24,13.11 19.23,13.38L19.21,14.31L17.72,14.5C17.66,14.5 17.59,14.5 17.5,14.53L15.77,15.19V18M13.5,9.5A1.5,1.5 0 0,0 12,11A1.5,1.5 0 0,0 13.5,12.5A1.5,1.5 0 0,0 15,11A1.5,1.5 0 0,0 13.5,9.5Z" />
        </symbol>
        <symbol id="icon-blockchain" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M12,1.75L5.75,12.25L12,16L18.25,12.25L12,1.75M5.75,13.5L12,22.25L18.25,13.5L12,17.25L5.75,13.5Z" />
        </symbol>
        <symbol id="icon-buyback" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M12,13A5,5 0 0,1 7,8H9A3,3 0 0,0 12,11A3,3 0 0,0 15,8H17A5,5 0 0,1 12,13M12,3A3,3 0 0,1 15,6H9A3,3 0 0,1 12,3M19,6H17A5,5 0 0,0 12,1A5,5 0 0,0 7,6H5C3.89,6 3,6.89 3,8V20A2,2 0 0,0 5,22H19A2,2 0 0,0 21,20V8C21,6.89 20.1,6 19,6Z" />
        </symbol>
        <symbol id="icon-monitor" viewBox="0 0 24 24">
            <path fill="#2b64a7" d="M13,2.05V5.08C16.39,5.57 19,8.47 19,12C19,15.87 15.87,19 12,19C8.47,19 5.57,16.39 5.08,13H2.05C2.56,17.95 6.81,22 12,22C17.51,22 22,17.51 22,12C22,6.81 17.95,2.56 13,2.05M12,6A6,6 0 0,0 6,12C6,14.22 7.21,16.16 9,17.2V14.5C8.39,13.75 8,12.92 8,12C8,9.79 9.79,8 12,8C14.21,8 16,9.79 16,12C16,14.21 14.21,16 12,16H11V18H12A8,8 0 0,0 20,12A8,8 0 0,0 12,4V6Z" />
        </symbol>
    </svg>
</head>
<body>
<section class="wrapper">

    <header class="header"> <a href="/" class="logo"> <img class="hide" src="static/picture/logo.png" alt="" /> <img class="show" src="static/picture/logo2.png" alt="" /> </a>
        <ul class="navs">
          <li class=""><a href="/">首页</a></li>
          <li ><a href="page-abouts.html">关于我们<i class="iconfont v">&#xe6b9;</i></a>
            <div class="navs-menus-box">
              <ol class="navs-menus">
                <li><a href="#pageInfo">公司简介<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#pageYJ">企业使命与愿景<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#pageAdvInfo">企业文化<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#pageTeam">团队介绍<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#pageInvestor">投资者关系<i class="iconfont">&#xe631;</i></a></li>
              </ol>
            </div>
          </li>
          <li ><a href="#">产品与服务<i class="iconfont v">&#xe6b9;</i></a>
            <div class="navs-menus-box">
              <ol class="navs-menus">
                <li><a href="#">算力调度平台<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">云服务解决方案<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">边缘计算服务<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">AI算力服务<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">算力资源池<i class="iconfont">&#xe631;</i></a></li>
              </ol>
            </div>
          </li>
          <li ><a href="solution-cases.html">解决方案<i class="iconfont v">&#xe6b9;</i></a>
            <div class="navs-menus-box">
              <ol class="navs-menus">
                <li><a href="#">电竞云网吧<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">AI眼镜<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">企业数字化转型<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">科研机构高性能计算<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">数据分析与大数据处理<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">媒体内容制作与渲染<i class="iconfont">&#xe631;</i></a></li>
              </ol>
            </div>
          </li>
          <li ><a href="hyyy-hyyy.html">成功案例<i class="iconfont v">&#xe6b9;</i></a>
            <div class="navs-menus-box">
              <ol class="navs-menus">
                <li><a href="#">互联网行业<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">人工智能<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">制造业<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">金融服务<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">教育与科研<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">政府与公共服务<i class="iconfont">&#xe631;</i></a></li>
              </ol>
            </div>
          </li>
          <li ><a href="#">技术与创新<i class="iconfont v">&#xe6b9;</i></a>
            <div class="navs-menus-box">
              <ol class="navs-menus">
                <li><a href="#">技术白皮书<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">研发团队<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">专利与知识产权<i class="iconfont">&#xe631;</i></a></li>
              </ol>
            </div>
          </li>
          <li ><a href="#">合作与生态<i class="iconfont v">&#xe6b9;</i></a>
            <div class="navs-menus-box">
              <ol class="navs-menus">
                <li><a href="#">合作伙伴<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">生态体系<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">合作模式<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
              </ol>
            </div>
          </li>
          <li ><a href="#">资源中心<i class="iconfont v">&#xe6b9;</i></a>
            <div class="navs-menus-box">
              <ol class="navs-menus">
                <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">产品手册<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">教程与指南<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">行业洞察<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">算力知识库<i class="iconfont">&#xe631;</i></a></li>
              </ol>
            </div>
          </li>
          <li><a href="contact-contacts.html">联系我们<i class="iconfont v">&#xe6b9;</i></a>
            <div class="navs-menus-box">
              <ol class="navs-menus">
                <li><a href="#">销售咨询<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">技术支持<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">媒体联系<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
                <li><a href="#">办公地点<i class="iconfont">&#xe631;</i></a></li>
              </ol>
            </div>
          </li>
        </ul>
        <div class="header-menu">
          <div class="header-search"> <span class="iconfont">&nbsp;</span> </div>
          <div class="menu-btn">
            <p>MENU</p>
            <div class="menubtn"> <span></span> </div>
          </div>
          <div class="menu-flex">
            <div class="menu-bg"></div>
            <div class="menu-right">
              <ul class="menu-list">
                <li><a href="/">首页</a></li>
                <li ><a href="page-abouts.html">关于我们<em></em></a>
                  <ol class="menu-leval">
                    <li><a href="#pageInfo">公司简介<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#pageYJ">企业使命与愿景<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#pageAdvInfo">企业文化<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#pageTeam">团队介绍<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#pageInvestor">投资者关系<i class="iconfont">&#xe624;</i></a></li>
                  </ol>
                </li>
                <li ><a href="#">产品与服务<em></em></a>
                  <ol class="menu-leval">
                    <li><a href="#">算力调度平台<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">云服务解决方案<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">边缘计算服务<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">AI算力服务<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">算力资源池<i class="iconfont">&#xe624;</i></a></li>
                  </ol>
                </li>
                <li ><a href="solution-cases.html">解决方案<em></em></a>
                  <ol class="menu-leval">
                    <li><a href="#">电竞云网吧<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">AI眼镜<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">企业数字化转型<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">科研机构高性能计算<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">数据分析与大数据处理<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">媒体内容制作与渲染<i class="iconfont">&#xe624;</i></a></li>
                  </ol>
                </li>
                <li ><a href="hyyy-hyyy.html">成功案例<em></em></a>
                  <ol class="menu-leval">
                    <li><a href="#">互联网行业<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">人工智能<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">制造业<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">金融服务<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">教育与科研<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">政府与公共服务<i class="iconfont">&#xe624;</i></a></li>
                  </ol>
                </li>
                <li ><a href="#">技术与创新<em></em></a>
                  <ol class="menu-leval">
                    <li><a href="#">技术白皮书<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">研发团队<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">专利与知识产权<i class="iconfont">&#xe624;</i></a></li>
                  </ol>
                </li>
                <li ><a href="#">合作与生态<em></em></a>
                  <ol class="menu-leval">
                    <li><a href="#">合作伙伴<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">生态体系<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">合作模式<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
                  </ol>
                </li>
                <li ><a href="#">资源中心<em></em></a>
                  <ol class="menu-leval">
                    <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">产品手册<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">教程与指南<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">行业洞察<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">算力知识库<i class="iconfont">&#xe624;</i></a></li>
                  </ol>
                </li>
                <li ><a href="contact-contacts.html">联系我们<em></em></a>
                  <ol class="menu-leval">
                    <li><a href="#">销售咨询<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">技术支持<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">媒体联系<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
                    <li><a href="#">办公地点<i class="iconfont">&#xe624;</i></a></li>
                  </ol>
                </li>
              </ul>
              <div class="menu-ip-down">
                <div class="online-shopp"> </div>
              </div>
            </div>
          </div>
        </div>
      </header>
        
<div class="main-content-wrap"><div class="content-wrap"><div class="head-search">
     <form action="search-search.html" method="post">
          <input class="search-ipt" type="text" name="query" placeholder="输入关键词...">
          <input class="search-btn" type="submit" name="submit" value="搜索" title="搜索">
     </form>
</div></div></div>
<div class="mobile-body-mask"></div>


                      
    <div class="pbanner">
        <div class="product-hide">
            <figure><img class="pc" src="static/picture/productbanner.jpg" alt="算力如水 创想无限"/>
            <div class="sjimg" style="background-image:url(static/images/banner-products.jpg);">
            </div>
            </figure>
        </div>
        <div class="series global-fix">
            <p class="article-block slidetop">Unlimited Creativity</p>
            <div class="series-title article-block slidetop detay1 syht">
                <strong>算力如水 创想无限</strong>
            </div>
        </div>
        <div class="sea-down defaul">
            <i></i>
            <span>向下滑动</span>
        </div>
    </div>
    


  
<!--page-->
<div class="page clearfix">
        
        <!-- 产品矩阵板块 -->
        <div class="product-matrix-section">
            <div class="w1600 clearfix">
                <div class="pageTit wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">
                    <span class="en">Product Matrix</span>
                    <div class="cn"><span class="syht">产品矩阵</span></div>
                </div>
                
                <div class="matrix-subtitle wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.4s">
                    <p>算力投资与技术代差双螺旋发展，真正实现让算力如流水般任意使用</p>
                </div>
                
                <div class="matrix-content">
                    <!-- 左侧内容 -->
                    <div class="matrix-left">
                        <div class="matrix-item wow fadeInLeft" data-wow-duration="1.2s" data-wow-delay="0.5s">
                            <div class="item-number">01</div>
                            <h3>高端算力集群</h3>
                            <p>采用A800/H100，适用于超大规模计算，AI大模型训练，超算等。</p>
                        </div>
                        
                        <div class="matrix-item wow fadeInLeft" data-wow-duration="1.2s" data-wow-delay="0.6s">
                            <div class="item-number">03</div>
                            <h3>专业算力集群</h3>
                            <p>采用RTX 4090，适用于实时渲染，技术优势在于灵活适配，超强扩展，高稳定性，混合架构兼容，自有算力达360 PFLOPS。</p>
                        </div>
                        
                        <div class="matrix-item wow fadeInLeft" data-wow-duration="1.2s" data-wow-delay="0.7s">
                            <div class="item-number">05</div>
                            <h3>普惠算力集群</h3>
                            <p>采用RTX 4070S，适用于工业自动化，质控检测，商业支持，技术优势在于高速响应。</p>
                        </div>
                    </div>
                    
                    <!-- 中间图形 -->
                    <div class="matrix-center wow fadeIn" data-wow-duration="1.5s" data-wow-delay="0.5s">
                        <div class="matrix-graphic">
                            <div class="matrix-image-container">
                                <!-- 产品矩阵图片 -->
                                <img src="static/images/ProductMatrix.png" alt="产品矩阵双螺旋图形" class="matrix-main-image" />
                                
                                <!-- 数字标记覆盖层 -->
                                <div class="number-overlay">
                                    <div class="number-marker marker-01" data-number="01">01</div>
                                    <div class="number-marker marker-02" data-number="02">02</div>
                                    <div class="number-marker marker-03" data-number="03">03</div>
                                    <div class="number-marker marker-04" data-number="04">04</div>
                                    <div class="number-marker marker-05" data-number="05">05</div>
                                    <div class="number-marker marker-06" data-number="06">06</div>
                                </div>
                                
                                <!-- 流动效果覆盖层 -->
                                <div class="flow-overlay">
                                    <div class="flow-particle flow-particle-1"></div>
                                    <div class="flow-particle flow-particle-2"></div>
                                    <div class="flow-particle flow-particle-3"></div>
                                    <div class="flow-particle flow-particle-4"></div>
                                    <div class="flow-particle flow-particle-5"></div>
                                    <div class="flow-particle flow-particle-6"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧内容 -->
                    <div class="matrix-right">
                        <div class="matrix-item wow fadeInRight" data-wow-duration="1.2s" data-wow-delay="0.5s">
                            <div class="item-number red">02</div>
                            <h3>基于云端算力监控技术</h3>
                            <p>SaaS支撑，实时追踪节点负载，平衡算力供需，优化资源利用率，生成多维数据。</p>
                        </div>
                        
                        <div class="matrix-item wow fadeInRight" data-wow-duration="1.2s" data-wow-delay="0.6s">
                            <div class="item-number red">04</div>
                            <h3>算力调度系统</h3>
                            <p>自研算法，可实时监测集群节点的负载，资源占用与运行状态，通过智能算法动态分配资源，自识别任务优先级别，自优化策略，保障关键任务高效运行，提升整体算力利用率，降低成本，同场景下GPU占用效率比其它平台高5%。</p>
                        </div>
                        
                        <div class="matrix-item wow fadeInRight" data-wow-duration="1.2s" data-wow-delay="0.7s">
                            <div class="item-number red">06</div>
                            <h3>算力贮存技术</h3>
                            <p>未公开技术，基于YZJ-McE开发，目的是使GPU闲时使用效率提升，从硬件上优化大模型。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!--pageAdv1-->
        <div class="pageAdv pageAdv1 clearfix">
<div class="pageTit"><span class="en">Service Model</span><div class="cn"><span class="syht">服务模式</span></div></div>

<div class="service-grid-container">
    <div class="service-grid">
        <!-- 第一行 -->
        <!-- 01 商业模式 -->
        <div class="service-card service-01 wow fadeInLeft" data-wow-duration="1.2" data-wow-delay="0.3s">
            <div class="card-number">01</div>
            <div class="card-content">
                <h2>商业模式</h2>
                <p>构建完整的盈利模式和运营模式体系，通过设备共享+收益分成实现稳定现金流，轻资产扩张快速占领市场。</p>
            </div>
        </div>
        
        <!-- 数据中心背景图片 -->
        <div class="service-card service-datacenter wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
            <div class="datacenter-bg"></div>
        </div>
        
        <!-- 03 算力接入服务 -->
        <div class="service-card service-03 wow fadeInRight" data-wow-duration="1.2" data-wow-delay="0.5s">
            <div class="card-number">03</div>
            <div class="card-content">
                <h2>算力接入服务</h2>
                <p>面向中小算力中心或个人算力投资者，可以方便地接入聚算云，进行云端服务，独立后台透明使用，提高中小算力中心使用效率，增加投资者收益。</p>
            </div>
        </div>
        
        <!-- 第二行 -->
        <!-- 智能拼图背景图片 -->
        <div class="service-card service-puzzle wow fadeInLeft" data-wow-duration="1.2" data-wow-delay="0.6s">
            <div class="puzzle-bg"></div>
        </div>
        
        <!-- 02 算力投资服务 -->
        <div class="service-card service-02 wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.7s">
            <div class="card-number">02</div>
            <div class="card-content">
                <h2>算力投资服务</h2>
                <p>提供一站式全生命周期算力托管，涵盖投资、采购、托管、收益、退出全流程服务。</p>
            </div>
        </div>
        
        <!-- AI机器人背景图片 -->
        <div class="service-card service-robot wow fadeInRight" data-wow-duration="1.2" data-wow-delay="0.8s">
            <div class="robot-bg"></div>
        </div>
    </div>
</div>
                
        </div>
        <!--pageAdv1 end-->
        
        <!-- 商业模式 -->
        <div id="business-model" class="product-section" style="background-color:#fff;">
            <div class="w1600 clearfix">
                <div class="pageTit" id="business-model-title"><span class="en">Business Model</span><div class="cn"><span class="syht">商业模式</span></div></div>
                
                <!-- 商业模式卡片网格 -->
                <div class="business-model-container">
                    <div class="business-model-grid">
                        <!-- 盈利模式 -->
                        <div class="business-model-section profit-model wow fadeInLeft" data-wow-duration="1.2" data-wow-delay="0.3s">
                            <h3 class="model-title">盈利模式</h3>
                            <div class="model-cards">
                                <!-- 核心模式 -->
                                <div class="model-card">
                                    <div class="card-icon">
                                        <img src="static/images/yingli1.png" alt="核心模式" class="icon-img">
                                    </div>
                                    <h4>核心模式</h4>
                                    <p>设备共享+收益分成，通过算力租赁收益分成获得稳定现金流，同时通过3年期满设备残值处置获得一次增值。</p>
                                </div>
                                
                                <!-- 收入来源 -->
                                <div class="model-card">
                                    <div class="card-icon">
                                        <img src="static/images/yingli2.png" alt="收入来源" class="icon-img">
                                    </div>
                                    <h4>收入来源</h4>
                                    <p>收入来源包括端算力租赁收入、算力投资服务收益分成、算力平台通道费用，3年期满设备残值处置收益等。</p>
                                </div>
                                
                                <!-- 未来增长 -->
                                <div class="model-card">
                                    <div class="card-icon">
                                        <img src="static/images/yingli3.png" alt="未来增长" class="icon-img">
                                    </div>
                                    <h4>未来增长</h4>
                                    <p>未来增长点包括新建算力中心、算力资产证券化、跨境算力交易，通过不断创新和拓展业务领域，实现可持续发展。</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 运营模式 -->
                        <div class="business-model-section operation-model wow fadeInRight" data-wow-duration="1.2" data-wow-delay="0.4s">
                            <h3 class="model-title">运营模式</h3>
                            <div class="model-cards">
                                <!-- 轻资产扩张 -->
                                <div class="model-card">
                                    <div class="card-icon">
                                        <img src="static/images/yingli1.png" alt="轻资产扩张" class="icon-img">
                                    </div>
                                    <h4>轻资产扩张</h4>
                                    <p>通过全国运营中心合伙人机制实现轻资产扩张，快速抢占市场份额，降低运营成本。</p>
                                </div>
                                
                                <!-- 生态闭环 -->
                                <div class="model-card">
                                    <div class="card-icon">
                                        <img src="static/images/yingli2.png" alt="生态闭环" class="icon-img">
                                    </div>
                                    <h4>生态闭环</h4>
                                    <p>构建"投资人-数据中心-算力需求方"三方协同的生态闭环，实现资源共享、优势互补，提升整体竞争力。</p>
                                </div>
                                
                                <!-- 成本优势 -->
                                <div class="model-card">
                                    <div class="card-icon">
                                        <img src="static/images/yingli3.png" alt="成本优势" class="icon-img">
                                    </div>
                                    <h4>成本优势</h4>
                                    <p>成本优势体现在集中采购降设备成本、智能调度降运营成本，通过优化资源配置，提高运营效率。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 竞争优势 -->
        <div id="competitive-advantages" class="product-section" style="background-color:#f8f9fa;">
            <div class="w1600 clearfix">
                <div class="pageTit" id="competitive-advantages-title"><span class="en">Competitive Advantages</span><div class="cn"><span class="syht">竞争优势</span></div></div>
                
                <!-- 竞争优势卡片网格 -->
                <div class="advantages-container">
                    <div class="advantages-grid">
                        <!-- 01 差异化赛道 -->
                        <div class="advantage-card advantage-01 wow fadeInLeft" data-wow-duration="1.2" data-wow-delay="0.3s">
                            <div class="card-number">01</div>
                            <div class="card-content">
                                <h3>差异化赛道</h3>
                                <p>中国市场内卷严重，我们经营之初就选择非直接竞争，通过共享经济整合闲置资源，开创算力共享经济新模式。</p>
                            </div>
                        </div>
                        
                        <!-- 02 三大护城河 -->
                        <div class="advantage-card advantage-02 wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
                            <div class="card-number">02</div>
                            <div class="card-content">
                                <h3>三大护城河</h3>
                                <p>三大护城河包括创新商业模式、自有技术生态系统、轻资产快速扩张能力，为公司发展提供坚实保障。</p>
                            </div>
                        </div>
                        
                        <!-- 03 铁锁哲学 -->
                        <div class="advantage-card advantage-03 wow fadeInRight" data-wow-duration="1.2" data-wow-delay="0.5s">
                            <div class="card-number">03</div>
                            <div class="card-content">
                                <h3>铁锁哲学</h3>
                                <p>我们并不知道谁会在这次"AI淘金热"中跑出，但我们知道每位淘金者都需要铁锹，所以我们只卖铁锹（算力），这就是"铁锹哲学"。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
<!--page End-->
<!--footer-->   
<div class="footer clearfix">
    <!--footer-nav-->
    <div class="footer-nav">
    <div class="w1600 clearfix">
        <div class="left"> <div class="leftBox clearfix">
        
                <div class="ul2"><div class="ul2B">
                     <div class="t1"><a href="solution-cases.html">解决方案</a></div>      
                     <div class="listB"> 
                     <ul class="ulB">
                        
                            <li class="t2"><a href="solution-1805-1809.html">企业算力解决方案</a></li>
               
                            <li class="t2"><a href="solution-1805-1810.html">电竞云网吧方案</a></li>
               
                            <li class="t2"><a href="solution-1805-1811.html">边缘计算方案</a></li>
                      
                     </ul>
                     </div>
                </div></div>
                
                <div class="ul2"><div class="ul2B">
                     <div class="t1 clearfix"><a href="advantage-ys.html">我们的优势</a></div>
                     <div class="listB"> 
                     <ul class="ulB">
                            
                            <li class="t2"><a href="advantage-ys.html">铁锹哲学</a></li>
               
                            <li class="t2"><a href="advantage-ys.html#adBox2">三重价值</a></li>
                    
                     </ul>
                     </div>
                </div></div>
                
                <div class="ul2"><div class="ul2B">
                     <div class="t1"><a href="page-abouts.html">关于我们</a></div>   
                     <div class="listB">     
                     <ul class="ulB">
                       
                            <li class="t2"><a href="page-1751-1752.html">公司介绍</a></li>
               
                            <li class="t2"><a href="page2-1751-1753.html">常见问题</a></li>
               
                            <li class="t2"><a href="contact-1751-1832.html">服务客户</a></li>
              
                     </ul>
                     </div>
                </div></div>
                
                <div class="ul2"><div class="ul2B">
                     <div class="t1 clearfix"><a href="hyyy-hyyy.html">客户案例</a></div>
                     <div class="listB"> 
                     <ul class="ulB">
                        
                            <li class="t2"><a href="hylist-1808-1814.html">电竞网吧</a></li>
               
                            <li class="t2"><a href="hylist-1808-1815.html">AI企业</a></li>
               
                            <li class="t2"><a href="hylist-1808-1816.html">创新创业</a></li>
               
                            <li class="t2"><a href="hylist-1808-1817.html">智能硬件</a></li>
               
                            <li class="t2"><a href="hylist-1808-1818.html">其它案例</a></li>
              
                     </ul>
                     </div>
                </div></div>
                
            
        
        
        </div></div>
        <div class="right"><div class="rightBox clearfix">
<table width="100%">
	<tbody>
		<tr>
			<td>
				<h1>
					服务热线
				</h1>
			</td>
		</tr>
		<tr>
			<td class="tel">
				<h1>
					<span>************</span> 
				</h1>
				<p>
					诚挚为您服务
				</p>
			</td>
		</tr>
	</tbody>
</table>
           

<div class="bottom-share">
    <span>关注我们：</span>
    <a class="iconfont email" href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">&#xe6db;</a>
    <a class="iconfont qq" href="http://wpa.qq.com/msgrd?v=3&uin=123456789&site=stelok&menu=yes" target="_blank" rel="noopener noreferrer">&#xe60f;</a>
    <a class="iconfont weix"  rel="noopener noreferrer">&#xe660;<span class="img"><img src="static/picture/202203011551003232.jpg" alt="" /><br /></span></a>
</div>



        </div></div>


        
     </div>
     
     <div class="bottom-wz"><div class="w1600 clearfix">聚算云，让算力如水般自由流动，为每一次创新提供触手可及的力量。</div></div>
     </div>
    <!--footer-nav end-->
    <div class="bq clearfix"><div class="w1600 clearfix">
             
            <div class="bqname">
            &copy;2021 广州聚算云科技有限公司 版权所有 <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">粤ICP备XXXXXXXX号</a>           
            </div>
            <div class="beian">
                网站设计: <a href="http://www.king-tin.com/" target="_blank" rel="noopener noreferrer">KINGTIN</a><script charset="UTF-8" id="LA_COLLECT" src="static/js/js-sdk-pro.min.js"></script><script>LA.init({id: "Jh6tR8OE31b5wc6v",ck: "Jh6tR8OE31b5wc6v"})</script>
            </div> 
    </div>
    </div>
    
    
    
    
    
   
</div>
<!--End footer-->     
<div class="common-bott">
    <div class="back-top commontop iconfont">&#xe731;</div>
</div>


</section>


<script type="text/javascript" src="static/js/wow.min2.js"></script>
<script type="text/javascript" src="static/js/plugin.js"></script>
<script type="text/javascript" src="static/js/page.js"></script>
<script>
$(document).ready(function(){
	var a,b,c;
	var videos=document.getElementById("video");
	a=$(window).height();
	var group=$("#videostart");
	$(window).scroll(function(){
		b=$(this).scrollTop();
		c=group.offset().top;
		if(a+b>c)
		{
			    videos.play();
		}else{
				videos.pause();				
			  }
		});

    // 平滑滚动到锚点
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if(target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 800);
        }
    });

    // 在滚动时高亮当前区块对应的导航项，但不改变背景色
    $(window).scroll(function() {
        var scrollPos = $(window).scrollTop();
        
        $('.product-section').each(function() {
            var sectionTop = $(this).offset().top - 100;
            var sectionId = $(this).attr('id');
            
            if (scrollPos >= sectionTop) {
                // 只移除和添加active类，但通过CSS控制不改变背景色
                $('.pageAdvList1 a').removeClass('active');
                $('.pageAdvList1 a[href="#' + sectionId + '"]').addClass('active');
            }
        });
    });
    
    // 移除所有元素的hover事件影响
    $('.solution-card, .feature-box, .grid-item, .trust-feature').unbind('mouseenter mouseleave');
});
</script>
<script>
var scrolling = true;
var timer = null;
$(document).on('mousewheel DOMMouseScroll', function (e) {
        if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
        navigateUp();
        } else {
        if (e.pageY <= w_height && scrolling && !isMobile) {
        clearTimeout(timer);
        scrolling = false;
        timer = setTimeout(function () {
        jQuery("html,body").stop(true, true).animate({ scrollTop: w_height }, 1200, "easeOutQuint")
        }, 100)
        }
        }
});
function navigateUp() {
        scrolling = true;
}
</script>
<script>
$(function(){
	$(".pageAdvList1 a").click(function(){
		var target = $(this).attr("href");
		var targetScroll = $(target).offset().top - 190;
		$("html,body").animate({scrollTop:targetScroll},300);
		return false;
	});
}());
</script>
</body>
</html>

