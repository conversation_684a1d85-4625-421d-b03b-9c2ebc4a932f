/* 合作模式页面样式 */

/* 通用样式 */
.coop-intro-section,
.coop-modes-section,
.coop-process-section,
.coop-advantage-section,
.coop-case-section,
.coop-join-section {
  max-width: 1200px;
  margin: 0 auto 60px;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.section-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #2b64a7, rgba(255, 128, 0, 1));
  margin: 0 auto;
}

/* 合作介绍部分 */
.coop-intro-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 40px;
  font-size: 18px;
  line-height: 1.8;
  color: #555;
}

/* 合作模式部分 */
.modes-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.mode-card {
  width: calc(33.33% - 30px);
  min-width: 280px;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.mode-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(43, 100, 167, 0.15);
}

.mode-header {
  padding: 20px 30px;
  background: linear-gradient(135deg, #2b64a7, #1e4675);
  color: white;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.mode-header:before {
  content: '';
  position: absolute;
  top: -30px;
  right: -30px;
  width: 100px;
  height: 100px;
  background: rgba(255, 128, 0, 0.3);
  border-radius: 50%;
  z-index: 1;
}

.mode-icon {
  width: 50px;
  height: 50px;
  min-width: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  position: relative;
  z-index: 2;
}

.mode-icon svg {
  width: 25px;
  height: 25px;
  fill: white;
}

.mode-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  position: relative;
  z-index: 2;
}

.mode-body {
  padding: 30px;
  position: relative;
}

.mode-body:before {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(43, 100, 167, 0.03) 0%, transparent 70%);
  z-index: 0;
}

.mode-body h4 {
  font-size: 18px;
  color: #2b64a7;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
}

.mode-body p {
  font-size: 15px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.feature-list {
  padding-left: 0;
  list-style: none;
  position: relative;
  z-index: 1;
}

.feature-list li {
  padding-left: 25px;
  position: relative;
  margin-bottom: 10px;
  font-size: 14px;
  color: #555;
}

.feature-list li:before {
  content: '';
  position: absolute;
  left: 0;
  top: 7px;
  width: 8px;
  height: 8px;
  background: rgba(255, 128, 0, 1);
  border-radius: 50%;
}

/* 合作流程部分 */
.process-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  margin-top: 40px;
}

.process-item {
  width: 200px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.process-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2b64a7, #1e4675);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 24px;
  font-weight: 700;
  position: relative;
  box-shadow: 0 5px 15px rgba(43, 100, 167, 0.3);
}

.process-circle:before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px dashed rgba(43, 100, 167, 0.3);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.process-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.process-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.process-arrow {
  width: 60px;
  height: 20px;
  position: relative;
  transform: translateY(-30px);
}

.process-arrow svg {
  width: 40px;
  height: 40px;
  fill: #2b64a7;
}

.process-line {
  position: absolute;
  top: 40px;
  left: 100px;
  right: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #2b64a7, transparent);
  z-index: 1;
}

/* 合作优势部分 */
.advantage-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.advantage-item {
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: 30px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.advantage-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.1);
}

.advantage-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #2b64a7, rgba(255, 128, 0, 1));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.advantage-item:hover:before {
  transform: scaleX(1);
}

.advantage-icon {
  width: 70px;
  height: 70px;
  background: rgba(43, 100, 167, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.advantage-item:hover .advantage-icon {
  background: rgba(43, 100, 167, 0.2);
}

.advantage-icon svg {
  width: 35px;
  height: 35px;
  fill: #2b64a7;
  transition: all 0.3s ease;
}

.advantage-item:hover .advantage-icon svg {
  transform: rotate(10deg) scale(1.1);
}

.advantage-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.advantage-desc {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
}

/* 合作案例部分 */
.case-container {
  margin-top: 40px;
}

.case-card {
  display: flex;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  transition: all 0.3s ease;
}

.case-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(43, 100, 167, 0.15);
}

.case-image {
  width: 35%;
  min-width: 300px;
  background-position: center;
  background-size: cover;
  position: relative;
}

.case-image:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(30, 70, 117, 0.7), transparent);
}

.case-content {
  padding: 40px;
  width: 65%;
}

.case-tag {
  display: inline-block;
  padding: 5px 15px;
  border-radius: 20px;
  background: rgba(43, 100, 167, 0.1);
  color: #2b64a7;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 15px;
}

.case-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.case-desc {
  font-size: 15px;
  color: #666;
  line-height: 1.8;
  margin-bottom: 20px;
}

.case-highlight {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.highlight-item {
  flex: 1;
  min-width: 150px;
  padding: 15px;
  background: rgba(43, 100, 167, 0.05);
  border-radius: 8px;
  text-align: center;
}

.highlight-number {
  font-size: 24px;
  font-weight: 700;
  color: #2b64a7;
  margin-bottom: 5px;
}

.highlight-label {
  font-size: 14px;
  color: #666;
}

/* 合作邀请部分 */
.join-section {
  background: linear-gradient(135deg, #2b64a7, #1e4675);
  border-radius: 15px;
  padding: 50px;
  color: white;
  position: relative;
  overflow: hidden;
  margin-top: 60px;
}

.join-section:before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: rgba(255, 128, 0, 0.3);
  border-radius: 50%;
  z-index: 0;
}

.join-container {
  position: relative;
  z-index: 1;
  text-align: center;
}

.join-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
}

.join-desc {
  font-size: 16px;
  line-height: 1.8;
  max-width: 700px;
  margin: 0 auto 30px;
  opacity: 0.9;
}

.join-button {
  display: inline-block;
  padding: 15px 40px;
  background: rgba(255, 128, 0, 1);
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 5px 15px rgba(255, 128, 0, 0.3);
}

.join-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(255, 128, 0, 0.4);
  background: rgba(255, 128, 0, 0.9);
  color: white;
}

.join-pattern {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="none" /><path d="M0 50 L50 0 L100 50 L50 100 Z" fill="rgba(255,255,255,0.03)" /></svg>');
  background-size: 20px 20px;
  opacity: 0.3;
  z-index: 0;
}

/* 响应式设计 */
@media (max-width: 1100px) {
  .process-container {
    justify-content: center;
    gap: 30px;
  }
  
  .process-line {
    display: none;
  }
  
  .process-arrow {
    display: none;
  }
  
  .process-item {
    margin-bottom: 30px;
  }
  
  .case-card {
    flex-direction: column;
  }
  
  .case-image {
    width: 100%;
    min-width: auto;
    height: 200px;
  }
  
  .case-content {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .mode-card {
    width: 100%;
  }
  
  .join-section {
    padding: 40px 30px;
  }
  
  .join-title {
    font-size: 28px;
  }
  
  .highlight-item {
    flex: auto;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 24px;
  }
  
  .coop-intro-content {
    font-size: 16px;
  }
  
  .mode-header {
    padding: 15px 20px;
  }
  
  .mode-body {
    padding: 20px;
  }
  
  .mode-title {
    font-size: 18px;
  }
  
  .join-title {
    font-size: 24px;
  }
  
  .join-desc {
    font-size: 15px;
  }
  
  .join-button {
    padding: 12px 30px;
    font-size: 15px;
  }
}

/* AI科技特效 */
.mode-card,
.advantage-item,
.case-card {
  position: relative;
  overflow: hidden;
}

.mode-card:after,
.advantage-item:after,
.case-card:after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(30deg);
  animation: shining 3s infinite linear;
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mode-card:hover:after,
.advantage-item:hover:after,
.case-card:hover:after {
  opacity: 1;
}

@keyframes shining {
  0% {
    transform: rotate(30deg) translate(-100%, -100%);
  }
  100% {
    transform: rotate(30deg) translate(100%, 100%);
  }
}

.join-button {
  position: relative;
  overflow: hidden;
}

.join-button:after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(30deg);
  animation: btnShining 2s infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes btnShining {
  0% {
    transform: rotate(30deg) translate(-100%, -100%);
  }
  100% {
    transform: rotate(30deg) translate(100%, 100%);
  }
}

.process-circle {
  position: relative;
  overflow: hidden;
}

.process-circle:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.process-item:hover .process-circle:after {
  opacity: 1;
}

.mode-header svg,
.advantage-icon svg,
.process-circle {
  transition: all 0.3s ease;
}

.mode-card:hover .mode-header svg,
.advantage-item:hover .advantage-icon svg {
  transform: rotate(10deg);
}

.process-item:hover .process-circle {
  transform: scale(1.1);
}