.home-about {
  padding: 80px 0;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.home-about .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.home-about-title {
  text-align: center;
  margin-bottom: 50px;
}

.home-about-title .en {
  font-size: 36px;
  color: #2f78be;
  font-weight: 600;
  letter-spacing: 2px;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.home-about-title .cn {
  font-size: 24px;
  color: #333;
  font-weight: 500;
}

.home-about-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.china-map-container {
  width: 60%;
  position: relative;
}

.china-map {
  width: 100%;
  height: auto;
}

.map-point {
  position: absolute;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  background-color: #2f78be;
  box-shadow: 0 0 0 5px rgba(47, 120, 190, 0.3);
  cursor: pointer;
  z-index: 2;
  transition: all 0.3s ease;
}

.map-point:hover, .map-point.active {
  background-color: #e58927;
  box-shadow: 0 0 0 5px rgba(229, 137, 39, 0.3);
}

.map-label {
  position: absolute;
  background-color: #2f78be;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  font-size: 14px;
  white-space: nowrap;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16);
  transform: translateY(-50%);
  z-index: 3;
  transition: all 0.3s ease;
}

.map-label:after {
  content: '';
  position: absolute;
  top: 50%;
  width: 10px;
  height: 10px;
  background-color: #2f78be;
  transform: rotate(45deg);
  z-index: -1;
}

.map-label.left:after {
  right: -5px;
}

.map-label.right:after {
  left: -5px;
}

.map-label:hover, .map-label.active {
  background-color: #e58927;
}

.map-label:hover:after, .map-label.active:after {
  background-color: #e58927;
}

.home-about-data {
  width: 35%;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 20px;
}

.data-number {
  font-size: 42px;
  font-weight: 700;
  color: #2f78be;
  line-height: 1;
}

.data-number span {
  font-size: 16px;
  color: #666;
  margin-left: 5px;
}

.data-text {
  font-size: 14px;
  color: #666;
}

@media screen and (max-width: 992px) {
  .home-about-content {
    flex-direction: column;
  }
  
  .china-map-container, .home-about-data {
    width: 100%;
  }
  
  .home-about-data {
    margin-top: 50px;
  }
}