/* 产品板块通用样式 */
.product-section {
    margin: 50px 0;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.product-section-inner {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.product-title {
    text-align: center;
    margin-bottom: 50px;
}

.product-title h2 {
    font-size: 36px;
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.product-title p {
    font-size: 18px;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

.product-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
}

.product-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex: 1 1 calc(33.33% - 30px);
    min-width: 300px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(225, 225, 225, 0.3);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #6366f1, #8b5cf6);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover::before {
    opacity: 1;
}

.product-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    border-radius: 15px;
    margin-bottom: 20px;
}

.product-icon img {
    width: 35px;
    height: 35px;
}

.product-card h3 {
    font-size: 22px;
    color: #1f2937;
    margin-bottom: 15px;
    font-weight: 600;
}

.product-card p {
    font-size: 16px;
    color: #4b5563;
    line-height: 1.6;
    margin-bottom: 20px;
}

.product-features {
    margin-top: 15px;
}

.product-feature {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.product-feature i {
    color: #10b981;
    margin-right: 10px;
    font-size: 16px;
}

.product-feature span {
    font-size: 14px;
    color: #374151;
}

.product-cta {
    margin-top: 20px;
}

.product-cta a {
    display: inline-block;
    padding: 10px 20px;
    background: #3b82f6;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: background 0.3s ease;
}

.product-cta a:hover {
    background: #2563eb;
}

/* 每个板块的特殊样式 */
#compute-platform-section {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

#cloud-solutions-section {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

#edge-computing-section {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

#ai-compute-section {
    background: linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%);
}

#compute-pool-section {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .product-card {
        flex: 1 1 calc(50% - 30px);
    }
}

@media (max-width: 768px) {
    .product-section {
        padding: 60px 0;
    }
    
    .product-title h2 {
        font-size: 30px;
    }
    
    .product-title p {
        font-size: 16px;
    }
    
    .product-card {
        flex: 1 1 100%;
    }
}

/* 动画效果 */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-in.show {
    opacity: 1;
    transform: translateY(0);
}

/* 装饰元素 */
.bg-decorator {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    z-index: -1;
}

.decorator-1 {
    top: -150px;
    right: -100px;
}

.decorator-2 {
    bottom: -150px;
    left: -100px;
}

.circle-decorator {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(99, 102, 241, 0.3);
    z-index: -1;
}

/* 科技纹理背景 */
.tech-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: -1;
}