/* 社交媒体图标样式 */
.social-icon {
    display: inline-block;
    width: 44px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    border-radius: 50%;
    margin-right: 5px;
    color: #fff;
    background-color: transparent;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.social-icon:before {
    content: "";
    width: 100%;
    height: 0;
    background: #2b64a7;
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: -1;
    transition: 0.45s;
}

.social-icon:hover:before {
    height: 100%;
    bottom: auto;
    top: 0;
}

.social-icon img {
    width: 24px;
    height: 24px;
    vertical-align: middle;
    filter: invert(1);
}

/* 二维码弹出框样式 */
.social-icon .qrcode {
    display: none;
    position: absolute;
    bottom: 55px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px;
    background: #fff;
    border: 2px solid #2b64a7;
    border-radius: 5px;
    z-index: 100;
}

.social-icon:hover .qrcode {
    display: block;
}

.social-icon .qrcode:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #2b64a7;
}

.social-icon .qrcode img {
    width: 120px;
    height: 120px;
    display: block;
    filter: none;
}

@media screen and (max-width: 768px) {
    .social-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
    }
    
    .social-icon img {
        width: 22px;
        height: 22px;
    }
    
    .social-icon .qrcode img {
        width: 100px;
        height: 100px;
    }
}