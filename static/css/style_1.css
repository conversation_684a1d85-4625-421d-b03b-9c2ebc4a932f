#new_tab tr
{
background:url(../images/dotline.gif) repeat-x bottom;
}
/*新闻列表-日期*/


.c_date {
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size: 9px;
/*	color: #F36525;*/
	text-decoration: none;
}
/*新闻列表-超链接*/
#new_tab tr{height:30px; overflow:hidden;}
#new_tab tr td{height:30px; overflow:hidden; display:inline-block;}
#new_tab .c_news a{
	FONT-SIZE: 14px;
	COLOR: #434343;
	TEXT-DECORATION:none;
	font-weight: normal;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	width:570px; height:28px; line-height:28px; overflow:hidden; display:block;
}
#new_tab .c_news a:hover {
	FONT-SIZE: 14px;
	color:#e6730e;
}
#new_tab .c_news a:visited{ font-size:14px;;}
#new_tab td {
/*	background-image: url(../images/line.jpg);
	background-repeat: repeat-x;
	background-position: bottom;
*/}
/*字体大中小的超链接*/
.ctrl_font{ padding-top:5px;}
.ctrl_font a{
	COLOR: #000;
	text-decoration:none;
	}
.ctrl_font a:hover{
	COLOR: #000;
	text-decoration:underline;
	}

.c_page_tab {
	border-top-width: 1px;
	border-top-style: dotted;
	border-top-color: #ccc;
}

.c_news_content_tab {
/*	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-style: solid;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: solid;
	border-top-color: #ffffff;
	border-right-color: #ffffff;
	border-bottom-color: #ffffff;
	border-left-color: #ffffff;
*/}
.c_news_content_tab td{

}
.c_news_content_title {
	font-size: 18px;
	/*font-weight: bold;*/
	text-decoration: none;
	margin:5px;
	line-height:25px;
	padding:15px 0;
	line-height:35px;
	color:#434343;
}
/*产品图片列表控制*/
.sort td{
	padding:5px;
}
.sort table{
	clear:both;
	text-align:center;
	/*border:1px solid #E4E4E4;
	padding:8px;
	width:220px;
	height:120px;*/
	vertical-align: middle;
}
.sort_title{
	margin-bottom:5px;
		text-align:center;

		padding-top:5px

	}
.sort_title a{ color:#000; font-size:14px; text-decoration:none}
/*产品文字列表控制*/
.sort_txt{padding:5px; text-align:left;event:expression(onmouseover=function(){bgColor="#ffeeee"},onmouseout=function(){bgColor=""})}

.product_pic_table{
	border:1px solid #ccc;
	background-color:#fff;
	padding:5px;
    width:212px;
	height:130px;
	overflow:hidden;
	
}
.product_pic_table img{max-width:220px; max-height:120px; 
	}
.sortpic{
	border:1px solid #E4E4E4;
	padding:5px;
	width:220px;
	height:120px;
	
	}
.sortpic img{ width:100%; max-width:220px; height:auto;}	


.main td{
	padding:0px;

}
.main {
	width:100%;
}


.small_sort {
	FONT-SIZE: 9px; COLOR: #666666
}
.show_bottom_td {
	background-color: #f8f8f8;
	text-align: center;
	padding: 5px;
}
.show_bottom_td a{
	font-size: 12px;
}
.idname_maka{ background:url(../images/member_03.jpg) no-repeat top; width:170px; height:21px;}

.detail_box .title{ font-size:18px; text-align:center; padding-bottom:10px; font-weight:bold;}
.detail_wz{ padding:2% 0;}




/*honor*/
.honor_list{height:auto; overflow:hidden; margin-top:14px;color:#333;line-height:30px; padding-bottom:30px;}
.honor_list .tit{font-size:18px;color:#333;text-align:center;margin-bottom:30px;}
.honor_list ul{margin:0; padding:0; height:auto; overflow:hidden;width:100%;}
.honor_list ul li{width:247px; float:left; margin-right:22px;margin-bottom:0px;margin-top:22px; position:relative;}
.honor_list ul li .ImgBoxB{width:245px; height:245px;overflow:hidden; position:relative;border:1px solid #d2d2d2; background-color:#fff; vertical-align:middle; }
.honor_list ul li .ImgBox{width:245px; height:245px;overflow:hidden; vertical-align:middle;}
.honor_list ul li .ImgBox img {max-width:300px; max-height:240px;transition: transform 0.3s ease-in-out;}
.honor_list ul li:hover .ImgBox img{transform: scale(1.1, 1.1);}
.honor_list ul li .ImgBox .item_mask {content: ""; width: 100%;height: 100%;background: rgba(0,0,0,.3);position: absolute;left: 0;top: 0;opacity: 0;transform-origin: 50% 0;transform: scale(1, 1);transition: transform 0.3s ease-in-out;}
.honor_list ul li:hover .item_mask {top: 0;opacity: 1;transform: scale(1.1, 1.1);transition: transform 0.3s ease-in-out;}
.honor_list ul li .product_title{font-size:14px; line-height:30px; height:30px;margin-top:2px; font-weight:normal;color:#464646;text-align:center;}
.honor_list ul li:hover .product_title{color:#349FF4;}
.honor_list ul li:nth-child(4n){margin-right:0;}

@media screen and (max-width: 1200px) { 
.honor_list ul li:nth-child(3n){margin-right:2%;}
.honor_list ul{width:102%;margin-bottom:67px;}
.honor_list ul li{width:31%;margin-right:2%;margin-top:2.5%;}
.honor_list ul li .ImgBox{width:98%;height:98%;}
.honor_list ul li .ImgBox img{max-width:100%;max-height:100%;}
}
@media screen and (max-width: 767px) { 
.honor_list ul{width: calc(100% + 2%);margin-bottom:1em;}
.honor_list ul li{width: calc(100% / 2 - 2%);margin-right: 2%;margin-top:2.5%;float: left;display: block;}

}




.c_page_tab{padding-top:35px; padding-bottom:35px; }
.c_page_tab a{display:inline-block; padding:0px 10px; border:1px solid #ccc;  color:#6e6e6e;transition: all .3s ease-in-out;-moz-transition: all .3s ease-in-out;-webkit-transition: all .3s ease-in-out;line-height:24px;}
.c_page_tab a:hover{background:#C24045; border-color:#C24045; color:#fff;}
.nextpage select{ border:1px solid #ccc;}


#c_page_tab2{padding-top:10px; padding-bottom:35px; border:none; }
#c_page_tab2 a{display:inline-block; padding:0px 10px; border:1px solid #ccc;color:#fff; transition: all .3s ease-in-out;-moz-transition: all .3s ease-in-out;-webkit-transition: all .3s ease-in-out;line-height:24px; font-size:12px;background:#E67016; border-color:#E67016; }
#c_page_tab2 a:hover{background:#E67016; border-color:#E67016; color:#fff;}




.page_nav_table{margin-top:30px;}

.art_list li{ padding:1% 0;}
.art_list .newbt{width:75%; overflow:hidden; height:1.4rem;}
.art_list li a:hover .newbt{ color:#F4B915;}
.dateTime{ float:right;}



select,textarea,button{appearance:none; -o-appearance:none; -ms-appearance:none; -moz-appearance:none; -webkit-appearance:none; resize: none; border-radius:0; outline: none; border:none; background-color:transparent; font-family:inherit; font-size:inherit; color:inherit; box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box; vertical-align:middle;}

input[type="button"],input[type="radio"],input[type="checkbox"],input[type="submit"],button {cursor:pointer;}


/*register*/
.page-formTable{ padding:50px 20px 70px 20px; background:#eff2f4;margin:0 auto; background-size:cover;}
.formTableBox{/*padding:65px 85px  55px 85px;*/margin:0 auto;}
.formTableBox .title2{font-size:18px;color:#1f3650;line-height: normal;display:block;margin-bottom:35px;margin-top:35px;text-align:center;}
.page-formTable .w605{max-width:453px;}
.page-formTable .w1030{ width:92%;max-width:1030px;margin:0 auto;}
.page-formTable .w1030 .formTableBox{padding:0 45px;}

.formTable{font-size:14px;color:#464646;line-height: 30px;text-align:left;padding:0;margin:0 auto;text-align:center;}
.formTable table{ width:100%;}
.formTable label{display:block;}
.formTable .l1{float:left;width: 50%; position:relative;}
.formTable .l2{float:left;width: 100%;}
.formTable .l5{padding:0 40px;margin-top:23px;}
.formTable .l1Box{margin:0 9px;}
.formTable .text{display:block;width: 94%;padding:4px 3% 4px 3%;height:53px;margin-bottom:19px;outline:none;font-size:14px;color:#a0a0a0;font-family:Arial,"微软雅黑",Sans-Serif;border-radius:5px;text-align:left;background:#ffffff;border:1px solid #ebebeb;}
.formTable .text2{display:block;width:100%;padding:10px 3% 10px 3%;height:106px;margin-bottom:19px;outline:none;font-size:14px;color:#a0a0a0;font-family:Arial,"微软雅黑",Sans-Serif;border:none;border-radius:5px;background:#ffffff;text-align:left; border:1px solid #ebebeb;}
.formTable .getcode{display:block;float:left;width:37%;padding:4px 3% 4px 3%;height:53px;margin-bottom:0;max-width:238px;
outline:none;font-size:14px;color:#a0a0a0;font-family:Arial,"微软雅黑",Sans-Serif;border-radius:5px;background:#ffffff;text-align:left; border:1px solid #ebebeb;}
.formTable label .l1Box .getcode{width:100%;max-width:167px; border:1px solid #ebebeb;}
.formTable .kk{font-size:14px;color:#a0a0a0; line-height:normal;margin-left:2px;}
.formTable .inputbtn{display:inline-block;width:100%;height: 53px;line-height: 53px;margin:0 5px;outline:none;font-size:14px;color: #fff;font-family: Arial,"微软雅黑",Sans-Serif;border:none;background-color: #b1b6bb;color:#fff;margin-bottom:15px;cursor:pointer;border-radius:5px;margin-top:45px;text-align:center;max-width:155px; }
.formTable .inputbtn.tj{background:#1f3650;}
.formTable .inputbtn:hover{background:#0061CF;}
.formTable .l5{ text-align:center;}
.formTable .text,.formTable .getcode{transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;}
.formTable .l3{width: 100%;float:none;margin:0 auto;}
.formTable .register{display:inline-block;width:100%;height:53px;line-height:53px;margin:0;outline:none;font-size:14px;color: #fff;font-family: Arial,"微软雅黑",Sans-Serif;border:none;background-color: #1f3650;color:#fff;margin-bottom:15px;cursor:pointer;border-radius:5px;margin-top:60px; text-align:center;}
.formTable .register:hover{background:#0061CF;}
.formTableBox a{color: #909090;}
.formTableBox a:hover{color: #b60005;}

.formTable .text:focus,.formTable .text:active,.formTable .getcode:focus,.formTable .getcode:active{-webkit-box-shadow: none;box-shadow: none;border: 2px solid rgba(94, 158, 255, 0.80); }
.page-formTable .formTable input::-webkit-input-placeholder { color: #b1b6bb; } 
.page-formTable .formTable input:-moz-placeholder { color: #b1b6bb; } 
.page-formTable .formTable input::-moz-placeholder { color: #b1b6bb; } 
.page-formTable .formTable input:-ms-input-placeholder { color: #b1b6bb; }  
@media screen and (max-width:1230px){
.formTableBox .title2{margin-bottom:30px;margin-top:30px;}
/*.formTableBox{padding:30px 0  50px 0;}*/
.page-formTable .w1030{ width:94%;}
.page-formTable .w1030 .formTableBox{padding:0 40px;}	
.formTable .t,
.formTable .l5{padding:0 3%; }
}
@media screen and (max-width:1140px){
.page-formTable .w1030 .formTableBox{padding:0;}	
}
@media screen and (max-width:767px){
.formTable .l1{float:none;width:100%;margin:0;}
.formTable .l2{float:none;width:100%;margin:0;    padding-left: 0;}
.formTable .kk{ display:inline-block; clear: both;}
.formTable .text{width: 94%;padding:4px 3% 4px 3%;}
.formTable .getcode{width:94%;padding:4px 3% 4px 3%;margin-bottom:10px;max-width:100%;}
.formTable label .l1Box{margin:0 auto;}
.formTable .l3{width:100%;}
}
@media screen and (max-width:530px){
.formTable .inputbtn{max-width:105px; }
}



.contactTable{ text-align:center;}

/*feedback*/
.feedback{font-size:14px;color:#464646;line-height: 30px;padding:0;margin:0 auto; display:inline-block;text-align:center;}
.feedback table{text-align:center;}
.feedback .t{font-size:12px;color:#333333;line-height: 30px;display:block;text-align:left;}
.feedback label{display:block;}
.feedback label.l1{float:left;width:395px;padding-right:10px;}
.feedback label.l2{float:right;width:395px;padding-left:10px;}
.feedback label.l3{width:810px;}
.feedback label.l5{ text-align:center;}
.feedback .text{display:block;width:100%;height:50px;margin-bottom:40px;padding:4px 10px 4px 10px;outline:none;font-size:14px;color:#a0a0a0;font-family:Arial,"微软雅黑",Sans-Serif;border:1px solid #ebebeb;background:#f8f8f8;border-radius:5px;text-align:left;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;}
.feedback .text2{display:block;width:100%;height:100px;margin-bottom:40px;padding:4px 10px 4px 10px;outline:none;font-size:14px;color:#a0a0a0;font-family:Arial,"微软雅黑",Sans-Serif;border:1px solid #ebebeb; background:#f8f8f8; border-radius:5px;margin-right:0;text-align:left;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;}
.feedback .input_Code{display:block;float:left;width:320px;height:50px;margin-bottom:5px;padding:4px 10px 4px 10px;margin-right:10px;
outline:none;font-size:14px;color:#a0a0a0;font-family:Arial,"微软雅黑",Sans-Serif;border: none;border:1px solid #ebebeb; background:#f8f8f8; border-radius:5px;text-align:left;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;}
.feedback .input{display:inline-block;width:107px; height:40px;line-height:40px;margin-right:10px;outline:none;font-size:14px;color:#464646;font-family:Arial,"微软雅黑",Sans-Serif;border-radius:38px; background:#dbdbdb;margin-top:60px;border:none;box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;}
.feedback .tj{background-color:#0061CF;color:#fff;}
.feedback .text,.feedback .text2,.feedback.input_Code{transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;}
@media screen and (max-width:1200px){
.feedback{display:block;}
.feedback table{ width:100%;}
.feedback label.l1{width:49%;}
.feedback label.l2{width:49%;padding-left:2%;}
.feedback label.l3{width:100%;}
.feedback .input_Code{width:320px;}
}
@media screen and (max-width:768px){
.feedback{padding:40px 0; display:block;}
.feedback table{ width:100%;}
.feedback label.l1{float:none;width:100%;margin:0;}
.feedback label.l2{float:none;width:100%;margin:0;    padding-left: 0;}
.feedback label.l3{margin:0 0;}
.feedback .kk{ display:block; clear: both;}
.feedback .input_Code{width:100%;}
.feedback .input{width: 100%;margin:10px 0; text-align:center;}
}







/*page-newsInfo*//*
.page-newsInfo{text-align:left;}
.page-newsInfo .page-newswz{border-bottom: 1px solid #e5e5e5;}
.page-newsInfo .newsPositon{ font-size:12px; color:#464646; line-height:30px;padding:6.5px 19px;border-bottom: 1px solid #e5e5e5; display:none;}
.page-newsInfo .newsPositon a{color:#464646;}
.page-newsInfo .newsPositon a:hover{color:#b60005;}
.page-newsInfo .newsTit{ font-size:26px; color:#000000; line-height: normal; text-align:center; font-weight:600;margin-bottom:10px;padding-top:50px;margin:0 0;}
.page-newsInfo .newsShuju{ font-size:12px; color:#909090;line-height:30px;text-align:center; padding-top:20px;padding-bottom:34px; }
.page-newsInfo .newsShuju span{ display:inline-block;margin:0 10px;font-size:12px; color:#909090;line-height:30px;}
.page-newsInfo .newsShuju span.s{padding-left:20px;background:url(../images/school-see.png) left center no-repeat;}
.page-newsInfo .newsShuju span.d{padding-left:20px;background:url(../images/d.png) left center no-repeat;}
.page-newsInfo .newsShuju span.u{padding-left:20px;background:url(../images/u.png) left center no-repeat;}
.page-newsInfo .newsText{margin:0 0;text-align:left;font-size:15px;color:#464646;line-height:30px;border-top:1px solid #e5e5e5;}
.page-newsInfo .newsTextNr{padding-top:40px;padding-bottom:20px;}

.page-newsInfo .newsBtn{border-bottom:1px solid #e2e2e2;padding-bottom:20px;font-size:0;}
.page-newsInfo .newsBtn .newsBtnA,.page-newsInfo .newsBtn .newsBtnB{display:inline-block;*display:inline;zoom:1;width:50%; line-height:30px; height:30px; overflow:hidden;color:#595959;font-size:13px;}
.page-newsInfo .newsBtn a{display:inline-block;max-width:90%;line-height:14px; height:14px;color:#595959; white-space:nowrap;text-overflow: ellipsis; overflow: hidden;}
.page-newsInfo .newsBtn a:hover{color:#40705A;}
.page-newsInfo .newsBtn .newsBtnA{text-align:left;}
.page-newsInfo .newsBtn .newsBtnB{ text-align:right;}
.page-newsInfo .newsBtn a srtong{ color:#333333;}
.page-newsInfo .newsBtn .newsBtnA span{padding-left:10px;border-left:3px solid #40705A;}
.page-newsInfo .newsBtn .newsBtnB span{padding-right:10px;border-right:3px solid #40705A;}
@media screen and (max-width:1220px) {
	.page-newsInfo{ padding:45px 30px;}
	
}
@media screen and (max-width:992px) {
  .page-newsInfo{ padding:30px 20px;}
  .page-newsInfo .newsTit,.page-newsInfo .newsText{margin:0 0;}
  .page-newsInfo .newsTit{ font-size:24px;padding-top:30px;}
  .page-newsInfo .newsTextNr{padding-top:30px;padding-bottom:20px;}
}
@media screen and (max-width:768px) {
  .page-newsInfo .newsBtn span{width:100%; }
  .page-newsInfo .newsBtn .newsBtnB{ text-align:right;}
  .page-newsInfo .newsTit{ font-size:18px;}
  .page-newsInfo .newsBtn span,.page-newsInfo .newsBtn a{line-height:20px; height:20px; }
  .page-newsInfo .newsBtn .newsBtnA{text-align:left; width:100%; display:block;}
.page-newsInfo .newsBtn .newsBtnB{ text-align:left; width:100%;display:block;}
.page-newsInfo .newsBtn .newsBtnB span{padding-right:none;border-right:none;padding-left:10px;border-left:3px solid #40705A;}
}
*/


/*page-back*/
















/*page-tab*/
.page-tab{margin:0 auto;padding:30px 0 50px 0;text-align: center; }
.page-tab ul { margin:0 auto;padding: 0;list-style: none; text-align:center;height: 38px;line-height: 38px;font-size: 0;}
.page-tab ul li{margin: 0;padding: 0;display: inline-block;position: relative;height: 36px;line-height: 36px;}
.page-tab ul li a{display: inline-block;text-align:center;font-size:14px;color: #5e5e5e;padding:0 15px;height: 36px;line-height: 36px;border: 1px solid #dcdcdc;background: transparent;}
.page-tab ul li a:hover,
.page-tab ul li a.focus{ background:#cbe3fd; color: #0061cf; }
.page-tab ul li.next-page, .page-tab ul li.prev-page{ margin:0 20px;}
.page-tab ul li.next-page a, .page-tab ul li.prev-page a{padding: 0 28px;}

/*page-back*/
/*.page-back{ margin:0 auto; text-align:center;padding:30px 0;border-top:1px dashed #d4d4d4;border-bottom:1px dashed #d4d4d4;}
.page-back a{display:block;width:200px;font-size: 12px;line-height: 40px;height:40px;overflow:hidden; text-align:center;color: #5e5e5e;margin:0 auto;-webkit-transition: 0.3s;-moz-transition: 0.3s;transition: 0.3s; background:#f2f2f2; }
.page-back a:hover{background:#0061cf; color:#fff;}*/







/*clearbox*/
.pageList ul{margin:0 -18.75px;font-size:0;}
.pageList ul li.listpic{display:inline-block;*display:inline;zoom:1;margin:0;padding:0;list-style:none;width:33.333333%;margin-bottom:25px; vertical-align:top;}
.pageList ul li.listpic a{ display:block;margin:0 18.75px;}
.pageList ul li.listpic .ImgBoxB{width:245px;height:210px;margin:0 auto; text-align:center;}
.pageList ul li.listpic .ImgBox{width:245px;height:210px;overflow:hidden; position:relative;margin:0 auto;display: table-cell;text-align:center;vertical-align:middle;}
.pageList ul li.listpic .ImgBox img{max-width:100%; max-height:100%;width:auto; vertical-align:middle;margin:auto;-moz-transition: all 0.6s ease;-webkit-transition: all 0.6s ease;-o-transition: all 0.6s ease;-ms-transition: all 0.6s ease;transition: all 0.6s ease;}
.pageList ul li.listpic:hover .ImgBox img {-webkit-transform: scale(1.05);-ms-transform: scale(1.05);transform: scale(1.05)}
.pageList ul li.listpic .ImgBox:before{content:""; display:block; position:absolute; left:0; top:0;bottom:0; right:0;border:1px solid #e8e8e8;border-bottom:none;}
.pageList ul li.listpic .ImgBox:after{opacity:0;content:""; display:block; position:absolute; left:0; top:0; z-index:5; width:100%; height:100%; background-color:#000;-moz-transition: all 0.6s ease;-webkit-transition: all 0.6s ease;-o-transition: all 0.6s ease;-ms-transition: all 0.6s ease;transition: all 0.6s ease;}
.pageList ul li.listpic:hover .ImgBox:after{opacity:0.3;}
.pageList ul li.listpic .listTitle{ text-align:left;background:#f2f2f2;font-size:14px;color:#333333; padding:0 40px 0 30px; position: relative;}
.pageList ul li.listpic .listTitle:after{content:""; display:block; position:absolute; right:25px; top:0; z-index:1; width:8px; height:100%;background:url(../images/m.png) right center no-repeat;-moz-transition: all 0.6s ease;-webkit-transition: all 0.6s ease;-o-transition: all 0.6s ease;-ms-transition: all 0.6s ease;transition: all 0.6s ease;}
.pageList ul li.listpic .listTitle .t{ display:block;line-height:54px;height:54px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.pageList ul li.listpic .listTitle,.pageList ul li.listpic,.pageList ul li.listpic a{-moz-transition: all 0.6s ease;-webkit-transition: all 0.6s ease;-o-transition: all 0.6s ease;-ms-transition: all 0.6s ease;transition: all 0.6s ease;}
.pageList ul li.listpic:hover .listTitle{ background:#0061cf; color:#fff;}
.pageList ul li.listpic:hover .listTitle:after{ background-image:url(../images/m2.png);}
.pageList ul li.listpic:hover a{box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);}
@media screen and (max-width: 1250px){
.pageList ul{margin:0 -10px;font-size:0;}	
.pageList ul li.listpic a{margin:0 10px;}
.pageList ul li.listpic .ImgBoxB,
.pageList ul li.listpic .ImgBox{width:320px;height:179px;}
}
@media screen and (max-width:1024px){
	.pageList ul{margin:0 -5px;}
	.pageList ul li.listpic{ width:50%;margin-bottom:25px;}
	.pageList ul li.listpic a{margin:0 5px;}
	.pageList ul li.listpic .ImgBoxB,
	.pageList ul li.listpic .ImgBox{width:44.43359375vw;height:24.90234375vw;}
}
@media screen and (max-width:768px){
	.pageList ul{margin:0;}
	.pageList ul li.listpic{width:100%;margin-bottom:15px;}
	.pageList ul li.listpic a{margin:0;}
	.pageList ul li.listpic .ImgBoxB,
	.pageList ul li.listpic .ImgBox{width:100%;height:210px; display:block;}
}
@media screen and (max-width:420px){
	.pageList ul li.listpic .ImgBoxB,
	.pageList ul li.listpic .ImgBox{width:100%;height:50vw;}
}





/*proList2 listpic*/
.proList2{margin-bottom:0;width:auto;padding:60px 40px;display:block!important;-webkit-box-shadow: 0px 0px 9px rgba(0,0,0,.1);-moz-box-shadow: 0px 0px 9px rgba(0,0,0,.1);box-shadow: 0px 0px 9px rgba(0,0,0,.1); background:#fff;}
.proList2 ul{font-size:0; line-height:normal;}
.proList2 ul li.listpic{font-size:0;display:inline-block;*display:inline;zoom:1;margin:0;padding:0;list-style:none;width:100%;position:relative;}
.proList2 ul li.listpic a{display:block;position: relative;padding:45px 0; overflow:hidden; position:relative;border-bottom:1px dashed #d4d4d4;position:relative;}
.proList2 ul li.listpic .ImgBox{width:535px;height:370px;overflow:hidden; float:left; position:relative;}
.proList2 ul li.listpic .ImgBox img{height:370px; width:100%; display:block;}
.proList2 ul li.listpic .ImgBox img,.proList2 ul li.listpic .ImgBox:after{-webkit-transition: all 0.5s ease-in-out;-moz-transition: all 0.5s ease-in-out;-o-transition: all 0.5s ease-in-out;-ms-transition: all 0.5s ease-in-out;transition: all 0.5s ease-in-out;}
.proList2 ul li.listpic a:hover .ImgBox img{-moz-transform: scale(1.05); -webkit-transform: scale(1.05);transform: scale(1.05);}
/*.proList2 ul li.listpic .ImgBox:after{content: '';position:absolute;top:0;left:0;height:100%;width:100%;background:#000;filter:alpha(opacity=0);-moz-opacity:0;-khtml-opacity:0; opacity: 0;}
.proList2 ul li.listpic a:hover .ImgBox:after{filter:alpha(opacity=10); -moz-opacity:0.1; -khtml-opacity: 0.1; opacity: 0.1;}*/
.proList2 ul li.listpic .listTitle{overflow:hidden;display:block;padding:0;}
.proList2 ul li.listpic .listTitle .listTitleBox{padding:85px 100px 0 100px; }
.proList2 ul li.listpic .listTitle .t{font-size: 20px;color:#0061cf;height:30px;line-height:30px; overflow:hidden; font-weight:600;margin-bottom:15px;}
.proList2 ul li.listpic .listTitle .t2{font-size:14px;color:#333333;height:130px;line-height:26px; overflow:hidden;}
.proList2 ul li.listpic .more2{font-size:12px;color:#0061cf;background:#fff;text-transform:uppercase;width:86px;height:33px;line-height:33px;cursor:pointer; position:relative; border:2px solid #0061cf;border-radius:36px; position:absolute; top:50%; margin-top:-18px; right:0;}
.proList2 ul li.listpic .more2{-webkit-transition: all 0.5s ease-in-out;-moz-transition: all 0.5s ease-in-out;-o-transition: all 0.5s ease-in-out;-ms-transition: all 0.5s ease-in-out;transition: all 0.5s ease-in-out;}
.proList2 ul li.listpic .more2:hover{background:#0061cf;color:#fff;}
.proList2 ul li.listpic .more2 span{ display:block; text-align:center; position:relative; z-index:1;}
@media screen and (max-width:1220px) {
.proList2{padding:30px 3%; width:94%!important;display:block!important;}
.proList2 ul li.listpic a{padding:30px 0 ;}
.proList2 ul li.listpic .ImgBox{width:455px;height:315px;}
.proList2 ul li.listpic .ImgBox img{height:315px;}
.proList2 ul li.listpic .listTitle .listTitleBox{padding:80px 100px 30px 50px;}
}
@media screen and (max-width:1024px) {
.proList2 ul li.listpic a{padding:30px 0 ;}
.proList2 ul li.listpic .ImgBox{width:100%;height:auto;overflow:hidden; float:none;}
.proList2 ul li.listpic .ImgBox img{height:auto; width:100%; display:block;}
.proList2 ul li.listpic .listTitle .listTitleBox{padding:30px 100px 30px 0; position:relative;}
.proList2 ul li.listpic .listTitle .t{font-size: 18px;margin-bottom:10px;}
.proList2 ul li.listpic .listTitle .t2{max-height:210px; height:auto;margin-bottom:15px; font-size:13px;}
.proList2 ul li.listpic:before{ display:none;}
}
@media screen and (max-width:768px) {
	.proList2{padding:0px 0; width:100%!important;display:block!important;-webkit-box-shadow: 0px 0px 0 rgba(0,0,0,0);-moz-box-shadow: 0px 0px 0 rgba(0,0,0,0);box-shadow: 0px 0px 0 rgba(0,0,0,0); }
	.proList2 ul li.listpic .listTitle .listTitleBox{padding:30px 0; position:relative; text-align:center;}
	.proList2 ul li.listpic .more2{ position:static;  margin:0 auto;margin-top:20px; top:auto;}
	
	
}

.pageall .page-newsInfo{ float:none; width:100%;}

