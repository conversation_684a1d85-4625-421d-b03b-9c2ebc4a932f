/* CSS Document */
#dialog{
	background:#000 \9;filter:alpha(opacity=80);
	background:rgba(0,0,0,0.8);
	width:100%;
	height:100%;
	position:fixed;
	left:0px;
	top:0px;
	z-index:1010;
	display:none
	}
.hide{ display:none;}
.dialog {
    position: fixed;
	z-index:100;
    top: 50%;
    left: 50%;
    width: 540px;
    padding-bottom:65px;
    margin-left: -270px;
    margin-top: -190px;
    text-align: center;
    box-shadow: 0 4px 4px 0 rgba(0,0,0,0.10);
    border-radius: 30px;
    background-color: #fff
}
.dialog .desc {
    margin-top:40px;
    margin-bottom: 40px;
	padding:0px 40px;
	font-size:22px;
	line-height:30px;
	color:#8f9bb2;
}
.dialog .icon {
    margin-top: 65px;
    height: 109px;
	}
.dialog .icon img{
	width:100%;
	max-width:109px;
	}	
.dialog .btn {
    width: 125px;
    height: 45px;
    border: none;
    outline: none;
    background: #2b64a7;
    border-radius: 22px;
    font-size: 16px;
    color: #FFF;
    cursor: pointer;}
.dialog .close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 22px;
    height: 22px;
    outline: none;
    border: none;
    cursor: pointer;
    background: url(../images/close.png) center no-repeat;	
}
@media screen and (max-width:1002px){
	
.dialog {  
    width: 340px;
    padding-bottom:30px;
    margin-left: -170px;
    margin-top: -190px;
    text-align: center;
    border-radius: 15px;
    background-color: #fff
}
	
	.dialog .icon {
    margin-top:30px;
    height: 55px;
	}
	.dialog .icon img {
	max-width:55px;
    height: 55px;
	}
.dialog .desc {
    margin-top:15px;
    margin-bottom: 15px;
	font-size:14px;
	line-height:16px;
}	
.dialog .btn {
    width: 65px;
    height: 25px; 
    font-size: 14px;
}	
	}
@media screen and (max-width:340px){

.dialog {   
    width: 240px;
    padding-bottom:30px;
    margin-left: -120px;
    margin-top: -90px;
    text-align: center;
    border-radius: 15px;
    background-color: #fff
}
}
