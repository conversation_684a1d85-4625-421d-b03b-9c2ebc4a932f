@charset "utf-8";
*, ::after, ::before {	-webkit-box-sizing: border-box;	-moz-box-sizing: border-box;box-sizing: border-box}

@font-face{font-family:"iconfont";/* Project id 3197750 */
  src:url('../fonts/iconfont.woff2') format('woff2'),url('../fonts/iconfont.woff') format('woff'),url('../fonts/iconfont.ttf') format('truetype')}
.iconfont{font-family:"iconfont" !important;font-size:16px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
.icon-qq1:before{content:"\e600"}
.icon-danjiantouyou:before{content:"\e629"}
.icon-danjiantouzuo:before{content:"\e63c"}
.icon-xiala:before{content:"\e6b9"}
.icon-weixin:before{content:"\e660"}
.icon-youjiantou:before{content:"\e624"}
.icon-jiantou-xia:before{content:"\e719"}
.icon-ic_search24px:before{content:"\e616"}
.icon-youxiang:before{content:"\e6db"}
.icon-jiantou-you:before{content:"\e631"}
.icon-expand_less:before{content:"\e731"}


@font-face {
    font-family: 'Tunga';
    src: url('../fonts/tunga.eot');
    src: url('../fonts/tunga.eot') format('embedded-opentype'),
        url('../fonts/tunga.woff2') format('woff2'),
        url('../fonts/tunga.woff') format('woff'),
        url('../fonts/tunga.ttf') format('truetype'),
        url('../fonts/tunga.svg#tunga') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Tunga Bold';
    src: url('../fonts/tungab.eot');
    src: url('../fonts/tungab.eot') format('embedded-opentype'),
        url('../fonts/tungab.woff2') format('woff2'),
        url('../fonts/tungab.woff') format('woff'),
        url('../fonts/tungab.ttf') format('truetype'),
        url('../fonts/tungab.svg#tungab') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0px;border:0;font-size:100%;outline:none;-webkit-font-smoothing:subpixel-antialiased}
html{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:subpixel-antialiased}

/* always display scrollbars */
body{font:12px/1.8 "PingFangSCMedium",Microsoft Yahei,Hiragino Sans GB,Microsoft Sans Serif,WenQuanYi Micro Hei,sans-serif;margin:0 auto;color:#454545;background-color:#fff;_background-attachment:fixed;_background-image:url(about:blank);overflow-y:visible\9;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:subpixel-antialiased;background-position:center;outline:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}
::-webkit-scrollbar{width:6px;border-radius:3px;background-color:#e2e2e2}
::-webkit-scrollbar-track{border-radius:6px;border-radius:3px;background-color:#e2e2e2}
::-webkit-scrollbar-thumb{border-radius:4px;background-color:#2b64a7}
article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}
input,select{vertical-align:middle}
ol,ul{list-style:none}
blockquote,q{quotes:none}
blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}
strong{font-weight:bold}
input{outline:none;padding:0}
img{border:0}
input[type="text"],input[type="button"],input[type="submit"],input[type="reset"],select{-webkit-appearance:none;border-radius:0}
textarea{-webkit-appearance:none;border-radius:0;background:none;outline:none;padding:0;margin:0;border:none}
::-moz-selection{background:#2b64a7;color:#fff;text-shadow:none}
::selection{background:#2b64a7;color:#fff;text-shadow:none}
a{-webkit-transition:0.3s;-moz-transition:0.3s;-o-transition:0.3s;transition:0.3s}
a:link,a:visited{text-decoration:none;color:inherit}
a:active,a:hover{text-decoration:none;color:inherit}
.fc{text-align:center}
.fr{text-align:right}
.fl{float:right}
.vt{vertical-align:top}
.vm{vertical-align:middle}

.inner{width:1200px;margin:0px auto}
.innerWidth{width:1440px;margin:auto}

html.has-scroll-smooth{overflow:hidden}
html.has-scroll-dragging{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
.has-scroll-smooth body,.has-scroll-smooth html{overflow:hidden}
.has-scroll-smooth [data-scroll-container]{min-height:100vh;overflow:hidden}
.c-scrollbar{position:absolute;right:0;top:0;width:11px;height:100vh;transform-origin:center right;transition:transform 0.3s,opacity 0.3s;/* opacity:0;*/
  z-index:99999;/* mix-blend-mode:difference */}
.c-scrollbar:hover{transform:scaleX(1.45)}
.c-scrollbar:hover,.has-scroll-scrolling .c-scrollbar,.has-scroll-dragging .c-scrollbar{opacity:1}
.c-scrollbar_thumb{position:absolute;top:0;right:0;background-color:rgba(0,0,0,0.8);opacity:0.5;width:7px;border-radius:10px;margin:2px;cursor:-webkit-grab;cursor:grab}
.has-scroll-dragging .c-scrollbar_thumb{cursor:-webkit-grabbing;cursor:grabbing}

.popUpblack{background:#000 \9;filter:alpha(opacity=80);background:rgba(0,0,0,0.8);width:100%;height:100%;position:fixed;left:0px;top:0px;z-index:1010;display:none}
.popUp{width:220px;border:2px solid #2b64a7;background:#fff;position:fixed;left:50%;top:50%;margin:-87px 0 0 -115px;z-index:1004}
.popUp .t{padding:0 0 0 10px;height:26px;line-height:26px;color:#666;font-weight:bold;border-bottom:1px solid #e8e8e8;background:#f2f2f2}
.popUp .t .close{padding:0 10px 0 0;float:right;cursor:pointer;color:#666;font-weight:normal}
.popUp .img{padding:20px}
.popUp .img img{width:100%;display:block}

/*slick-list*/
.slick-list{overflow:hidden;width:100%;height:100%}
.slick-track{position:relative;left:0;top:0;display:block;zoom:1}
.slick-track:before,.slick-track:after{content:"";display:table}
.slick-track:after{clear:both}
/**/
.slick-dots{width:100%;text-align:center;position:absolute;left:0;bottom:27px;line-height:0;z-index:10}
.slick-dots li{display:inline-block;margin-right:8px;line-height:0}
.slick-dots li:last-child{margin-right:0}
.slick-dots li button{display:block;padding:0;margin:0;width:8px;height:8px;background-color:#fff;line-height:1;border:none;text-indent:99999px;overflow:hidden;outline:none;cursor:pointer;border-radius:50%;transition:0.3s}
.slick-dots li.slick-active button{background:#2b64a7}

