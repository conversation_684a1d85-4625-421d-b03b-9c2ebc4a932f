/* 解决方案导航按钮样式 */
.indexCaselist .item {
    position: relative;
}

.indexCaselist .cases-button-prev,
.indexCaselist .cases-button-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    margin-top: 0;
    z-index: 10;
    cursor: pointer;
    background-color: rgba(43, 100, 167, 0.8);
    background-size: 16px;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 0.9;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.indexCaselist .cases-button-prev {
    left: 10px;
    background-image: url(../images/s1.png);
    background-position: center;
    background-repeat: no-repeat;
}

.indexCaselist .cases-button-next {
    right: 10px;
    background-image: url(../images/s.png);
    background-position: center;
    background-repeat: no-repeat;
}

.indexCaselist .cases-button-prev:hover,
.indexCaselist .cases-button-next:hover {
    background-color: rgba(43, 100, 167, 1);
    transform: translateY(-50%) scale(1.1);
    opacity: 1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
    .indexCaselist .cases-button-prev,
    .indexCaselist .cases-button-next {
        width: 30px;
        height: 30px;
        background-size: 12px;
    }
}