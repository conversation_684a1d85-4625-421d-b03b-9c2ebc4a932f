/* 解决方案轮播导航按钮样式 */
.solution-prev, .solution-next {
	display: block;
	text-align: center;
	border-radius: 50%;
	color: #fff;
	background: rgba(0,0,0,0.2);
	cursor: pointer;
	transition: 0.32s;
	width: 44px;
	height: 44px;
	line-height: 44px;
	font-size: 14px;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	z-index: 10;
}
.solution-prev:hover, .solution-next:hover {
	background: rgba(0,0,0,0.4);
	line-height: 40px;
}
.solution-prev {
	left: 20px;
}
.solution-next {
	right: 20px;
}
.solution-prev:after, .solution-next:after {
	content: "";
	font-family: "iconfont" !important;
}
.solution-prev:after {
	content: "\e63c"; /* 使用icon-danjiantouzuo图标 */
}
.solution-next:after {
	content: "\e629"; /* 使用icon-danjiantouyou图标 */
}

@media screen and (max-width: 768px) {
.solution-prev, .solution-next {
	width: 36px;
	height: 36px;
	line-height: 36px;
	font-size: 12px;
}
.solution-prev:hover, .solution-next:hover {
	line-height: 34px;
}
.solution-prev {
	left: 10px;
}
.solution-next {
	right: 10px;
}
}
/* 确保轮播项目不受导航按钮影响 */
.indexCaselist .item {
	overflow: hidden;
}

/* 合作伙伴轮播样式修复 */
.indexPartners {
	padding: 70px 0;
	background: linear-gradient(135deg, #f5f7fa 0%, #e6eaf0 100%);
	position: relative;
	overflow: hidden;
}
.indexPartners:before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(circle, transparent 20%, #f7f7f7 20%, #f7f7f7 21%, transparent 21%, transparent 34%, #f7f7f7 34%, #f7f7f7 35%, transparent 35%);
	background-size: 25px 25px;
	opacity: 0.3;
	pointer-events: none;
}
.partners-tech-container {
	padding: 40px 20px;
	max-width: 1200px;
	margin: 0 auto;
}
.partners-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30px;
	justify-content: center;
}
.partner-item {
	perspective: 1000px;
	-webkit-perspective: 1000px;
	-moz-perspective: 1000px;
	height: 154px;
	width: 100%;
	cursor: pointer;
	position: relative;
	margin-bottom: 30px;
	transition: transform 0.3s ease;
}
.partner-item:hover {
	transform: translateY(-5px);
}
.partner-inner {
	position: relative;
	width: 100%;
	height: 70%;
	text-align: center;
	transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	-moz-transform-style: preserve-3d;
	box-shadow: 0 5px 15px rgba(0,0,0,0.08);
	border-radius: 15px;
	background: rgba(255,255,255,0.92);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255,255,255,0.3);
	overflow: hidden;
	transition: box-shadow 0.3s ease;
}
.partner-item:hover .partner-inner {
	box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
.partner-item:hover .partner-inner {
	transform: rotateY(180deg);
	-webkit-transform: rotateY(180deg);
	-moz-transform: rotateY(180deg);
}
.partner-item.flipped .partner-inner {
	transform: rotateY(180deg);
	-webkit-transform: rotateY(180deg);
	-moz-transform: rotateY(180deg);
}
.partner-front, .partner-back {
	position: absolute;
	width: 100%;
	height: 100%;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 20px;
	box-sizing: border-box;
}
.partner-front {
	background-color: white;
	border-radius: 15px;
	z-index: 2;
	display: flex;
	justify-content: center;
	align-items: center;
}
.partner-back {
	background: linear-gradient(135deg, #2b64a7 0%, #164785 100%);
	color: white;
	transform: rotateY(180deg);
	-webkit-transform: rotateY(180deg);
	-moz-transform: rotateY(180deg);
	border-radius: 15px;
	z-index: 1;
}
.partner-logo {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}
.partner-logo img {
	max-width: 90%;
	max-height: 90%;
	object-fit: contain;
	transition: opacity 0.3s ease, transform 0.3s ease;
	transform: translateZ(0); /* 避免3D闪烁 */
}
.partner-item:hover .partner-logo img {
	transform: translateZ(40px);
}
.partner-title {
	margin-top: 15px;
	font-size: 14px;
	color: #333;
	text-align: center;
	font-weight: bold;
}
.partner-desc {
	font-size: 13px;
	line-height: 1.4;
	text-align: center;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
	.partners-grid {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media screen and (max-width: 768px) {
	.partners-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 20px;
	}
	.partner-item {
		height: 140px;
	}
	.partner-desc {
		font-size: 12px;
	}
}

@media screen and (max-width: 480px) {
	.partners-grid {
		grid-template-columns: repeat(1, 1fr);
	}
	.partner-item {
		height: 130px;
		max-width: 250px;
		margin-left: auto;
		margin-right: auto;
	}
}

/* 卡片加载状态 */
.partner-logo img {
	opacity: 0;
	transition: opacity 0.5s ease;
}
.partner-logo img.loaded {
	opacity: 1;
}
.partner-item.image-loaded .partner-logo img {
	opacity: 1;
}
.partner-item.image-error .partner-logo {
	background: #f8f8f8;
}

/* 卡片动画 */
.partner-item {
	opacity: 0;
	transform: translateY(30px);
	transition: opacity 0.8s ease, transform 0.8s ease;
}
.partner-item.animated {
	opacity: 1;
	transform: translateY(0);
}

/* 加入3D光效 */
.partner-inner:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0) 100%);
	background-position: 50% 50%;
	background-size: 300% 300%;
	z-index: 3;
	transition: opacity 0.3s ease;
	opacity: 0;
}
.partner-item:hover .partner-inner:before {
	opacity: 1;
}

/* 当不支持 3D 变换时的降级处理 */
.no-3d-support .partner-item {
	perspective: none;
}
.no-3d-support .partner-inner {
	transform-style: flat;
}
.no-3d-support .partner-back {
	opacity: 0;
	transform: none;
}
.no-3d-support .partner-item:hover .partner-inner {
	transform: none;
}
.no-3d-support .partner-item:hover .partner-back {
	opacity: 1;
}