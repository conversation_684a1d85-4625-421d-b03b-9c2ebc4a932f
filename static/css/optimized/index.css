/* 优化版本的index.css - 合并了所有内联样式 */

/* 机房枢纽和地图相关样式 */
.map-container {
    position: relative;
    width: 100%;
    height: 600px;
    overflow: hidden;
    background-color: rgba(32, 35, 42, 0.97);
    border-radius: 8px;
}

.china-map {
    width: 100%;
    height: 100%;
    position: relative;
}

.china-map img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    opacity: 0.8;
}

.map-point {
    position: absolute;
    width: 20px;
    height: 20px;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 10;
}

.point-dot {
    position: absolute;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #ff6600;
    box-shadow: 0 0 12px #ff6600;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    transition: transform 0.3s ease;
}

.point-pulse {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 102, 0, 0.3);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    z-index: 1;
}

.pulse1 {
    animation: pulse 2s infinite;
}

.pulse2 {
    animation: pulse 2s infinite;
    animation-delay: 0.5s;
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
    }
    70% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0;
    }
}

.point-info {
    position: absolute;
    width: 240px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    padding: 15px;
    visibility: hidden;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
    z-index: 20;
}

.info-left .point-info {
    right: 30px;
    top: -60px;
}

.info-right .point-info {
    left: 30px;
    top: -60px;
}

.point-info h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.point-info p {
    margin: 0 0 15px 0;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.stats {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.stat-item {
    text-align: center;
    width: 33%;
}

.stat-number {
    font-size: 18px;
    font-weight: 700;
    color: #ff6600;
    margin-bottom: 5px;
}

.stat-number.percent:after {
    content: '%';
}

.stat-number.plus:after {
    content: '+';
}

.stat-label {
    font-size: 12px;
    color: #999;
}

.map-point.info-visible .point-info {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

.map-point.other-hidden {
    opacity: 0.3;
}

/* 动画效果 */
.stat-number.counting {
    animation: countUp 2s ease-out forwards;
}

@keyframes countUp {
    0% {
        content: "0";
    }
    100% {
        content: attr(--target-num);
    }
}

/* 科技感粒子效果 */
.tech-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    background-color: #fff;
    border-radius: 50%;
    pointer-events: none;
}

/* 数据中心信息 */
.datacenter-info {
    padding: 30px;
    margin-top: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.datacenter-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
}

.tech-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.tech-stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
    width: 22%;
}

.tech-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tech-stat-number {
    font-size: 36px;
    font-weight: 700;
    color: #0066cc;
    margin-bottom: 10px;
}

.tech-stat-label {
    font-size: 14px;
    color: #666;
}

/* 新闻中心样式 */
.news-container {
    display: flex;
    background-color: #f9f9f9;
    border-radius: 10px;
    overflow: hidden;
    height: 440px;
}

.news-list {
    width: 35%;
    overflow-y: auto;
    background-color: #fff;
    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
}

.news-item {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.news-item:hover, .news-item.active {
    background-color: #f5f7fa;
}

.news-content {
    width: 65%;
    position: relative;
}

.news-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
}

.news-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 30px;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: #fff;
}

.news-title {
    font-size: 24px;
    margin-bottom: 10px;
    font-weight: 600;
}

.news-date {
    font-size: 14px;
    margin-bottom: 15px;
    opacity: 0.8;
}

.news-desc {
    font-size: 16px;
    line-height: 1.6;
}

/* 粒子动画 */
@keyframes flicker {
    0% { opacity: 0.3; }
    50% { opacity: 0.8; }
    100% { opacity: 0.3; }
}

/* 合并来自原始index.css的样式 */
/* 解决方案轮播导航按钮样式 */
.solution-prev, .solution-next {
	display: block;
	text-align: center;
	border-radius: 50%;
	color: #fff;
	background: rgba(0,0,0,0.2);
	cursor: pointer;
	transition: 0.32s;
	width: 44px;
	height: 44px;
	line-height: 44px;
	font-size: 14px;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	z-index: 10;
}
.solution-prev:hover, .solution-next:hover {
	background: rgba(0,0,0,0.4);
	line-height: 40px;
}
.solution-prev {
	left: 20px;
}
.solution-next {
	right: 20px;
}
.solution-prev:after, .solution-next:after {
	content: "";
	font-family: "iconfont" !important;
}
.solution-prev:after {
	content: "\e63c"; /* 使用icon-danjiantouzuo图标 */
}
.solution-next:after {
	content: "\e629"; /* 使用icon-danjiantouyou图标 */
}

@media screen and (max-width: 768px) {
.solution-prev, .solution-next {
	width: 36px;
	height: 36px;
	line-height: 36px;
	font-size: 12px;
}
.solution-prev:hover, .solution-next:hover {
	line-height: 34px;
}
.solution-prev {
	left: 10px;
}
.solution-next {
	right: 10px;
}
}
/* 确保轮播项目不受导航按钮影响 */
.indexCaselist .item {
	overflow: hidden;
}

/* 合作伙伴轮播样式修复 */
.indexPartners {
	padding: 70px 0;
	background: linear-gradient(135deg, #f5f7fa 0%, #e6eaf0 100%);
	position: relative;
	overflow: hidden;
}
.indexPartners:before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(circle, transparent 20%, #f7f7f7 20%, #f7f7f7 21%, transparent 21%, transparent 34%, #f7f7f7 34%, #f7f7f7 35%, transparent 35%);
	background-size: 25px 25px;
	opacity: 0.3;
	pointer-events: none;
}
.partners-tech-container {
	padding: 40px 20px;
	max-width: 1200px;
	margin: 0 auto;
}
.partners-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30px;
	justify-content: center;
}
.partner-item {
	perspective: 1000px;
	-webkit-perspective: 1000px;
	-moz-perspective: 1000px;
	height: 154px;
	width: 100%;
	cursor: pointer;
	position: relative;
	margin-bottom: 30px;
	transition: transform 0.3s ease;
}
.partner-item:hover {
	transform: translateY(-5px);
}
.partner-inner {
	position: relative;
	width: 100%;
	height: 70%;
	text-align: center;
	transform-style: preserve-3d;
	-webkit-transform-style: preserve-3d;
	-moz-transform-style: preserve-3d;
	box-shadow: 0 5px 15px rgba(0,0,0,0.08);
	border-radius: 15px;
	background: rgba(255,255,255,0.92);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255,255,255,0.3);
	overflow: hidden;
	transition: box-shadow 0.3s ease;
}
.partner-item:hover .partner-inner {
	box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
.partner-item:hover .partner-inner {
	transform: rotateY(180deg);
	-webkit-transform: rotateY(180deg);
	-moz-transform: rotateY(180deg);
}
.partner-item.flipped .partner-inner {
	transform: rotateY(180deg);
	-webkit-transform: rotateY(180deg);
	-moz-transform: rotateY(180deg);
}
.partner-front, .partner-back {
	position: absolute;
	width: 100%;
	height: 100%;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 20px;
	box-sizing: border-box;
}
.partner-front {
	background-color: white;
	border-radius: 15px;
	z-index: 2;
	display: flex;
	justify-content: center;
	align-items: center;
}
.partner-back {
	background: linear-gradient(135deg, #2b64a7 0%, #164785 100%);
	color: white;
	transform: rotateY(180deg);
	-webkit-transform: rotateY(180deg);
	-moz-transform: rotateY(180deg);
	border-radius: 15px;
	z-index: 1;
}
.partner-logo {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}
.partner-logo img {
	max-width: 90%;
	max-height: 90%;
	object-fit: contain;
	transition: opacity 0.3s ease, transform 0.3s ease;
	transform: translateZ(0); /* 避免3D闪烁 */
}
.partner-item:hover .partner-logo img {
	transform: translateZ(40px);
}
.partner-title {
	margin-top: 15px;
	font-size: 14px;
	color: #333;
	text-align: center;
	font-weight: bold;
}
.partner-desc {
	font-size: 13px;
	line-height: 1.4;
	text-align: center;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
	.partners-grid {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media screen and (max-width: 768px) {
	.partners-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 20px;
	}
	.partner-item {
		height: 140px;
	}
	.partner-desc {
		font-size: 12px;
	}
}

@media screen and (max-width: 480px) {
	.partners-grid {
		grid-template-columns: repeat(1, 1fr);
	}
	.partner-item {
		height: 130px;
		max-width: 250px;
		margin-left: auto;
		margin-right: auto;
	}
}

/* 卡片加载状态 */
.partner-logo img {
	opacity: 0;
	transition: opacity 0.5s ease;
}
.partner-logo img.loaded {
	opacity: 1;
}
.partner-item.image-loaded .partner-logo img {
	opacity: 1;
}
.partner-item.image-error .partner-logo {
	background: #f8f8f8;
}

/* 卡片动画 */
.partner-item {
	opacity: 0;
	transform: translateY(30px);
	transition: opacity 0.8s ease, transform 0.8s ease;
}
.partner-item.animated {
	opacity: 1;
	transform: translateY(0);
}

/* 加入3D光效 */
.partner-inner:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0) 100%);
	background-position: 50% 50%;
	background-size: 300% 300%;
	z-index: 3;
	transition: opacity 0.3s ease;
	opacity: 0;
}
.partner-item:hover .partner-inner:before {
	opacity: 1;
}

/* 当不支持 3D 变换时的降级处理 */
.no-3d-support .partner-item {
	perspective: none;
}
.no-3d-support .partner-inner {
	transform-style: flat;
}
.no-3d-support .partner-back {
	opacity: 0;
	transform: none;
}
.no-3d-support .partner-item:hover .partner-inner {
	transform: none;
}
.no-3d-support .partner-item:hover .partner-back {
	opacity: 1;
}