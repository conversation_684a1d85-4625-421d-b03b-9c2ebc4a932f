/* 生态体系页面样式 */

/* 通用样式 */
.eco-intro-section,
.eco-philosophy-section,
.eco-pillars-section,
.eco-model-section,
.eco-value-section,
.eco-join-section {
  max-width: 1200px;
  margin: 0 auto 60px;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.section-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #2b64a7, rgba(255, 128, 0, 1));
  margin: 0 auto;
}

/* 生态介绍部分 */
.eco-intro-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 40px;
  font-size: 18px;
  line-height: 1.8;
  color: #555;
}

/* 核心理念部分 */
.philosophy-card {
  background: rgba(43, 100, 167, 0.03);
  border-radius: 15px;
  overflow: hidden;
  padding: 40px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.philosophy-content {
  display: flex;
  align-items: center;
}

.philosophy-icon {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #2b64a7, rgba(43, 100, 167, 0.7));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30px;
}

.philosophy-icon svg {
  width: 40px;
  height: 40px;
  fill: white;
}

.philosophy-text h4 {
  font-size: 24px;
  color: #2b64a7;
  margin-bottom: 15px;
  font-weight: 600;
}

.philosophy-text p {
  font-size: 16px;
  line-height: 1.8;
  color: #555;
}

/* 生态三大支柱 */
.pillars-container {
  display: flex;
  justify-content: space-between;
  gap: 30px;
}

.pillar-item {
  flex: 1;
  background: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pillar-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.1);
}

.pillar-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #2b64a7, rgba(255, 128, 0, 1));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.pillar-item:hover:before {
  transform: scaleX(1);
}

.pillar-icon {
  width: 70px;
  height: 70px;
  background: rgba(43, 100, 167, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.pillar-item:hover .pillar-icon {
  background: rgba(43, 100, 167, 0.2);
}

.pillar-icon svg {
  width: 35px;
  height: 35px;
  fill: #2b64a7;
  transition: all 0.3s ease;
}

.pillar-item:hover .pillar-icon svg {
  transform: rotate(10deg) scale(1.1);
}

.pillar-item h4 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.pillar-item p {
  font-size: 15px;
  line-height: 1.6;
  color: #666;
}

/* 生态运行模式 */
.model-diagram {
  background: white;
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.model-container {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  margin-bottom: 30px;
}

.model-box {
  width: 30%;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
}

.model-box-left,
.model-box-right {
  background: rgba(43, 100, 167, 0.05);
}

.model-box-center {
  background: linear-gradient(135deg, #2b64a7, #1e4675);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px 20px;
  position: relative;
  overflow: hidden;
}

.model-box-center:before {
  content: '';
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: rgba(255, 128, 0, 0.3);
  border-radius: 50%;
  z-index: 0;
}

.model-icon {
  width: 70px;
  height: 70px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  position: relative;
  z-index: 1;
}

.model-icon svg {
  width: 40px;
  height: 40px;
  fill: white;
}

.model-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
}

.model-sub {
  font-size: 14px;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.model-list {
  list-style: none;
  padding: 0;
  text-align: left;
}

.model-list li {
  font-size: 15px;
  color: #555;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.dot {
  width: 8px;
  height: 8px;
  background: #2b64a7;
  border-radius: 50%;
  display: inline-block;
  margin-right: 10px;
}

.flow-arrows {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.arrow-left,
.arrow-right {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #2b64a7;
  font-weight: 600;
}

.arrow-left svg,
.arrow-right svg {
  width: 30px;
  height: 30px;
  fill: #2b64a7;
  margin: 0 10px;
}

.arrow-text {
  min-width: 80px;
}

/* 生态价值 */
.value-container {
  display: flex;
  justify-content: space-between;
  gap: 30px;
}

.value-card {
  flex: 1;
  background: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.value-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.1);
}

.value-icon {
  width: 70px;
  height: 70px;
  background: rgba(43, 100, 167, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.value-card:hover .value-icon {
  background: rgba(43, 100, 167, 0.2);
}

.value-icon svg {
  width: 35px;
  height: 35px;
  fill: #2b64a7;
  transition: all 0.3s ease;
}

.value-card:hover .value-icon svg {
  transform: rotate(10deg) scale(1.1);
}

.value-card h4 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.value-card p {
  font-size: 15px;
  line-height: 1.6;
  color: #666;
}

/* 加入生态 */
.join-text {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 40px;
  font-size: 16px;
  line-height: 1.8;
  color: #555;
}

.join-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  margin-bottom: 40px;
}

.join-item {
  width: calc(25% - 30px);
  min-width: 200px;
  background: white;
  border-radius: 10px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.join-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.1);
}

.join-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(255, 128, 0, 1);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.join-item:hover:before {
  transform: scaleX(1);
}

.join-icon {
  width: 60px;
  height: 60px;
  background: rgba(43, 100, 167, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  transition: all 0.3s ease;
}

.join-item:hover .join-icon {
  background: rgba(43, 100, 167, 0.2);
}

.join-icon svg {
  width: 30px;
  height: 30px;
  fill: #2b64a7;
  transition: all 0.3s ease;
}

.join-item:hover .join-icon svg {
  transform: rotate(10deg) scale(1.1);
}

.join-item h4 {
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.join-item p {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.join-button-container {
  text-align: center;
}

.join-button {
  display: inline-block;
  padding: 12px 30px;
  background: #2b64a7;
  color: #fff!important;
  font-size: 16px;
  font-weight: 600;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 5px 15px rgba(43, 100, 167, 0.3);
}

.join-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(43, 100, 167, 0.4);
  background: rgba(255, 128, 0, 1);
  color: white;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .philosophy-content {
    flex-direction: column;
    text-align: center;
  }
  
  .philosophy-icon {
    margin: 0 auto 20px;
  }
  
  .pillars-container,
  .value-container {
    flex-direction: column;
  }
  
  .pillar-item,
  .value-card {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .model-container {
    flex-direction: column;
  }
  
  .model-box {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .flow-arrows {
    flex-direction: column;
    gap: 20px;
  }
  
  .join-item {
    width: calc(50% - 30px);
  }
}

@media (max-width: 768px) {
  .philosophy-card {
    padding: 30px 20px;
  }
  
  .model-diagram {
    padding: 30px 20px;
  }
  
  .join-item {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 24px;
  }
  
  .eco-intro-content {
    font-size: 16px;
  }
  
  .philosophy-text h4 {
    font-size: 20px;
  }
  
  .philosophy-text p {
    font-size: 15px;
  }
  
  .philosophy-icon {
    width: 70px;
    height: 70px;
  }
  
  .philosophy-icon svg {
    width: 35px;
    height: 35px;
  }
  
  .arrow-text {
    min-width: 60px;
    font-size: 14px;
  }
}

/* AI科技特效 */
.pillar-item,
.value-card,
.join-item {
  position: relative;
  overflow: hidden;
}

.pillar-item:after,
.value-card:after,
.join-item:after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(30deg);
  animation: shining 3s infinite linear;
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pillar-item:hover:after,
.value-card:hover:after,
.join-item:hover:after {
  opacity: 1;
}

@keyframes shining {
  0% {
    transform: rotate(30deg) translate(-100%, -100%);
  }
  100% {
    transform: rotate(30deg) translate(100%, 100%);
  }
}

.model-box-center {
  position: relative;
  overflow: hidden;
}

.model-box-center:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="none" /><path d="M0 50 L50 0 L100 50 L50 100 Z" fill="rgba(255,255,255,0.03)" /></svg>');
  background-size: 20px 20px;
  opacity: 0.5;
  z-index: 0;
}

.model-box-center .model-icon,
.model-box-center .model-title,
.model-box-center .model-sub {
  position: relative;
  z-index: 1;
}

.join-button {
  position: relative;
  overflow: hidden;
}

.join-button:after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(30deg);
  animation: btnShining 2s infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes btnShining {
  0% {
    transform: rotate(30deg) translate(-100%, -100%);
  }
  100% {
    transform: rotate(30deg) translate(100%, 100%);
  }
}