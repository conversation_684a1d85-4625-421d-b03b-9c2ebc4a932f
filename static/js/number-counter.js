/**
 * 数字滚动动画 - 适用于 stat-number 元素
 * 使用方法：
 * 1. 将数字放在有 stat-number 类的元素中
 * 2. 如需百分比或加号后缀，添加 percent 或 plus 类
 */
document.addEventListener('DOMContentLoaded', function() {
    // 等待页面完全加载后再开始动画
    setTimeout(initStatNumberAnimation, 500);
    
    // 为视口滚动添加数字动画触发
    window.addEventListener('scroll', function() {
        const techStats = document.querySelectorAll('.stat-number:not(.animating)');
        if (techStats.length) {
            techStats.forEach(statNumber => {
                if (isElementInViewport(statNumber)) {
                    animateStatNumber(statNumber);
                }
            });
        }
    });
});

// 初始化所有可见的数字动画
function initStatNumberAnimation() {
    const techStats = document.querySelectorAll('.stat-number');
    if (!techStats.length) return;
    
    techStats.forEach(statNumber => {
        if (isElementInViewport(statNumber)) {
            animateStatNumber(statNumber);
        }
    });
}

// 检查元素是否在视口中
function isElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// 对单个数字元素执行动画
function animateStatNumber(statNumber) {
    // 提取数字值
    let value = statNumber.textContent.trim();
    let isPercent = value.includes('%');
    let isPlus = value.includes('+');
    
    // 去除特殊字符计算纯数字
    value = value.replace(/[^0-9.]/g, '');
    value = parseFloat(value);
    
    if (isNaN(value)) return;
    
    // 设置 CSS 变量，用于计数器
    statNumber.style.setProperty('--num', value);
    
    // 添加特殊类名
    statNumber.classList.add('animating');
    if (isPercent) statNumber.classList.add('percent');
    if (isPlus) statNumber.classList.add('plus');
}

// 手动为所有数字添加动画的公共方法 (可以从其他脚本调用)
function animateAllStatNumbers() {
    const allStats = document.querySelectorAll('.stat-number');
    allStats.forEach(animateStatNumber);
}