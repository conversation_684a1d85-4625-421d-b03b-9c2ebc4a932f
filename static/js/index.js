// 首页轮播初始化
// pcbanner
var my = new Swiper('.ind-banner .swiper-container', {				
    speed: 1500,
    //loop: true,
    //effect: 'fade',
    //autoplay: {
    //    delay: 4000, //3秒切换一次
    //    disableOnInteraction: false,
    //},
    //autoplayDisableOnInteraction: true,
    pagination: {
        el: '.ind-banner .swiper-pagination',
        clickable: true,
    },
    preventClicks : false,
    preventClicksPropagation: true,
    paginationClickable: true,
    navigation: {
        nextEl: '.ind-banner .swiper-button-next',
        prevEl: '.ind-banner .swiper-button-prev',
    }
});

// 解决方案轮播
var indexCase = new Swiper('.index-products-swiper-container1', {
    loop : true,
    noSwipingClass : 'stop-swiping',
});

// 解决方案图片轮播
var indexCaseimg = new Swiper('.index-products-swiper-container2', {
    speed:700,
    loop : true,
    autoplay: {
        delay: 5000,
        stopOnLastSlide: false,
        disableOnInteraction: false,
    },
    //noSwipingClass : 'stop-swiping',
    controller: {
        control: indexCase,
    },
    pagination:{
        el: '.swiper-pagination',
        bulletClass : 'my-bullet',
        bulletActiveClass: 'my-bullet-active',
        clickable :true,
    },
});

// 产品轮播
var indexPro = new Swiper('.indexPro .Probox',{
    slidesPerView:5,
    spaceBetween:0,
    paginationClickable :true,
    preventClicks : false,//默认true
    speed:2000,
    loop :true,
    autoplay: {
        delay: 8000, //3秒切换一次
        disableOnInteraction: false,
    },
    autoplayDisableOnInteraction: true,
    navigation: {
        nextEl: '.indexPro .swiper-button-next',
        prevEl: '.indexPro .swiper-button-prev',
    },
    breakpoints: { 
        1024: {
            slidesPerView:5,
            spaceBetween:0
        },
        992: {
            slidesPerView:3,
        },
        768: {
            slidesPerView:2,
        }
    }
});

// 新闻中心轮播
var newsSwiper = new Swiper('.newsSwiper', {
    effect: 'fade',
    fadeEffect: {
        crossFade: true
    },
    loop: true,
    autoplay: {
        delay: 5000,
        disableOnInteraction: false,
    },
    pagination: {
        el: '.news-pagination',
        clickable: true,
    },
    on: {
        slideChange: function() {
            // 当轮播切换时，可以在这里添加额外的效果
        }
    }
});

// 案例展示轮播
var galleryThumbs = new Swiper('.indexCaselist .item', {
    paginationClickable :true,
    preventClicks : false,//默认true
    spaceBetween: 0,
    slidesPerView: 5,
    speed:1000,
    freeMode: true,
    loop :false,
    watchSlidesProgress: true,
    navigation: {
        nextEl: '.solution-next',
        prevEl: '.solution-prev',
    },
    breakpoints: { 
        1024: {
            slidesPerView:3,	
            freeMode: false,
            watchSlidesProgress: false,
            loop :true,
        },
        992: {
            slidesPerView:2,
            freeMode: false,
            watchSlidesProgress: false,
            loop :true,
        },
        768: {
            slidesPerView:1,
            freeMode: false,
            watchSlidesProgress: false,
            loop :true,
        }
    }
});

// 案例轮播交互效果
$(document).ready(function() {
    // 初始化第一个案例为激活状态
    $(".indexCaselist .item .li:first").addClass("swiper-slide-thumb-active");
    
    // 鼠标悬停切换案例
    $(".indexCaselist .item .li").hover(function(){
        $(".indexCaselist .item").find(".li").removeClass("swiper-slide-thumb-active");
        $(this).addClass("swiper-slide-thumb-active");
    });

    // 鼠标悬停更新背景图片
    $('.indexCaselist .item .li').mouseover(function(){
        if($(this).find('.img').attr('src')){
            $(".itemBg").css({ 'background-image':'url(' + $(this).find('.img').attr('src') +')' }, 300);
        }
    });

    // 优势列表鼠标悬停效果
    $(".indexYoushilist ul li").mouseover(function(){
        $(".indexYoushilist ul").find("li").removeClass("on");
        $(this).addClass("on");
    });
});

// 添加合作伙伴图片加载和3D效果
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化合作伙伴区域效果');
    
    // 获取所有合作伙伴项
    const partnerItems = document.querySelectorAll('.partner-item');
    if (!partnerItems.length) {
        console.log('未找到合作伙伴元素');
        return;
    }
    
    console.log(`找到 ${partnerItems.length} 个合作伙伴卡片`);
    
    // 检查是否支持3D变换
    function supports3DTransforms() {
        var el = document.createElement('p'),
            has3d,
            transforms = {
                'webkitTransform': '-webkit-transform',
                'OTransform': '-o-transform',
                'msTransform': '-ms-transform',
                'MozTransform': '-moz-transform',
                'transform': 'transform'
            };
        
        document.body.appendChild(el);
        
        for (var t in transforms) {
            if (el.style[t] !== undefined) {
                el.style[t] = 'translate3d(1px,1px,1px)';
                has3d = window.getComputedStyle(el).getPropertyValue(transforms[t]);
            }
        }
        
        document.body.removeChild(el);
        return (has3d !== undefined && has3d.length > 0 && has3d !== "none");
    }
    
    // 检测3D支持
    const supports3D = supports3DTransforms();
    if (!supports3D) {
        console.warn('浏览器不完全支持3D变换，降级处理');
        document.querySelector('.partners-grid').classList.add('fallback-mode');
        
        // 添加降级CSS
        const fallbackCSS = document.createElement('style');
        fallbackCSS.textContent = `
            .fallback-mode .partner-item {
              perspective: none;
            }
            .fallback-mode .partner-inner {
              transition: opacity 0.5s ease;
            }
            .fallback-mode .partner-item:hover .partner-inner {
              transform: none;
            }
            .fallback-mode .partner-back {
              opacity: 0;
              transform: none;
              transition: opacity 0.5s ease;
            }
            .fallback-mode .partner-item:hover .partner-back {
              opacity: 1;
            }
            .fallback-mode .partner-item:hover .partner-front {
              opacity: 0;
            }
        `;
        document.head.appendChild(fallbackCSS);
        document.documentElement.classList.add('no-3d-support');
        return;
    }
    
    const partnerImages = document.querySelectorAll('.partner-logo img');
    
    // 图片加载处理
    partnerImages.forEach(img => {
        if (img.complete) {
            console.log('图片已加载:', img.src);
            img.closest('.partner-item').classList.add('image-loaded');
        } else {
            img.addEventListener('load', function() {
                console.log('合作伙伴图片加载成功:', this.src);
                this.closest('.partner-item').classList.add('image-loaded');
                this.classList.add('loaded');
            });
          
            img.addEventListener('error', function() {
                console.error('合作伙伴图片加载失败:', this.src);
                // 加载失败时使用默认图片
                this.src = 'static/images/banner-pagebig.jpg';
                this.closest('.partner-item').classList.add('image-error');
            });
        }
    });
    
    // 为每个合作伙伴卡片添加事件监听器
    partnerItems.forEach((item, index) => {
        const partnerInner = item.querySelector('.partner-inner');
        
        // 为每个项目添加动画效果，错开时间
        setTimeout(() => {
            item.classList.add('animated');
        }, index * 150);
        
        // 添加鼠标移动事件以创建3D效果
        item.addEventListener('mousemove', function(e) {
            if (window.innerWidth < 768) return; // 在移动设备上禁用鼠标移动效果
            
            const rect = item.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = e.clientX;
            const mouseY = e.clientY;
            
            // 计算鼠标位置相对于卡片中心的偏移
            const percentX = (mouseX - centerX) / (rect.width / 2);
            const percentY = (mouseY - centerY) / (rect.height / 2);
            
            // 限制倾斜角度
            const tiltX = Math.max(Math.min(percentY * 15, 15), -15);
            const tiltY = Math.max(Math.min(percentX * -15, 15), -15);
            
            // 应用3D变换
            partnerInner.style.transform = `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg) scale3d(1.02, 1.02, 1.02)`;
            partnerInner.style.transition = 'none'; // 移动时取消过渡以获得更平滑的效果
            
            // 添加光影效果
            const glarePosition = `${50 + percentX * 10}% ${50 + percentY * 10}%`;
            partnerInner.style.backgroundPosition = glarePosition;
        });
        
        // 添加鼠标离开事件以重置3D效果
        item.addEventListener('mouseleave', function() {
            partnerInner.style.transform = '';
            partnerInner.style.transition = 'transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            partnerInner.style.backgroundPosition = '50% 50%';
        });
        
        // 添加点击事件以在移动设备上翻转卡片
        item.addEventListener('click', function() {
            if (window.innerWidth < 768) {
                this.classList.toggle('flipped');
                
                // 关闭其他翻转的卡片
                partnerItems.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('flipped')) {
                        otherItem.classList.remove('flipped');
                    }
                });
            }
        });
    });
    
    // 添加窗口大小变化事件以重置效果
    window.addEventListener('resize', function() {
        partnerItems.forEach(item => {
            const partnerInner = item.querySelector('.partner-inner');
            partnerInner.style.transform = '';
            
            // 如果是桌面模式，取消所有翻转状态
            if (window.innerWidth >= 768) {
                item.classList.remove('flipped');
            }
        });
    });
    
    // 添加滚动动画
    if (typeof WOW !== 'undefined') {
        new WOW().init();
        console.log('WOW动画初始化成功');
    } else {
        console.warn('WOW.js未加载，将使用基本动画');
    }
});

// 新闻切换功能
document.addEventListener('DOMContentLoaded', function() {
    const newsItems = document.querySelectorAll('.news-item');
    const newsImage = document.getElementById('newsImage');
    const newsTitle = document.querySelector('.news-content .news-title');
    const newsDate = document.querySelector('.news-content .news-date');
    const newsDesc = document.querySelector('.news-content .news-desc');
    
    newsItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            // 移除所有激活状态
            newsItems.forEach(i => i.classList.remove('active'));
            
            // 添加当前激活状态
            this.classList.add('active');
            
            // 更新图片和内容，添加淡入效果
            newsImage.style.opacity = '0';
            setTimeout(() => {
                newsImage.style.backgroundImage = `url('${this.dataset.image}')`;
                newsTitle.textContent = this.dataset.title;
                newsDate.textContent = this.dataset.date;
                newsDesc.textContent = this.dataset.desc;
                newsImage.style.opacity = '1';
            }, 300);
        });
    });
    
    // 初始化透明度过渡效果
    newsImage.style.transition = 'opacity 0.5s ease';
    newsImage.style.opacity = '1';
});

// 机房枢纽交互效果
document.addEventListener('DOMContentLoaded', function() {
    // 初始化科技感粒子效果
    if (typeof initTechParticles === 'function') {
        initTechParticles();
    }
    
    // 数据中心卡片数字动画 - 修复版
    function initNumberCounters() {
        console.log('初始化数字计数动画');
        
        // 直接选取固定区域的统计数字
        const datacenterStatsContainer = document.querySelector('.datacenter-info .tech-stats');
        if (!datacenterStatsContainer) {
            console.error('未找到数据中心统计容器');
            return;
        }
        
        const datacenterStats = datacenterStatsContainer.querySelectorAll('.stat-number');
        console.log('找到统计数字元素:', datacenterStats.length);
        
        if (datacenterStats.length === 0) {
            console.error('未找到任何数字元素');
            return;
        }
        
        // 先保存原始值，以便后续动画使用
        datacenterStats.forEach(el => {
            if (!el.hasAttribute('data-target')) {
                let value = el.textContent.trim();
                // 判断是否包含特殊符号
                let isPercent = value.includes('%');
                let isPlus = value.includes('+');
                
                // 提取纯数字
                value = value.replace(/[^0-9.]/g, '');
                let numValue = parseInt(value) || 0; // 防止NaN
                
                // 存储目标值和格式
                el.setAttribute('data-target', numValue);
                if (isPercent) el.setAttribute('data-format', 'percent');
                if (isPlus) el.setAttribute('data-format', 'plus');
                
                // 初始化为0
                if (numValue > 0) {
                    if (isPercent) el.textContent = '0%';
                    else if (isPlus) el.textContent = '0+';
                    else el.textContent = '0';
                }
            }
        });
        
        // 开始新的动画
        datacenterStats.forEach(el => {
            const targetValue = parseInt(el.getAttribute('data-target')) || 0;
            const format = el.getAttribute('data-format') || '';
            const isPercent = format === 'percent';
            const isPlus = format === 'plus';
            
            // 清除可能正在进行的动画
            if (el._animationTimer) {
                clearTimeout(el._animationTimer);
                delete el._animationTimer;
            }
            
            // 激活新的动画
            animateNumberFixed(el, targetValue, isPercent, isPlus);
        });
    }
    
    // 完全重写的数字动画函数 - 使用setTimeout替代requestAnimationFrame避免可能的冲突
    function animateNumberFixed(element, targetValue, isPercent, isPlus) {
        if (!element || targetValue === undefined) return;
        
        // 确保目标值是数字
        targetValue = parseInt(targetValue) || 0;
        
        // 格式化函数
        function formatValue(value) {
            let roundedValue = Math.round(value);
            if (isPercent) {
                return roundedValue + '%';
            } else if (isPlus) {
                return roundedValue + '+';
            }
            return roundedValue.toString();
        }
        
        // 当前值
        let currentValue = 0;
        // 当currentValue到达0.95倍目标值时快速结束
        const threshold = targetValue * 0.95;
        // 每次增加的量，使得动画总时间约为2秒
        const frameTime = 30; // 毫秒
        
        // 计算每次递增的值 (使用指数减速函数)
        const totalSteps = Math.min(30, targetValue); // 最多30步
        let increment = Math.max(1, Math.ceil(targetValue / totalSteps));
        
        // 防止空白内容
        element.textContent = formatValue(0);
        
        function step() {
            // 增加当前值
            currentValue += increment;
            
            // 加速完成动画
            if (currentValue >= threshold) {
                element.textContent = formatValue(targetValue);
                return;
            }
            
            // 防止超过目标值
            if (currentValue > targetValue) {
                currentValue = targetValue;
            }
            
            // 显示当前值
            element.textContent = formatValue(currentValue);
            
            // 继续动画
            if (currentValue < targetValue) {
                element._animationTimer = setTimeout(step, frameTime);
            }
        }
        
        // 开始递增动画
        element._animationTimer = setTimeout(step, frameTime);
    }
    
    // 确保页面加载后立即执行数字动画
    // 立即执行一次
    initNumberCounters();
    
    // 每次都连续尝试几次，确保在所有情况下都能正常运行
    for (let i = 1; i <= 5; i++) {
        setTimeout(initNumberCounters, i * 500); // 500ms, 1000ms, 1500ms, 2000ms, 2500ms
    }
    
    // 监听滚动事件，当滚动到数据中心区域时触发动画
    window.addEventListener('scroll', function() {
        const datacenterInfo = document.querySelector('.datacenter-info');
        if (datacenterInfo && isElementInViewport(datacenterInfo)) {
            initNumberCounters();
        }
    });
    
    // 监听窗口进入可见状态
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            setTimeout(initNumberCounters, 100); // 当页面变为可见状态时执行
        }
    });
    
    // 检查元素是否在视口中
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
});