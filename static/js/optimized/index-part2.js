/**
 * 初始化新闻切换功能
 */
function initNewsInteraction() {
  const newsItems = document.querySelectorAll('.news-item');
  const newsImage = document.getElementById('newsImage');
  const newsTitle = document.querySelector('.news-content .news-title');
  const newsDate = document.querySelector('.news-content .news-date');
  const newsDesc = document.querySelector('.news-content .news-desc');
  
  if (!newsImage || !newsItems.length) return;
  
  newsItems.forEach(item => {
    item.addEventListener('mouseenter', function() {
      // 移除所有激活状态
      newsItems.forEach(i => i.classList.remove('active'));
      
      // 添加当前激活状态
      this.classList.add('active');
      
      // 更新图片和内容，添加淡入效果
      newsImage.style.opacity = '0';
      setTimeout(() => {
        newsImage.style.backgroundImage = `url('${this.dataset.image}')`;
        newsTitle.textContent = this.dataset.title;
        newsDate.textContent = this.dataset.date;
        newsDesc.textContent = this.dataset.desc;
        newsImage.style.opacity = '1';
      }, 300);
    });
  });
  
  // 初始化透明度过渡效果
  newsImage.style.transition = 'opacity 0.5s ease';
  newsImage.style.opacity = '1';
}

/**
 * 初始化机房枢纽交互效果
 */
function initDataCenterMap() {
  // 初始化科技感粒子效果
  initTechParticles();
  
  // 数据中心卡片数字动画
  initNumberCounters();
  
  // 数据中心信息配置
  const dataCenters = [
    {
      id: 'beijing',
      name: '京津冀枢纽',
      desc: '京津冀枢纽：张家口集群高性能计算中心，提供AI训练和推理服务'
    },
    {
      id: 'shanghai',
      name: '长三角枢纽',
      desc: '长三角枢纽：芜湖集群、长三角生态绿色一体化发展示范集团'
    },
    {
      id: 'guangzhou',
      name: '粤港澳枢纽',
      desc: '粤港澳枢纽：韶关集群，提供全面云服务和边缘计算能力'
    },
    {
      id: 'chengdu',
      name: '贵州枢纽',
      desc: '贵州枢纽：集安集群，算力中心，支持大规模并行计算'
    },
    {
      id: 'xian',
      name: '内蒙古枢纽',
      desc: '内蒙古枢纽：和林格尔集群核心节点，提供高可靠性灾备服务'
    },
    {
      id: 'wuhan',
      name: '宁夏枢纽',
      desc: '宁夏枢纽：中卫集群，专注科研计算和数据分析'
    },
    {
      id: 'shenyang',
      name: '成渝枢纽',
      desc: '成渝枢纽：重庆集群，工业互联网和智能制造支持'
    },
    {
      id: 'wulumuqi',
      name: '甘肃枢纽',
      desc: '甘肃枢纽：天府集群处理中心，服务一带一路战略'
    }
  ];
  
  // 等待中国地图图片完全加载
  const chinaMapImg = document.getElementById('chinaMapImage');
  const chinaMapContainer = document.getElementById('chinaMapContainer');
  
  if (!chinaMapImg || !chinaMapContainer) {
    console.error('未找到中国地图或容器元素');
    return;
  }
  
  // 橙色点的精确坐标，基于图片尺寸的比例
  const pointCoordinates = [
    { x: 64.8, y: 45.5 }, // 北京
    { x: 71.0, y: 59.5 }, // 上海
    { x: 61.0, y: 71 },   // 广州
    { x: 52.5, y: 66.0 }, // 成都
    { x: 55.0, y: 44.5 }, // 西安
    { x: 51.5, y: 50.0 }, // 武汉
    { x: 51.0, y: 59.0 }, // 沈阳
    { x: 49.0, y: 53.5 }  // 乌鲁木齐
  ];
  
  // 创建地图点位
  function createMapPoints() {
    // 清空现有点
    const existingPoints = chinaMapContainer.querySelectorAll('.map-point');
    existingPoints.forEach(point => point.remove());
    
    // 为每个数据中心创建点
    dataCenters.forEach((center, index) => {
      if (index < pointCoordinates.length) {
        const point = document.createElement('div');
        point.className = 'map-point';
        point.setAttribute('data-location', center.name);
        point.setAttribute('data-desc', center.desc);
        
        // 根据id添加特定的类名，以控制信息框弹出的位置
        if (center.id === 'beijing' || center.id === 'guangzhou') {
          point.classList.add('info-right');
        } else if (center.id === 'chengdu') {
          point.classList.add('info-left');
        } else {
          // 其他数据中心根据索引决定位置
          if (index % 2 === 0) {
            point.classList.add('info-right');
          } else {
            point.classList.add('info-left');
          }
        }
        
        // 设置点的位置
        point.style.top = pointCoordinates[index].y + '%';
        point.style.left = pointCoordinates[index].x + '%';
        
        // 创建点的内部结构
        point.innerHTML = `
          <div class="point-dot"></div>
          <div class="point-pulse pulse1"></div>
          <div class="point-pulse pulse2"></div>
          <div class="point-info">
            <h4>${center.name}数据中心</h4>
            <p>${center.desc.split('：')[1]}</p>
            <div class="stats">
              <div class="stat-item">
                <div class="stat-number" data-value="${95 + Math.floor(Math.random() * 5)}">0</div>
                <div class="stat-label">可用率</div>
              </div>
              <div class="stat-item">
                <div class="stat-number" data-value="${Math.floor(50 + Math.random() * 50)}">0</div>
                <div class="stat-label">节点数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number plus" data-value="${Math.floor(10 + Math.random() * 90)}">0</div>
                <div class="stat-label">服务器</div>
              </div>
            </div>
          </div>
        `;
        
        // 添加点到容器
        chinaMapContainer.appendChild(point);
        
        // 添加地图点位交互事件
        addMapPointInteractions(point);
      }
    });
    
    console.log(`成功创建了 ${dataCenters.length} 个地图点`);
  }
  
  // 添加地图点位交互事件
  function addMapPointInteractions(point) {
    // 鼠标进入事件
    point.addEventListener('mouseenter', function(e) {
      e.stopPropagation();
      
      // 添加info-visible类
      this.classList.add('info-visible');
      setTimeout(() => {
        this.classList.add('info-visible');
      }, 10);
      
      // 隐藏所有其他地图点
      document.querySelectorAll('.map-point').forEach(p => {
        if (p !== this) {
          p.classList.add('other-hidden');
          
          const otherInfo = p.querySelector('.point-info');
          if (otherInfo) {
            otherInfo.style.visibility = 'hidden';
            otherInfo.style.opacity = '0';
            otherInfo.style.transform = 'translateY(10px)';
          }
          p.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1)';
          p.classList.remove('info-visible');
        }
      });
      
      // 美化当前点
      this.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1.2)';
      const info = this.querySelector('.point-info');
      if (info) {
        info.style.visibility = 'visible';
        info.style.opacity = '1';
        info.style.transform = 'translateY(0)';
        
        // 启动数字计数动画
        const statNumbers = info.querySelectorAll('.stat-number');
        statNumbers.forEach(el => {
          const value = parseInt(el.getAttribute('data-value'));
          el.style.setProperty('--target-num', value);
          el.classList.add('counting');
          
          if (el.closest('.stat-item').querySelector('.stat-label').textContent.includes('率')) {
            el.classList.add('percent');
          }
        });
      }
    });
    
    // 鼠标离开事件
    point.addEventListener('mouseleave', function() {
      this.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1)';
      const info = this.querySelector('.point-info');
      if (info) {
        info.style.visibility = 'hidden';
        info.style.opacity = '0';
        info.style.transform = 'translateY(10px)';
        this.classList.remove('info-visible');
        
        // 重置数字计数状态
        const statNumbers = info.querySelectorAll('.stat-number');
        statNumbers.forEach(el => {
          el.classList.remove('counting');
          setTimeout(() => {
            el.textContent = '0';
          }, 500);
        });
      }
      
      // 恢复所有其他点的显示
      document.querySelectorAll('.map-point.other-hidden').forEach(p => {
        p.classList.remove('other-hidden');
      });
    });
    
    // 触摸事件支持
    point.addEventListener('touchstart', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      // 检查是否已经处于激活状态
      const isActive = this.classList.contains('info-visible');
      
      if (isActive) {
        // 恢复所有点
        document.querySelectorAll('.map-point').forEach(p => {
          resetMapPoint(p);
        });
      } else {
        // 激活当前点
        this.classList.add('info-visible');
        setTimeout(() => {
          this.classList.add('info-visible');
          if (this.querySelector('::after')) {
            try {
              this.style.setProperty('--after-visibility', 'hidden');
              this.style.setProperty('--after-opacity', '0');
            } catch(e) {}
          }
        }, 10);
        
        // 隐藏其他所有点
        document.querySelectorAll('.map-point').forEach(p => {
          if (p !== this) {
            p.classList.add('other-hidden');
            resetMapPoint(p);
          }
        });
        
        // 美化当前点
        activateMapPoint(this);
      }
    });
  }
  
  // 激活地图点
  function activateMapPoint(point) {
    point.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1.2)';
    const info = point.querySelector('.point-info');
    if (info) {
      info.style.visibility = 'visible';
      info.style.opacity = '1';
      info.style.transform = 'translateY(0)';
      
      // 启动数字计数动画
      const statNumbers = info.querySelectorAll('.stat-number');
      statNumbers.forEach(el => {
        const value = parseInt(el.getAttribute('data-value'));
        el.style.setProperty('--target-num', value);
        el.classList.add('counting');
        
        if (el.closest('.stat-item').querySelector('.stat-label').textContent.includes('率')) {
          el.classList.add('percent');
        }
      });
    }
  }
  
  // 重置地图点
  function resetMapPoint(point) {
    point.classList.remove('other-hidden');
    point.classList.remove('info-visible');
    
    const pointInfo = point.querySelector('.point-info');
    if (pointInfo) {
      pointInfo.style.visibility = 'hidden';
      pointInfo.style.opacity = '0';
      pointInfo.style.transform = 'translateY(10px)';
      
      // 重置数字计数状态
      const statNumbers = pointInfo.querySelectorAll('.stat-number');
      statNumbers.forEach(el => {
        el.classList.remove('counting');
        setTimeout(() => {
          el.textContent = '0';
        }, 500);
      });
    }
    point.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1)';
  }
  
  // 当图片加载完成后创建点
  if (chinaMapImg.complete) {
    console.log('地图已加载，立即创建点');
    createMapPoints();
  } else {
    chinaMapImg.onload = function() {
      console.log('地图加载完成，创建点');
      createMapPoints();
    };
  }
  
  // 添加全局点击事件，点击其他区域时关闭所有信息框
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.map-point')) {
      // 隐藏所有信息框并恢复所有点
      document.querySelectorAll('.map-point').forEach(point => {
        resetMapPoint(point);
      });
    }
  });
  
  // 窗口大小改变时，重新创建点以保持位置准确
  window.addEventListener('resize', function() {
    console.log('窗口大小改变，重新创建点');
    setTimeout(createMapPoints, 100);
  });
}

/**
 * 初始化科技感粒子效果
 */
function initTechParticles() {
  const container = document.getElementById('techParticles');
  if (!container) return;
  
  // 创建20-30个粒子
  const particleCount = 20 + Math.floor(Math.random() * 10);
  
  for (let i = 0; i < particleCount; i++) {
    createParticle(container);
  }
}

/**
 * 创建单个粒子并设置动画
 */
function createParticle(container) {
  const particle = document.createElement('div');
  particle.className = 'particle';
  
  // 随机位置
  const x = Math.random() * 100;
  const y = Math.random() * 100;
  particle.style.left = x + '%';
  particle.style.top = y + '%';
  
  // 随机大小和亮度
  const size = 1 + Math.random() * 2;
  const opacity = 0.3 + Math.random() * 0.7;
  particle.style.width = size + 'px';
  particle.style.height = size + 'px';
  particle.style.opacity = opacity;
  
  // 添加到容器
  container.appendChild(particle);
  
  // 应用动画
  animateParticle(particle);
}

/**
 * 粒子动画
 */
function animateParticle(particle) {
  // 闪烁动画
  const duration = 2 + Math.random() * 3;
  particle.style.animation = `flicker ${duration}s infinite`;
  
  // 添加闪烁动画关键帧
  if (!document.querySelector('#particle-keyframes')) {
    const style = document.createElement('style');
    style.id = 'particle-keyframes';
    style.textContent = `
      @keyframes flicker {
        0% { opacity: 0.3; }
        50% { opacity: 0.8; }
        100% { opacity: 0.3; }
      }
    `;
    document.head.appendChild(style);
  }
  
  // 每隔10-20秒移动到新位置
  setInterval(() => {
    const x = Math.random() * 100;
    const y = Math.random() * 100;
    particle.style.transition = 'left 8s ease, top 8s ease';
    particle.style.left = x + '%';
    particle.style.top = y + '%';
  }, 10000 + Math.random() * 10000);
}

/**
 * 初始化数字计数动画
 */
function initNumberCounters() {
  console.log('初始化数字计数动画');
  
  // 直接选取固定区域的统计数字
  const datacenterStatsContainer = document.querySelector('.datacenter-info .tech-stats');
  if (!datacenterStatsContainer) {
    console.error('未找到数据中心统计容器');
    return;
  }
  
  const datacenterStats = datacenterStatsContainer.querySelectorAll('.stat-number');
  console.log('找到统计数字元素:', datacenterStats.length);
  
  if (datacenterStats.length === 0) {
    console.error('未找到任何数字元素');
    return;
  }
  
  // 先保存原始值，以便后续动画使用
  datacenterStats.forEach(el => {
    if (!el.hasAttribute('data-target')) {
      let value = el.textContent.trim();
      // 判断是否包含特殊符号
      let isPercent = value.includes('%');
      let isPlus = value.includes('+');
      
      // 提取纯数字
      value = value.replace(/[^0-9.]/g, '');
      let numValue = parseInt(value) || 0; // 防止NaN
      
      // 存储目标值和格式
      el.setAttribute('data-target', numValue);
      if (isPercent) el.setAttribute('data-format', 'percent');
      if (isPlus) el.setAttribute('data-format', 'plus');
      
      // 初始化为0
      if (numValue > 0) {
        if (isPercent) el.textContent = '0%';
        else if (isPlus) el.textContent = '0+';
        else el.textContent = '0';
      }
    }
  });
  
  // 开始新的动画
  datacenterStats.forEach(el => {
    const targetValue = parseInt(el.getAttribute('data-target')) || 0;
    const format = el.getAttribute('data-format') || '';
    const isPercent = format === 'percent';
    const isPlus = format === 'plus';
    
    // 清除可能正在进行的动画
    if (el._animationTimer) {
      clearTimeout(el._animationTimer);
      delete el._animationTimer;
    }
    
    // 激活新的动画
    animateNumberFixed(el, targetValue, isPercent, isPlus);
  });
  
  // 多次尝试执行动画，确保在各种情况下都能正常运行
  for (let i = 1; i <= 3; i++) {
    setTimeout(function() {
      datacenterStats.forEach(el => {
        const targetValue = parseInt(el.getAttribute('data-target')) || 0;
        const format = el.getAttribute('data-format') || '';
        const isPercent = format === 'percent';
        const isPlus = format === 'plus';
        
        if (el.textContent === '0' || el.textContent === '0%' || el.textContent === '0+') {
          animateNumberFixed(el, targetValue, isPercent, isPlus);
        }
      });
    }, i * 800);
  }
  
  // 监听滚动事件，当滚动到数据中心区域时触发动画
  window.addEventListener('scroll', function() {
    const datacenterInfo = document.querySelector('.datacenter-info');
    if (datacenterInfo && isElementInViewport(datacenterInfo)) {
      // 仅当元素在视口中且数字未显示时执行动画
      datacenterStats.forEach(el => {
        const targetValue = parseInt(el.getAttribute('data-target')) || 0;
        const format = el.getAttribute('data-format') || '';
        const isPercent = format === 'percent';
        const isPlus = format === 'plus';
        const currentValue = parseInt(el.textContent.replace(/[^0-9]/g, '')) || 0;
        
        if (currentValue < targetValue * 0.9) {
          animateNumberFixed(el, targetValue, isPercent, isPlus);
        }
      });
    }
  });
}

/**
 * 数字动画函数 - 使用setTimeout避免可能的冲突
 */
function animateNumberFixed(element, targetValue, isPercent, isPlus) {
  if (!element || targetValue === undefined) return;
  
  // 确保目标值是数字
  targetValue = parseInt(targetValue) || 0;
  
  // 格式化函数
  function formatValue(value) {
    let roundedValue = Math.round(value);
    if (isPercent) {
      return roundedValue + '%';
    } else if (isPlus) {
      return roundedValue + '+';
    }
    return roundedValue.toString();
  }
  
  // 当前值
  let currentValue = 0;
  // 当currentValue到达0.95倍目标值时快速结束
  const threshold = targetValue * 0.95;
  // 每次增加的量，使得动画总时间约为2秒
  const frameTime = 30; // 毫秒
  
  // 计算每次递增的值
  const totalSteps = Math.min(30, targetValue); // 最多30步
  let increment = Math.max(1, Math.ceil(targetValue / totalSteps));
  
  // 防止空白内容
  element.textContent = formatValue(0);
  
  function step() {
    // 增加当前值
    currentValue += increment;
    
    // 加速完成动画
    if (currentValue >= threshold) {
      element.textContent = formatValue(targetValue);
      return;
    }
    
    // 防止超过目标值
    if (currentValue > targetValue) {
      currentValue = targetValue;
    }
    
    // 显示当前值
    element.textContent = formatValue(currentValue);
    
    // 继续动画
    if (currentValue < targetValue) {
      element._animationTimer = setTimeout(step, frameTime);
    }
  }
  
  // 开始递增动画
  element._animationTimer = setTimeout(step, frameTime);
}

/**
 * 检查元素是否在视口中
 */
function isElementInViewport(el) {
  const rect = el.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}