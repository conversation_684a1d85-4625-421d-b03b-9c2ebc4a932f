// 等待文档加载完成
$(document).ready(function() {
  // 获取所有地图标记点
  const mapPoints = document.querySelectorAll('.map-point');
  const mapLabels = document.querySelectorAll('.map-label');
  
  // 为每个地图点添加点击事件
  mapPoints.forEach((point, index) => {
    point.addEventListener('click', () => {
      // 移除所有激活状态
      mapPoints.forEach(p => p.classList.remove('active'));
      mapLabels.forEach(l => l.classList.remove('active'));
      
      // 为当前点和标签添加激活状态
      point.classList.add('active');
      mapLabels[index].classList.add('active');
    });
  });
  
  // 为每个标签添加点击事件
  mapLabels.forEach((label, index) => {
    label.addEventListener('click', () => {
      // 移除所有激活状态
      mapPoints.forEach(p => p.classList.remove('active'));
      mapLabels.forEach(l => l.classList.remove('active'));
      
      // 为当前点和标签添加激活状态
      mapPoints[index].classList.add('active');
      label.classList.add('active');
    });
  });
  
  // 随机激活一个点（默认第一个）
  if (mapPoints.length > 0) {
    mapPoints[0].classList.add('active');
    mapLabels[0].classList.add('active');
  }
  
  // 数字动画效果
  const dataNumbers = document.querySelectorAll('.data-number');
  
  // 使用Intersection Observer API检测元素可见性
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // 元素可见时开始动画
        animateNumbers();
        // 只执行一次
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.5 });
  
  // 观察每个数字元素
  if (dataNumbers.length > 0) {
    observer.observe(document.querySelector('.home-about-data'));
  }
  
  // 数字动画函数
  function animateNumbers() {
    dataNumbers.forEach(number => {
      const targetValue = parseInt(number.textContent);
      let startValue = 0;
      const duration = 2000; // 2秒动画
      const startTime = performance.now();
      
      // 获取并保存数字后面的文本内容（如 "年", "+", "%"）
      const suffix = number.querySelector('span') ? number.querySelector('span').outerHTML : '';
      
      // 动画函数
      function updateNumber(currentTime) {
        const elapsedTime = currentTime - startTime;
        const progress = Math.min(elapsedTime / duration, 1);
        
        // 使用缓动函数使动画更自然
        const easedProgress = 1 - Math.pow(1 - progress, 3);
        
        // 计算当前值
        const currentValue = Math.floor(startValue + (targetValue - startValue) * easedProgress);
        
        // 更新DOM
        number.innerHTML = currentValue + suffix;
        
        // 继续动画直到完成
        if (progress < 1) {
          requestAnimationFrame(updateNumber);
        } else {
          number.innerHTML = targetValue + suffix;
        }
      }
      
      // 开始动画
      requestAnimationFrame(updateNumber);
    });
  }
});