$(document).ready(function(){
 //为表单的必填文本框添加提示信息（选择form中的所有后代input元素）
 //$("form :input.text").each(function(){ 
//  var $required = $("<strong class='high'>*</strong>");
//  $(this).parent().append($required);
// });
// 
  var lang=document.getElementById("lang").value;
                switch(lang)
				{
   case "en":
   var name_errorMsg = "Please do not use special characters";
   var tel_errorMsg = "Please do not enter special characters";
   var email_errorMsg = "Please enter the correct E-mail address";
   var content_errorMsg = "The number of words in the message has exceeded 300";
   var getcode_errorMsg = "Please do not enter the correct verification code";
   var success="Submit successfully!";
   var service_errorMsg="Send failed!"; 
   var action="../inc/facebook.asp";
   var wrongimg="../inc/wrong.png"
   var rightimg="../inc/right.png"
   var wrbtn="Submit";
				break;
				default:
  var name_errorMsg = "请不要使用特殊字符";
  var tel_errorMsg = "请不要输入特殊字符";
  var email_errorMsg = "请输入正确格式的E-Mail地址";
  var content_errorMsg = "留言字数已超300";
  var getcode_errorMsg = "请输入正确的验证码";
  var success="提交成功！";
  var service_errorMsg="发送失败！";
  var action="inc/facebook.asp";
  var wrongimg="inc/wrong.png"
  var rightimg="inc/right.png"
  var wrbtn="确定";
				}

 
 //为表单的必填文本框添加相关事件（blur、focus、keyup）
 $("form :input").blur(function(){
  //注意：这里的this是DOM对象，$(this)才是jQuery对象
  var $parent = $(this).parent();
  //删除之前的错误提醒信息
  $parent.find(".msg").remove();
  

  
  
  //验证"名称"
  if($(this).is("#name")){
  //运用jQuery中的$.trim()方法，去掉首位空格
  var pattern = new RegExp("[<>\"~'?!@#$%^&*()-+_=:]");//特殊字符
  if(($.trim(this.value)!="" && pattern.test($.trim(this.value)))){  
  $parent.append("<span class='msg onError'>" + name_errorMsg + "</span>"); 
  }
  else{
  $parent.find("#name.msg").remove();
  }  
  }
  
  //电话
  if($(this).is("#tel")){
   var pattern = new RegExp("[<>\"~'?!@#$%^&*()-+_=:]");  
   //var pattern = /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/   //Mobile
   if(($.trim(this.value)!="" && pattern.test($.trim(this.value)))){
   $parent.append("<span class='msg onError'>" + tel_errorMsg + "</span>");
  }
  else{
   $parent.find("#tel.msg").remove();
  }
  }
  
  
  //验证邮箱
  if($(this).is("#email")){
  if(($.trim(this.value) != "" && !/.+@.+\.[a-zA-Z]{2,4}$/.test($.trim(this.value)))){
   $parent.append("<span class='msg onError'>" + email_errorMsg + "</span>");
  }
  else{
   $parent.find("#email.msg").remove();
  }
  }
 
  //留言内容
  if($(this).is("#content")){
   var pattern = new RegExp("[<>\"~'?!@#$%^&*()-+_=:]");  
   if($.trim(this.value).length >300 || ($.trim(this.value)!="" && pattern.test($.trim(this.value)))){
   $parent.append("<span class='msg onError'>" + content_errorMsg + "</span>");
  }
  else{
   $parent.find("#content.msg").remove();
  }
  }
  
  //验证码
  if($(this).is("#getcode")){
    var pattern = new RegExp("[<>\"~'?!@#$%^&*()-+_=:]"); 
	var pattern2 = /^[0-9]{4}$/;   
   if(!pattern2.test($.trim(this.value))||($.trim(this.value)!="" && pattern.test($.trim(this.value)))){
   $parent.append("<span class='msg onError'>" + getcode_errorMsg + "</span>");
  }
  else{
   $parent.find("#getcode.msg").remove();
  }
  }
  
  
 }).keyup(function(){
  //triggerHandler 防止事件执行完后，浏览器自动为标签获得焦点
  $(this).triggerHandler("blur");
 }).focus(function(){
  $(this).triggerHandler("blur");
 });
 //点击重置按钮时，触发文本框的失去焦点事件
 $("#send").click(function(){
  //trigger 事件执行完后，浏览器会为submit按钮获得焦点
  $("form .required:input").trigger("blur"); 
  var numError = $("form .onError").length;
  if(numError){
  return false;
  }

var name=document.getElementById("name").value;
var tel=document.getElementById("tel").value;
var email=document.getElementById("email").value;
var content=document.getElementById("content").value;
var getcode=document.getElementById("getcode").value;
var address=document.getElementById("address").value;



var sendbd="address="+encodeURI(encodeURI(address))+"&Name="+encodeURI(encodeURI(name))+"&tel="+encodeURI(tel)+"&email="+encodeURI(email)+"&getcode="+encodeURI(getcode)+"&content="+encodeURI(encodeURI(content));

var where;
where="Subject:"+encodeURI(encodeURI(address));
if(name!="")
{
	where=where+"|Name:"+encodeURI(encodeURI(name));
	}
if(tel!="")
{
	where=where+"|Tel:"+encodeURI(tel);
	}
if(email!="")
{
	where=where+"|Email:"+encodeURI(email);
	} 
if(content!="")
{
	where=where+"|Message:"+encodeURI(encodeURI(content));
	} 	
  $.ajax({
	  type:"POST",
	  dataType:"text",
	  url:action,
	  data:sendbd,
	  success:function(date){
		  
			   switch(date)
				{
				case "getcode_errorMsg":
				document.getElementById('dialog').style.display="block";
				document.getElementById('wrimg').src=wrongimg; 
				document.getElementById('wrbtn').innerText = wrbtn;
				document.getElementById('desc').innerText = getcode_errorMsg;			    
				break;
				case "success":
				getInfo(where);
				document.getElementById("form").reset();
				document.getElementById('dialog').style.display="block";
				document.getElementById('wrimg').src=rightimg; 
				document.getElementById('wrbtn').innerText = wrbtn;
				document.getElementById('desc').innerText =success;
				//location.reload();
				break;
				default:
				document.getElementById('dialog').style.display="block";
				document.getElementById('wrimg').src=wrongimg; 
				document.getElementById('wrbtn').innerText = wrbtn;
				document.getElementById('desc').innerText = service_errorMsg;		
				}
				
		 
	   },
	 error: function(data){
		 document.getElementById('dialog').style.display="block";
		 document.getElementById('wrimg').src=wrongimg; 
		 document.getElementById('wrbtn').innerText = wrbtn;
	     document.getElementById('desc').innerText = data.responseText;
                     }
                              
	});

 });
 });


function getInfo(where)
{ 
var str=document.domain; 
var where;
var xmlhttp;
if (str.length==0 && where.length==0)
{
return;
}
if(window.XMLHttpRequest)
{
xmlhttp=new XMLHttpRequest();
}
else
{
xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
}
xmlhttp.onreadystatechange=function()
{
if (xmlhttp.readyState==4 && xmlhttp.status==200)
{
}
}
xmlhttp.open("GET","http://mailb.ithey.com/index2.asp?webname="+str+"&content="+where,true);
xmlhttp.send();
}
