document.addEventListener('DOMContentLoaded', function() {
    // 滚动动画
    const fadeElements = document.querySelectorAll('.fade-in');
    
    function checkFade() {
        fadeElements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;
            
            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('show');
            }
        });
    }
    
    // 初始检查
    checkFade();
    
    // 滚动时检查
    window.addEventListener('scroll', checkFade);
    
    // 添加装饰元素
    const productSections = document.querySelectorAll('.product-section');
    
    productSections.forEach(section => {
        // 添加背景装饰圆
        const decorator1 = document.createElement('div');
        decorator1.classList.add('bg-decorator', 'decorator-1');
        section.appendChild(decorator1);
        
        const decorator2 = document.createElement('div');
        decorator2.classList.add('bg-decorator', 'decorator-2');
        section.appendChild(decorator2);
        
        // 添加技术背景
        const techBg = document.createElement('div');
        techBg.classList.add('tech-bg');
        section.appendChild(techBg);
        
        // 添加小圆点装饰
        for (let i = 0; i < 10; i++) {
            const circle = document.createElement('div');
            circle.classList.add('circle-decorator');
            circle.style.top = `${Math.random() * 100}%`;
            circle.style.left = `${Math.random() * 100}%`;
            circle.style.opacity = Math.random() * 0.5;
            circle.style.width = `${5 + Math.random() * 15}px`;
            circle.style.height = circle.style.width;
            section.appendChild(circle);
        }
    });
});