var proList = 0;
// wow
if($(".wow").length){
	if (!(/msie [6|7|8|9]/i.test(navigator.userAgent))){
		var wow = new WOW({
			boxClass: 'wow',
			animateClass: 'animated',
			offset: 50,
			mobile: true,
			live: true,
  callback: function (e) {
	"use strict"; 
	if ($(e).attr('data-type') === 'proList') {
	  proList += 300;
	  $(e).css("animation-delay", proList + 'ms');
	}
  }
		});
		wow.init();
	};
}
setTimes();
var timeOuts = null;
function setTimes() {"use strict"; 
	timeOuts = setTimeout(function() {
	  proList = 0;
	}, 150);
}	


//header-search
$('.header-search').click(function() {
		$('.main-content-wrap').addClass('show-content-wrap');
		$('.mobile-body-mask').show();
		$('.mobile-body-mask').click(function() {
			$('.mobile-body-mask').hide();
			$('.main-content-wrap').removeClass('show-content-wrap');
		});
});

$(document).ready(function() {
             document.addEventListener("WeixinJSBridgeReady", function() {
            document.getElementById("video").play();
                }, false);
});


//
var isTouch = Modernizr.touch,
	isMobile = false, //区分移动端与PC端
	mobile = false, //区分手机端与平板
	w_width = 0,
	w_height = 0,
	bannerImgh = 620,
	navItem = 0,
	h_height = 0,
	roll = 0,
	sTop = 150,
	produs = 0,
	ST = 0;

var _mousemove;
var _click;
var _mousedown;
var _mouseup;

//移动端事件和PC事件的切换
if (Modernizr.touch) {
	_mousemove = "touchmove";
	_click = "touchend";
	_mousedown = "touchstart";
	_mouseup = "touchend";
} else {
	_mousemove = "mousemove";
	_click = "click";
	_mousedown = "mousedown";
	_mouseup = "mouseup";
};

//function pageBox() {
//	w_width = jQuery(window).width();
//	w_height = jQuery(window).height();
//
//
//	//设置移动端参数
//	if (w_width <= 1024) {
//		isMobile = true;
//	} else if (w_width > 1024) {
//		isMobile = false;
//	};
//	//区分手机端和平板
//	if (w_width <= 640) {
//		mobile = true;
//	} else if (w_width > 640) {
//		mobile = false;
//	};
//	$('.pbanner figure').css('height', w_height*0.5740740740740741);
//	setImgMax($('.pbanner figure img'), 1920, 620, w_width, w_height*0.5740740740740741);
//}
//pageBox();
//jQuery(window).resize(function() {
//	pageBox();
//});


var pageFn = {
	init: function(){
		this.getAudio();
		this.getScrollColor();
		this.getNavs();
		this.setInnerBanner();
		this.getHashs();
		this.getScrollTop();
		this.loader();
	},
	getAudio: function(){
		
	},
	loader: function(){
		
	},
	getNavs: function(){
		$(".menu-btn").click(function(){
			$(".header-menu").toggleClass("active");
			$(".menubtn").toggleClass("active");
			$(".menu-flex").toggleClass("show");
		});
		$(".menu-bg").click(function(){
			$(".header-menu").removeClass("active");
			$(".menubtn").removeClass("active");
			$(".menu-flex").removeClass("show");
		});

		$(".menu-list > li >a").bind("click", function (e) {
			var $navMobile1=jQuery(".menu-flex"),
				$navA1=$navMobile1.find(".menu-list > li >a"),
				$mSubnav1=$navMobile1.find(".menu-leval");
			var hjDD1 = $(this).parents("li");
			if (hjDD1.find(".menu-leval").size() > 0) {
				if (hjDD1.hasClass("active")) {
					hjDD1.find(".menu-leval").stop(false, false).slideUp();
					hjDD1.removeClass("active");
					$navMobile1.removeClass("show");
				} else {
					$navA1.parents('li').removeClass("active");
					$mSubnav1.stop(false, false).slideUp();
					hjDD1.find(".menu-leval").stop(false, false).slideDown();
					hjDD1.addClass("active");
					e.preventDefault();
				}
			}
		});

	},
	getScrollColor: function() {
		function a(){
			var s = $(window).scrollTop();
			s > 30 ? $(".header").addClass('scrollfix'): $(".header").removeClass('scrollfix');
		}	
		$(window).scroll(a);
		a();		
	},
	getHashs: function() {
		if(!isMobile) return;
		var hash = location.hash;
		jQuery(".atlas a,.btnIc a,.menu-leval li a,.enter-btn a").click(function(e){
			var hash=jQuery(this).attr("href").split("#")[1];
			if(hash && jQuery("#"+hash).length==1){
				setScroll("#"+hash);
			}
		});
		function getHash(){
			if(hash){
				setScroll(hash);
			}
		};
		var scnum=0;
		function setScroll(anchorCur){
			$(".header-menu").removeClass("active");
			$(".menubtn").removeClass("active");
			$(".menu-flex").removeClass("show");
			scnum=$('.header').outerHeight();
			jQuery("html,body").animate({ scrollTop: jQuery(anchorCur).offset().top-scnum},1000,"easeOutQuint");
		};
		jQuery(window).load(function() {
			getHash();
		});
	},
	setInnerBanner: function() {
		jQuery(window).scroll(function () {
			var windowTop = jQuery(window).scrollTop();
			if (windowTop < w_height) {
				jQuery('.pbanner figure img,.product-inner').css('transform', "translate(0px," + (windowTop) / 100 + "px)");
			};
		});

		$("img.lazy").lazyload({
			effect: "fadeIn",
			threshold: 10, 
			placeholder: "images/loading.gif"
		});
		if($(".pbanner").length> 0){
			var s1 = new TimelineMax();
			s1.fromTo(".pbanner figure", 5, {scale: 1.1},{scale: 1});
		}


	},
	getHash: function() {
		
	},
	getScrollTop: function(){
		$(".back-top").click(function(){
			jQuery("html,body").animate({ scrollTop: 0},1000,"easeOutQuint");
		});
		if(isMobile){
			function b(){
				var s = $(window).scrollTop();
				s > 30 ? $(".common-bott").fadeIn(): $(".common-bott").fadeOut();
			}	
			$(window).scroll(b);
			b();	
		};
		
	}
}
pageFn.init();


jQuery(window).load(function() {
	jQuery('.article-block').delay(300).scrollClass();
});

//weixin
setPopUp($('.weix'), "二维码");
var aa = $('.weix .img').html();

function setPopUp(obj, title) {
	obj.click(function() {
		var str = '<div class="popUpblack"><div class="popUp"><div class="t">' + title +
			'<span class="close">关闭</span></div><div class="img">'+ aa +'</div></div></div>';
		$("body").append(str);
		jQuery(".popUpblack").fadeIn();
		jQuery(".popUp").animate({
			marginTop: "-127"
		}, 400);
		$(".popUp .close").click(function() {
			$(".popUpblack").remove();
		});
		jQuery(".popUpblack").click(function() {
			$(".popUpblack").remove();
		});
		return false;
	});
};

//window._bd_share_config = { "common": { "bdSnsKey": {}, "bdText": "", "bdMini": "2", "bdMiniList": false, "bdPic": "", "bdStyle": "0", "bdSize": "16" }, "share": {} }; with (document) 0[(getElementsByTagName('head')[0] || body).appendChild(createElement('script')).src = 'http://bdimg.share.baidu.com/static/api/js/share.js?v=89860593.js?cdnversion=' + ~(-new Date() / 36e5)];
(function(jQuery) {
	$.fn.scrollClass = function(config) {
		var defaults = {};
		var config = jQuery.extend(defaults, config);
		var target = this;

		function addAction() {
			var length = target.length;
			for (var i = 0; i < length; i++) {
				if (target.eq(i).hasClass('articleShow')) continue;

				var in_position = target.eq(i).offset().top + 100;
				var window_bottom_position = jQuery(window).scrollTop() + jQuery(window).height();
				if (in_position < window_bottom_position) {
					target.eq(i).addClass('articleShow');
				}
			}
		}
		addAction();

		jQuery(window).on('scroll', function() {
			addAction();
		});
		return target;
	};
})(jQuery);


function setImgMax(img, imgW, imgH, tW, tH) {
	var tWidth = tW || w_width;
	var tHeight = tH || w_height;
	var coe = imgH / imgW;
	var coe2 = tHeight / tWidth;
	if (coe < coe2) {
		var imgWidth = tHeight / coe;
		img.css({
			height: tHeight,
			width: imgWidth,
			left: -(imgWidth - tWidth) / 2,
			top: 0
		});
	} else {
		var imgHeight = tWidth * coe;
		img.css({
			height: imgHeight,
			width: tWidth,
			left: 0,
			top: -(imgHeight - tHeight) / 2
		});
	};

};


