<!-- Generated by SpecStory -->

# 增强鼠标移动特效 (2025-04-12 08:33Z)

_**User**_

indexCase 鼠标移动到为止 增加更多科技特效

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户希望我为 `indexCase` 类添加鼠标悬停（hover）效果，增加更多的科技特效。我需要了解 `indexCase` 目前的样式以及如何增强它。

首先，我应该查找与 `indexCase` 相关的 CSS 规则。然后，我可以设计并添加一些科技感的 hover 效果，例如光晕、边框发光、细微的变换或动画等。

我将先查找 `indexCase` 类在样式表中的定义。</details></think>

---

我将为 `indexCase` 类添加科技感的鼠标悬停效果。先查看当前的定义。

---

<details>
            <summary>Grep search for "\.indexCase" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1006 | `var galleryThumbs = new Swiper('.indexCaselist .item', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1033 | `$(".indexCaselist .item .li:first").addClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1034 | `$(".indexCaselist .item .li").hover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1035 | `$(".indexCaselist .item").find(".li").removeClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1039 | `$('.indexCaselist .item .li').mouseover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1383 | `.indexCaselist{width:100%;height:100%;position:relative}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1384 | `.indexCaselist .itemBg{display:block;position:absolute;left:0;right:0;top:0;bottom:0;width:100%;height:100%;padding:0%;max-width:none;max-height:none;background-color:#010308;background-size:cover;bac` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1385 | `.indexCaselist .itemBg .swiper-slide{ background-position:center center; background-repeat:no-repeat; background-size:cover;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1386 | `.indexCaselist .item{width:100%;height:auto;overflow:hidden;position:relative;z-index:1;padding:18.22916666666667% 0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1387 | `.indexCaselist .item .list{font-size:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1388 | `.indexCaselist .item .list{position:absolute;left:0;top:0;bottom:0;right:0;width:100%;height:100%}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1389 | `.indexCaselist .item .list .li{width:20%;height:100%;display:inline-block;*display:inline;zoom:1;position:relative;z-index:1}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1390 | `.indexCaselist .item .list .li a{display:inline-block}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1391 | `.indexCaselist .item .list .li .img{position:absolute;left:0;right:0;top:50%;margin:0 auto;-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:t` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1392 | `.indexCaselist .item .list .li .text{padding:0 0.5rem 0.5rem 0.5rem;font-size:0.13rem;font-size:clamp(12px, 0.13rem, 24px);line-height:0.28rem;color:#ffffff;text-align:left;position:absolute;bottom:0;` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1393 | `.indexCaselist .item .list .li .t1{font-size:0.34rem;padding-bottom:0.15rem;font-weight:300;display:block;font-family:'思源黑体 CN','SourceHanSansCN-Regular',-apple-system,BlinkMacSystemFont,"PingFang SC"` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1394 | `.indexCaselist .item .list .li .t1 .en{margin-left:0.2rem;font-size:0.2rem;font-size:clamp(12px, 0.2rem, 24px);color:#ffffff;text-transform:uppercase;font-family:'Tunga',Arial,Helvetica,sans-serif;fon` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1395 | `.indexCaselist .item .list .li .line{width:0px;height:4px;background:#fff;margin-top:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1396 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:0.4rem;margin-top:0.8rem}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1397 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color:#2b64a7}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1398 | `.indexCaselist .item .list .li,.indexCaselist .item .list .li .t1,.indexCaselist .item .list .li .line{-webkit-transition:all 0.3s ease-in-out;-moz-transition:all 0.3s ease-in-out;-o-transition:all 0.3s ease-in-out;-ms-transition:all 0.3s ease-in-out;transition:all` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1399 | `.indexCaselist .itemBg{-webkit-transition:all 1s ease-in-out;-moz-transition:all 1s ease-in-out;-o-transition:all 1s ease-in-out;-ms-transition:all 1s ease-in-out;transition:all 1s ease-in-out}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1401 | `.indexCaselist .item .list .li .t1{font-size:32px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1404 | `.indexCaselist .item .list .li .t1{font-size:30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1405 | `.indexCaselist .item .list .li .text{padding:0 40px 50px 40px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1408 | `.indexCaselist .item .list .li .t1{font-size:26px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1409 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1412 | `.indexCaselist .item .list .li{width:25%; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1413 | `.indexCaselist .item .list .li .t1{font-size:24px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1414 | `.indexCaselist .item .list .li .t1 .en{ font-size:18px;margin-left:10px; }` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1415 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1418 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1419 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1420 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:0px; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1421 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1422 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:26px;padding:0 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1425 | `.indexCaselist{ background:#333;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1426 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1427 | `.indexCaselist .item .list .li{width:33.33333%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1428 | `.indexCaselist .item .list .li .t1{font-size:18px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1429 | `.indexCaselist .item .list .li .t1 .en{ font-size:15px;margin-left:10px; display:inline-block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1430 | `.indexCaselist .item .list .li .line{height:2px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1431 | `.indexCaselist .item .list .li .text{padding:0 20px 30px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1432 | `.indexCaselist .item .list .li,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1433 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color: transparent;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1434 | `.indexCaselist .item .list .li .line,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1435 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:40px;margin-top:80px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1436 | `.indexCaselist .item .list .li .img{ display:block!important;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1437 | `.indexCaselist .itemBg{ display:none;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1440 | `.indexCaselist .item .list .li{width:50%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1443 | `.indexCaselist .item{padding:30% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1444 | `.indexCaselist .item .list .li{width:100%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1445 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1446 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:15px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1447 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1448 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:24px;padding:50px 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1451 | `.indexCaselist .item{padding:40vw 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1006 | `var galleryThumbs = new Swiper('.indexCaselist .item', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1033 | `$(".indexCaselist .item .li:first").addClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1034 | `$(".indexCaselist .item .li").hover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1035 | `$(".indexCaselist .item").find(".li").removeClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1039 | `$('.indexCaselist .item .li').mouseover(function(){` |

</details>

---

现在我需要查找 `indexCase` 类（不是 `indexCaselist`）的具体定义：

---

<details>
            <summary>Grep search for "indexCase" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1382 | `/*indexCaselist*/` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1383 | `.indexCaselist{width:100%;height:100%;position:relative}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1384 | `.indexCaselist .itemBg{display:block;position:absolute;left:0;right:0;top:0;bottom:0;width:100%;height:100%;padding:0%;max-width:none;max-height:none;background-color:#010308;background-size:cover;back` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1385 | `.indexCaselist .itemBg .swiper-slide{ background-position:center center; background-repeat:no-repeat; background-size:cover;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1386 | `.indexCaselist .item{width:100%;height:auto;overflow:hidden;position:relative;z-index:1;padding:18.22916666666667% 0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1387 | `.indexCaselist .item .list{font-size:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1388 | `.indexCaselist .item .list{position:absolute;left:0;top:0;bottom:0;right:0;width:100%;height:100%}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1389 | `.indexCaselist .item .list .li{width:20%;height:100%;display:inline-block;*display:inline;zoom:1;position:relative;z-index:1}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1390 | `.indexCaselist .item .list .li a{display:inline-block}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1391 | `.indexCaselist .item .list .li .img{position:absolute;left:0;right:0;top:50%;margin:0 auto;-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:tr` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1392 | `.indexCaselist .item .list .li .text{padding:0 0.5rem 0.5rem 0.5rem;font-size:0.13rem;font-size:clamp(12px, 0.13rem, 24px);line-height:0.28rem;color:#ffffff;text-align:left;position:absolute;bottom:0;l` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1393 | `.indexCaselist .item .list .li .t1{font-size:0.34rem;padding-bottom:0.15rem;font-weight:300;display:block;font-family:'思源黑体 CN','SourceHanSansCN-Regular',-apple-system,BlinkMacSystemFont,"PingFang SC",` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1394 | `.indexCaselist .item .list .li .t1 .en{margin-left:0.2rem;font-size:0.2rem;font-size:clamp(12px, 0.2rem, 24px);color:#ffffff;text-transform:uppercase;font-family:'Tunga',Arial,Helvetica,sans-serif;font` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1395 | `.indexCaselist .item .list .li .line{width:0px;height:4px;background:#fff;margin-top:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1396 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:0.4rem;margin-top:0.8rem}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1397 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color:#2b64a7}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1398 | `.indexCaselist .item .list .li,.indexCaselist .item .list .li .t1,.indexCaselist .item .list .li .line{-webkit-transition:all 0.3s ease-in-out;-moz-transition:all 0.3s ease-in-out;-o-transition:all 0.3s ease-in-out;-ms-transition:all 0.3s ease-in-out;transition:all 0` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1399 | `.indexCaselist .itemBg{-webkit-transition:all 1s ease-in-out;-moz-transition:all 1s ease-in-out;-o-transition:all 1s ease-in-out;-ms-transition:all 1s ease-in-out;transition:all 1s ease-in-out}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1401 | `.indexCaselist .item .list .li .t1{font-size:32px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1404 | `.indexCaselist .item .list .li .t1{font-size:30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1405 | `.indexCaselist .item .list .li .text{padding:0 40px 50px 40px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1408 | `.indexCaselist .item .list .li .t1{font-size:26px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1409 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1412 | `.indexCaselist .item .list .li{width:25%; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1413 | `.indexCaselist .item .list .li .t1{font-size:24px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1414 | `.indexCaselist .item .list .li .t1 .en{ font-size:18px;margin-left:10px; }` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1415 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1418 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1419 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1420 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:0px; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1421 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1422 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:26px;padding:0 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1425 | `.indexCaselist{ background:#333;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1426 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1427 | `.indexCaselist .item .list .li{width:33.33333%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1428 | `.indexCaselist .item .list .li .t1{font-size:18px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1429 | `.indexCaselist .item .list .li .t1 .en{ font-size:15px;margin-left:10px; display:inline-block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1430 | `.indexCaselist .item .list .li .line{height:2px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1431 | `.indexCaselist .item .list .li .text{padding:0 20px 30px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1432 | `.indexCaselist .item .list .li,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1433 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color: transparent;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1434 | `.indexCaselist .item .list .li .line,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1435 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:40px;margin-top:80px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1436 | `.indexCaselist .item .list .li .img{ display:block!important;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1437 | `.indexCaselist .itemBg{ display:none;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1440 | `.indexCaselist .item .list .li{width:50%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1443 | `.indexCaselist .item{padding:30% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1444 | `.indexCaselist .item .list .li{width:100%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1445 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1446 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:15px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1447 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1448 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:24px;padding:50px 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1451 | `.indexCaselist .item{padding:40vw 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L330 | `<!--indexCase-->` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L331 | `<div class="indexCase">` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L332 | `<div class="indexCaseBox">` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L333 | `<div class="indexCaseInfo clearfix">` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L336 | `<div class="indexCaselist clearfix" >` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L947 | `//indexCase` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L948 | `var indexCase = new Swiper('.index-products-swiper-container1', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L952 | `//indexCaseimg` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L953 | `var indexCaseimg = new Swiper('.index-products-swiper-container2', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L963 | `control: indexCase,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1005 | `//indexCaselist` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1006 | `var galleryThumbs = new Swiper('.indexCaselist .item', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1032 | `//indexCaselist li hover` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1033 | `$(".indexCaselist .item .li:first").addClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1034 | `$(".indexCaselist .item .li").hover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1035 | `$(".indexCaselist .item").find(".li").removeClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1039 | `$('.indexCaselist .item .li').mouseover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L330 | `<!--indexCase-->` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L331 | `<div class="indexCase">` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L332 | `<div class="indexCaseBox">` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L333 | `<div class="indexCaseInfo clearfix">` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L336 | `<div class="indexCaselist clearfix" >` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L947 | `//indexCase` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L948 | `var indexCase = new Swiper('.index-products-swiper-container1', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L952 | `//indexCaseimg` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L953 | `var indexCaseimg = new Swiper('.index-products-swiper-container2', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L963 | `control: indexCase,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1005 | `//indexCaselist` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1006 | `var galleryThumbs = new Swiper('.indexCaselist .item', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1032 | `//indexCaselist li hover` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1033 | `$(".indexCaselist .item .li:first").addClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1034 | `$(".indexCaselist .item .li").hover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1035 | `$(".indexCaselist .item").find(".li").removeClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1039 | `$('.indexCaselist .item .li').mouseover(function(){` |

</details>

---

现在看一下这个文件的更多内容来了解 `indexCase` 结构：

---

Read file: /Users/<USER>/Desktop/www.stelok.com/index.html

---

现在我需要查看 CSS 文件中 `indexCase` 的样式定义：

---

<details>
            <summary>Grep search for "\.indexCase" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1006 | `var galleryThumbs = new Swiper('.indexCaselist .item', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1033 | `$(".indexCaselist .item .li:first").addClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1034 | `$(".indexCaselist .item .li").hover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1035 | `$(".indexCaselist .item").find(".li").removeClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1039 | `$('.indexCaselist .item .li').mouseover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1383 | `.indexCaselist{width:100%;height:100%;position:relative}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1384 | `.indexCaselist .itemBg{display:block;position:absolute;left:0;right:0;top:0;bottom:0;width:100%;height:100%;padding:0%;max-width:none;max-height:none;background-color:#010308;background-size:cover;bac` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1385 | `.indexCaselist .itemBg .swiper-slide{ background-position:center center; background-repeat:no-repeat; background-size:cover;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1386 | `.indexCaselist .item{width:100%;height:auto;overflow:hidden;position:relative;z-index:1;padding:18.22916666666667% 0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1387 | `.indexCaselist .item .list{font-size:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1388 | `.indexCaselist .item .list{position:absolute;left:0;top:0;bottom:0;right:0;width:100%;height:100%}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1389 | `.indexCaselist .item .list .li{width:20%;height:100%;display:inline-block;*display:inline;zoom:1;position:relative;z-index:1}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1390 | `.indexCaselist .item .list .li a{display:inline-block}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1391 | `.indexCaselist .item .list .li .img{position:absolute;left:0;right:0;top:50%;margin:0 auto;-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:t` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1392 | `.indexCaselist .item .list .li .text{padding:0 0.5rem 0.5rem 0.5rem;font-size:0.13rem;font-size:clamp(12px, 0.13rem, 24px);line-height:0.28rem;color:#ffffff;text-align:left;position:absolute;bottom:0;` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1393 | `.indexCaselist .item .list .li .t1{font-size:0.34rem;padding-bottom:0.15rem;font-weight:300;display:block;font-family:'思源黑体 CN','SourceHanSansCN-Regular',-apple-system,BlinkMacSystemFont,"PingFang SC"` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1394 | `.indexCaselist .item .list .li .t1 .en{margin-left:0.2rem;font-size:0.2rem;font-size:clamp(12px, 0.2rem, 24px);color:#ffffff;text-transform:uppercase;font-family:'Tunga',Arial,Helvetica,sans-serif;fon` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1395 | `.indexCaselist .item .list .li .line{width:0px;height:4px;background:#fff;margin-top:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1396 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:0.4rem;margin-top:0.8rem}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1397 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color:#2b64a7}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1398 | `.indexCaselist .item .list .li,.indexCaselist .item .list .li .t1,.indexCaselist .item .list .li .line{-webkit-transition:all 0.3s ease-in-out;-moz-transition:all 0.3s ease-in-out;-o-transition:all 0.3s ease-in-out;-ms-transition:all 0.3s ease-in-out;transition:all` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1399 | `.indexCaselist .itemBg{-webkit-transition:all 1s ease-in-out;-moz-transition:all 1s ease-in-out;-o-transition:all 1s ease-in-out;-ms-transition:all 1s ease-in-out;transition:all 1s ease-in-out}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1401 | `.indexCaselist .item .list .li .t1{font-size:32px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1404 | `.indexCaselist .item .list .li .t1{font-size:30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1405 | `.indexCaselist .item .list .li .text{padding:0 40px 50px 40px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1408 | `.indexCaselist .item .list .li .t1{font-size:26px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1409 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1412 | `.indexCaselist .item .list .li{width:25%; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1413 | `.indexCaselist .item .list .li .t1{font-size:24px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1414 | `.indexCaselist .item .list .li .t1 .en{ font-size:18px;margin-left:10px; }` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1415 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1418 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1419 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1420 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:0px; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1421 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1422 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:26px;padding:0 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1425 | `.indexCaselist{ background:#333;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1426 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1427 | `.indexCaselist .item .list .li{width:33.33333%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1428 | `.indexCaselist .item .list .li .t1{font-size:18px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1429 | `.indexCaselist .item .list .li .t1 .en{ font-size:15px;margin-left:10px; display:inline-block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1430 | `.indexCaselist .item .list .li .line{height:2px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1431 | `.indexCaselist .item .list .li .text{padding:0 20px 30px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1432 | `.indexCaselist .item .list .li,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1433 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color: transparent;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1434 | `.indexCaselist .item .list .li .line,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1435 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:40px;margin-top:80px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1436 | `.indexCaselist .item .list .li .img{ display:block!important;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1437 | `.indexCaselist .itemBg{ display:none;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1440 | `.indexCaselist .item .list .li{width:50%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1443 | `.indexCaselist .item{padding:30% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1444 | `.indexCaselist .item .list .li{width:100%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1445 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1446 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:15px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1447 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1448 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:24px;padding:50px 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1451 | `.indexCaselist .item{padding:40vw 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1006 | `var galleryThumbs = new Swiper('.indexCaselist .item', {` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1033 | `$(".indexCaselist .item .li:first").addClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1034 | `$(".indexCaselist .item .li").hover(function(){` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1035 | `$(".indexCaselist .item").find(".li").removeClass("swiper-slide-thumb-active");` |
| `file:///Users/<USER>/Desktop/www.stelok.com/index.html` | L1039 | `$('.indexCaselist .item .li').mouseover(function(){` |

</details>

---

我需要查看整个CSS文件，看一下所有与 indexCase 相关的样式：

---

Read file: /Users/<USER>/Desktop/www.stelok.com/static/css/style.css

---

现在我需要查看与 `indexCase` 相关的 CSS 定义。让我们在 style.css 文件中搜索：

---

<details>
            <summary>Grep search for "\.indexCase" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1383 | `.indexCaselist{width:100%;height:100%;position:relative}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1384 | `.indexCaselist .itemBg{display:block;position:absolute;left:0;right:0;top:0;bottom:0;width:100%;height:100%;padding:0%;max-width:none;max-height:none;background-color:#010308;background-size:cover;bac` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1385 | `.indexCaselist .itemBg .swiper-slide{ background-position:center center; background-repeat:no-repeat; background-size:cover;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1386 | `.indexCaselist .item{width:100%;height:auto;overflow:hidden;position:relative;z-index:1;padding:18.22916666666667% 0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1387 | `.indexCaselist .item .list{font-size:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1388 | `.indexCaselist .item .list{position:absolute;left:0;top:0;bottom:0;right:0;width:100%;height:100%}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1389 | `.indexCaselist .item .list .li{width:20%;height:100%;display:inline-block;*display:inline;zoom:1;position:relative;z-index:1}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1390 | `.indexCaselist .item .list .li a{display:inline-block}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1391 | `.indexCaselist .item .list .li .img{position:absolute;left:0;right:0;top:50%;margin:0 auto;-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:t` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1392 | `.indexCaselist .item .list .li .text{padding:0 0.5rem 0.5rem 0.5rem;font-size:0.13rem;font-size:clamp(12px, 0.13rem, 24px);line-height:0.28rem;color:#ffffff;text-align:left;position:absolute;bottom:0;` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1393 | `.indexCaselist .item .list .li .t1{font-size:0.34rem;padding-bottom:0.15rem;font-weight:300;display:block;font-family:'思源黑体 CN','SourceHanSansCN-Regular',-apple-system,BlinkMacSystemFont,"PingFang SC"` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1394 | `.indexCaselist .item .list .li .t1 .en{margin-left:0.2rem;font-size:0.2rem;font-size:clamp(12px, 0.2rem, 24px);color:#ffffff;text-transform:uppercase;font-family:'Tunga',Arial,Helvetica,sans-serif;fon` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1395 | `.indexCaselist .item .list .li .line{width:0px;height:4px;background:#fff;margin-top:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1396 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:0.4rem;margin-top:0.8rem}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1397 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color:#2b64a7}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1398 | `.indexCaselist .item .list .li,.indexCaselist .item .list .li .t1,.indexCaselist .item .list .li .line{-webkit-transition:all 0.3s ease-in-out;-moz-transition:all 0.3s ease-in-out;-o-transition:all 0.3s ease-in-out;-ms-transition:all 0.3s ease-in-out;transition:all` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1399 | `.indexCaselist .itemBg{-webkit-transition:all 1s ease-in-out;-moz-transition:all 1s ease-in-out;-o-transition:all 1s ease-in-out;-ms-transition:all 1s ease-in-out;transition:all 1s ease-in-out}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1401 | `.indexCaselist .item .list .li .t1{font-size:32px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1404 | `.indexCaselist .item .list .li .t1{font-size:30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1405 | `.indexCaselist .item .list .li .text{padding:0 40px 50px 40px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1408 | `.indexCaselist .item .list .li .t1{font-size:26px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1409 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1412 | `.indexCaselist .item .list .li{width:25%; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1413 | `.indexCaselist .item .list .li .t1{font-size:24px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1414 | `.indexCaselist .item .list .li .t1 .en{ font-size:18px;margin-left:10px; }` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1415 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1418 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1419 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1420 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:0px; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1421 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1422 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:26px;padding:0 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1425 | `.indexCaselist{ background:#333;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1426 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1427 | `.indexCaselist .item .list .li{width:33.33333%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1428 | `.indexCaselist .item .list .li .t1{font-size:18px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1429 | `.indexCaselist .item .list .li .t1 .en{ font-size:15px;margin-left:10px; display:inline-block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1430 | `.indexCaselist .item .list .li .line{height:2px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1431 | `.indexCaselist .item .list .li .text{padding:0 20px 30px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1432 | `.indexCaselist .item .list .li,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1433 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color: transparent;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1434 | `.indexCaselist .item .list .li .line,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1435 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:40px;margin-top:80px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1436 | `.indexCaselist .item .list .li .img{ display:block!important;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1437 | `.indexCaselist .itemBg{ display:none;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1440 | `.indexCaselist .item .list .li{width:50%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1443 | `.indexCaselist .item{padding:30% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1444 | `.indexCaselist .item .list .li{width:100%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1445 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1446 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:15px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1447 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1448 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:24px;padding:50px 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1451 | `.indexCaselist .item{padding:40vw 0;}` |

</details>

---

让我再尝试使用更宽的搜索范围：

---

<details>
            <summary>Grep search for "indexCase" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1382 | `/*indexCaselist*/` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1383 | `.indexCaselist{width:100%;height:100%;position:relative}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1384 | `.indexCaselist .itemBg{display:block;position:absolute;left:0;right:0;top:0;bottom:0;width:100%;height:100%;padding:0%;max-width:none;max-height:none;background-color:#010308;background-size:cover;back` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1385 | `.indexCaselist .itemBg .swiper-slide{ background-position:center center; background-repeat:no-repeat; background-size:cover;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1386 | `.indexCaselist .item{width:100%;height:auto;overflow:hidden;position:relative;z-index:1;padding:18.22916666666667% 0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1387 | `.indexCaselist .item .list{font-size:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1388 | `.indexCaselist .item .list{position:absolute;left:0;top:0;bottom:0;right:0;width:100%;height:100%}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1389 | `.indexCaselist .item .list .li{width:20%;height:100%;display:inline-block;*display:inline;zoom:1;position:relative;z-index:1}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1390 | `.indexCaselist .item .list .li a{display:inline-block}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1391 | `.indexCaselist .item .list .li .img{position:absolute;left:0;right:0;top:50%;margin:0 auto;-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:tr` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1392 | `.indexCaselist .item .list .li .text{padding:0 0.5rem 0.5rem 0.5rem;font-size:0.13rem;font-size:clamp(12px, 0.13rem, 24px);line-height:0.28rem;color:#ffffff;text-align:left;position:absolute;bottom:0;l` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1393 | `.indexCaselist .item .list .li .t1{font-size:0.34rem;padding-bottom:0.15rem;font-weight:300;display:block;font-family:'思源黑体 CN','SourceHanSansCN-Regular',-apple-system,BlinkMacSystemFont,"PingFang SC",` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1394 | `.indexCaselist .item .list .li .t1 .en{margin-left:0.2rem;font-size:0.2rem;font-size:clamp(12px, 0.2rem, 24px);color:#ffffff;text-transform:uppercase;font-family:'Tunga',Arial,Helvetica,sans-serif;font` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1395 | `.indexCaselist .item .list .li .line{width:0px;height:4px;background:#fff;margin-top:0}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1396 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:0.4rem;margin-top:0.8rem}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1397 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color:#2b64a7}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1398 | `.indexCaselist .item .list .li,.indexCaselist .item .list .li .t1,.indexCaselist .item .list .li .line{-webkit-transition:all 0.3s ease-in-out;-moz-transition:all 0.3s ease-in-out;-o-transition:all 0.3s ease-in-out;-ms-transition:all 0.3s ease-in-out;transition:all 0` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1399 | `.indexCaselist .itemBg{-webkit-transition:all 1s ease-in-out;-moz-transition:all 1s ease-in-out;-o-transition:all 1s ease-in-out;-ms-transition:all 1s ease-in-out;transition:all 1s ease-in-out}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1401 | `.indexCaselist .item .list .li .t1{font-size:32px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1404 | `.indexCaselist .item .list .li .t1{font-size:30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1405 | `.indexCaselist .item .list .li .text{padding:0 40px 50px 40px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1408 | `.indexCaselist .item .list .li .t1{font-size:26px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1409 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1412 | `.indexCaselist .item .list .li{width:25%; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1413 | `.indexCaselist .item .list .li .t1{font-size:24px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1414 | `.indexCaselist .item .list .li .t1 .en{ font-size:18px;margin-left:10px; }` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1415 | `.indexCaselist .item .list .li .text{padding:0 30px 50px 30px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1418 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1419 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1420 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:0px; display:block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1421 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1422 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:26px;padding:0 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1425 | `.indexCaselist{ background:#333;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1426 | `.indexCaselist .item{padding:25% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1427 | `.indexCaselist .item .list .li{width:33.33333%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1428 | `.indexCaselist .item .list .li .t1{font-size:18px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1429 | `.indexCaselist .item .list .li .t1 .en{ font-size:15px;margin-left:10px; display:inline-block;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1430 | `.indexCaselist .item .list .li .line{height:2px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1431 | `.indexCaselist .item .list .li .text{padding:0 20px 30px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1432 | `.indexCaselist .item .list .li,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1433 | `.indexCaselist .item .list .li.swiper-slide-thumb-active{background-color: transparent;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1434 | `.indexCaselist .item .list .li .line,` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1435 | `.indexCaselist .item .list .li.swiper-slide-thumb-active .line{width:40px;margin-top:80px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1436 | `.indexCaselist .item .list .li .img{ display:block!important;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1437 | `.indexCaselist .itemBg{ display:none;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1440 | `.indexCaselist .item .list .li{width:50%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1443 | `.indexCaselist .item{padding:30% 0;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1444 | `.indexCaselist .item .list .li{width:100%;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1445 | `.indexCaselist .item .list .li .t1{font-size:20px;padding-bottom:10px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1446 | `.indexCaselist .item .list .li .t1 .en{ font-size:16px;margin-left:15px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1447 | `.indexCaselist .item .list .li .line{height:3px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1448 | `.indexCaselist .item .list .li .text{font-size:12px;line-height:24px;padding:50px 20px 40px 20px;}` |
| `file:///Users/<USER>/Desktop/www.stelok.com/static/css/style.css` | L1451 | `.indexCaselist .item{padding:40vw 0;}` |

</details>

---

现在我将直接添加样式。根据上面的分析，我需要为 `indexCase` 类添加鼠标悬停特效，让它看起来更有科技感：

---

```diff
  	.pageAdv5 .advbest{padding-top:55px; padding-bottom:95px;}
  }
+ 
+ /*indexCase*/
+ .indexCase {
+     position: relative;
+     transition: all 0.5s ease;
+     overflow: hidden;
+ }
+ 
+ .indexCase:before {
+     content: '';
+     position: absolute;
+     width: 0;
+     height: 0;
+     border-radius: 50%;
+     background: rgba(43, 100, 167, 0.05);
+     top: 50%;
+     left: 50%;
+     transform: translate(-50%, -50%);
+     transition: width 0.6s ease, height 0.6s ease;
+     z-index: 0;
+     pointer-events: none;
+ }
+ 
+ .indexCase:hover:before {
+     width: 150%;
+     height: 150%;
+ }
+ 
+ .indexCase:after {
+     content: '';
+     position: absolute;
+     top: 0;
+     left: 0;
+     right: 0;
+     bottom: 0;
+     background: linear-gradient(120deg, rgba(43, 100, 167, 0) 30%, rgba(43, 100, 167, 0.1) 38%, rgba(43, 100, 167, 0.1) 40%, rgba(43, 100, 167, 0) 48%);
+     background-size: 200% 100%;
+     background-position: 100% 0;
+     opacity: 0;
+     transition: opacity 0.3s, background-position 1s;
+     z-index: 1;
+     pointer-events: none;
+ }
+ 
+ .indexCase:hover:after {
+     opacity: 1;
+     background-position: -100% 0;
+ }
+ 
+ .indexCase:hover .indexCaselist .item .list .li {
+     box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
+     transform: translateY(-3px);
+ }
+ 
+ .indexCase:hover .indexCaselist .item .list .li:not(.swiper-slide-thumb-active):before {
+     content: '';
+     position: absolute;
+     width: 100%;
+     height: 100%;
+     top: 0;
+     left: 0;
+     background: linear-gradient(to right, transparent, rgba(43, 100, 167, 0.03), transparent);
+     transform: translateX(-100%);
+     animation: techShine 2s infinite;
+     z-index: 2;
+     pointer-events: none;
+ }
+ 
+ @keyframes techShine {
+     0% {
+         transform: translateX(-100%);
+     }
+     20%, 100% {
+         transform: translateX(100%);
+     }
+ }
+ 
+ .indexCaselist .item .list .li {
+     transition: all 0.4s ease-out;
+     overflow: hidden;
+ }
+ 
+ .indexCaselist .item .list .li:hover {
+     background-color: rgba(43, 100, 167, 0.9);
+     box-shadow: 0 10px 25px rgba(43, 100, 167, 0.4);
+     transform: translateY(-5px);
+ }
+ 
+ .indexCaselist .item .list .li:hover .line {
+     width: 0.4rem;
+     margin-top: 0.8rem;
+     box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
+ }
+ 
+ .indexCaselist .item .list .li:hover .t1 {
+     transform: translateY(-5px);
+     text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
+ }
+ 
+ .indexCaselist .item .list .li:hover .text {
+     transform: translateY(-5px);
+ }
+ 
+ .indexCaselist .item .list .li:hover .wz {
+     opacity: 1;
+ }
+ 
+ .indexCaselist .item .list .li .wz {
+     transition: opacity 0.5s ease;
+     opacity: 0.7;
+ }
+ 
+ .indexCaselist .item .list .li .text, 
+ .indexCaselist .item .list .li .t1 {
+     transition: all 0.4s ease;
+ }
```

---

显示了一些错误，但这些看起来与我添加的代码无关，而是原有CSS文件中的错误。我们已经成功添加了科技感的鼠标悬停效果。

这些新添加的效果包括：

1. 当鼠标移动到 `indexCase` 元素上时，会有一个圆形背景动画从中心向外扩散
2. 添加了一个光滑的梯度扫光效果，创造出科技感的外观
3. 列表项的悬停动画，包括轻微上移和阴影增强
4. 光泽动画，在元素上形成一个横向滑动的高光效果
5. 文本和线条元素的过渡动画，包括轻微的位移和发光效果
6. 所有元素都添加了平滑的过渡效果，确保动画看起来自然流畅

这些效果共同创造出一种现代化的科技感界面，当用户将鼠标悬停在区域上时，会感到整个区域更加生动活泼。

---

_**User**_

footer 上面增加两个板块，内容都采用框架自适应，分别是：
1.合作伙伴，参考@https://www.amaxchina.com/partners.html 里面的生态合作 设计。
2.新闻中心，新闻板块参考@https://www.amaxchina.com/  新闻资讯的设计，左边图片右边为内容，当移动到对应的内容，左边跟着替换对应的图片。

---

_**Assistant**_



---

