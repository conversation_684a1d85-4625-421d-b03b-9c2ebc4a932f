<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8" />
<meta name="format-detection" content="telephone=no" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta name="HandheldFriendly" content="true" />
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
<!--[if lt IE 9]>
    <script type="text/javascript" src="static/js/css3-mediaqueries.js">
    </script>
    <![endif]-->
<meta name="Author" content="易思信网页工作室 www.eczone.net ">
<!--
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
           网站设计制作 by 易思信网页工作室 www.eczone.net   
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
    -->
<title>广州聚算云科技有限公司 - 算力如水 创想无限</title>
<meta name="keywords" content='节能蒸汽疏水装置、控制阀系列、蒸汽系统节能优化、疏水阀管理、蒸汽系统诊断调查' />
<meta name="description" content='思德乐是佛山市孚泺自控技术有限公司的控股公司，由孚泺自控20多年行业经验的工程师和项目部组成，负责孚泺自控公司的产品销售并专注于蒸汽系统与冷凝水应用节能改造。' />
<link rel="shortcut icon" href="favicon.ico" />
<link rel="stylesheet" href="static/css/animate.css" type="text/css" >
<link rel="stylesheet" href="static/css/common.css" type="text/css" >
<link rel="stylesheet" href="static/css/style.css" type="text/css" >
<script type="text/javascript" src="static/js/jquery.min.js"></script> 
<script>
// 平滑滚动到锚点位置
$(document).ready(function() {
  // 为导航菜单中的锚点链接添加点击事件
  $('a[href^="#page"]').on('click', function(e) {
    e.preventDefault();
    
    // 获取目标元素
    var target = $(this.hash);
    if (target.length) {
      // 计算偏移量，考虑到固定导航栏的高度
      var offset = target.offset().top - 80; // 80是导航栏高度，根据实际情况调整
      
      // 平滑滚动到目标位置
      $('html, body').animate({
        scrollTop: offset
      }, 800);
    }
  });
});
</script>
</head>
<body>
<style>
.pageCaselink .circle-bg {
  background: #2b64a7;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
  text-align: center;
  padding: 0;
  box-sizing: border-box;
}
.pageCaselink .circle-bg:hover {
  background: rgb(255, 128, 0);
  transform: scale(1.05);
}
.pageCaselink {
  margin: 40px auto;
  max-width: 1200px;
  width: 100%;
}
.pageCaselink ul {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding: 0;
  margin: 0 auto;
  width: 100%;
}
.pageCaselink ul li {
  position: relative;
  width: 180px;
  height: 180px;
  margin: 0 15px;
  list-style: none;
}

/* 添加响应式样式确保圆形在小屏幕上不变形 */
@media screen and (max-width: 1200px) {
  .pageCaselink ul li {
    width: 160px;
    height: 160px;
    margin: 0 10px;
  }
}

@media screen and (max-width: 900px) {
  .pageCaselink ul {
    flex-wrap: wrap;
    justify-content: center;
  }
  .pageCaselink ul li {
    width: 30vw;
    height: 30vw;
    max-width: 150px;
    max-height: 150px;
    margin: 10px;
  }
}

@media screen and (max-width: 767px) {
  .pageCaselink ul li {
    width: 28vw;
    height: 28vw;
    max-width: 120px;
    max-height: 120px;
    margin: 8px;
  }
  .pageCaselink ul li a p svg {
    width: 40px;
    height: 40px;
  }
  .pageCaselink ul li a p {
    font-size: 14px;
  }
}

@media screen and (max-width: 480px) {
  .pageCaselink ul li {
    width: 26vw;
    height: 26vw;
    max-width: 110px;
    max-height: 110px;
    margin: 5px;
  }
  .pageCaselink ul li a p svg {
    width: 36px;
    height: 36px;
  }
  .pageCaselink ul li a p {
    font-size: 12px;
  }
}

@media screen and (max-width: 605px) {
  .pageCaselink .circle-bg {
    border-radius: 0;
    background: #2b64a7;
  }
  .pageCaselink .circle-bg:hover {
    background: rgb(255, 128, 0);
    transform: scale(1.05);
  }
  .pageCaselink ul li {
    width: 28vw;
    height: 28vw;
    max-width: 120px;
    max-height: 120px;
    margin: 6px;
  }
  .pageCaselink ul li a p svg {
    width: 34px;
    height: 34px;
  }
  .pageCaselink ul li a p {
    font-size: 12px;
  }
}

@media screen and (max-width: 450px) {
  .pageCaselink ul li {
    width: 30vw;
    height: 30vw;
    max-width: 100px;
    max-height: 100px;
    margin: 4px;
  }
  .pageCaselink ul li a p svg {
    width: 32px;
    height: 32px;
  }
  .pageCaselink ul li a p {
    font-size: 11px;
  }
}

.pageCaselink ul li a {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}
.pageCaselink ul li a p {
  margin: 5px 0;
  padding: 0;
  width: 100%;
  text-align: center;
  font-size: 16px;
  line-height: 1.2;
}
.pageCaselink ul li a p:first-child {
  margin-bottom: 10px;
}
.pageCaselink ul li a p svg {
  transition: all 0.3s ease;
  margin: 5px auto;
}
.pageCaselink ul li a:hover p svg {
  transform: translateY(-5px);
  stroke-width: 2;
}
</style>
<section class="wrapper">
  <header class="header"> <a href="/" class="logo"> <img class="hide" src="static/picture/logo.png" alt="" /> <img class="show" src="static/picture/logo2.png" alt="" /> </a>
    <ul class="navs">
      <li class=""><a href="/">首页</a></li>
      <li ><a href="page-abouts.html">关于我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#pageInfo">公司简介<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#pageYJ">企业使命与愿景<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#pageAdvInfo">企业文化<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#pageTeam">团队介绍<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#pageInvestor">投资者关系<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">产品与服务<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">算力调度平台<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">云服务解决方案<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">边缘计算服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">AI算力服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">算力资源池<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="solution-cases.html">解决方案<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">电竞云网吧<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">AI眼镜<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业数字化转型<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">科研机构高性能计算<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">数据分析与大数据处理<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">媒体内容制作与渲染<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="hyyy-hyyy.html">成功案例<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">互联网行业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">人工智能<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">制造业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">金融服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教育与科研<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">政府与公共服务<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">技术与创新<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">技术白皮书<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">研发团队<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">专利与知识产权<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">合作与生态<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">合作伙伴<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">生态体系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">合作模式<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">资源中心<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">产品手册<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教程与指南<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">行业洞察<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">算力知识库<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li><a href="contact-contacts.html">联系我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">销售咨询<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">技术支持<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">媒体联系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">办公地点<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
    </ul>
    <div class="header-menu">
      <div class="header-search"> <span class="iconfont">&nbsp;</span> </div>
      <div class="menu-btn">
        <p>MENU</p>
        <div class="menubtn"> <span></span> </div>
      </div>
      <div class="menu-flex">
        <div class="menu-bg"></div>
        <div class="menu-right">
          <ul class="menu-list">
            <li><a href="/">首页</a></li>
            <li ><a href="page-abouts.html">关于我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="#pageInfo">公司简介<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#pageYJ">企业使命与愿景<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#pageAdvInfo">企业文化<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#pageTeam">团队介绍<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#pageInvestor">投资者关系<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">产品与服务<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">算力调度平台<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">云服务解决方案<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">边缘计算服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">AI算力服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">算力资源池<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="solution-cases.html">解决方案<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">电竞云网吧<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">AI眼镜<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业数字化转型<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">科研机构高性能计算<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">数据分析与大数据处理<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">媒体内容制作与渲染<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="hyyy-hyyy.html">成功案例<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">互联网行业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">人工智能<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">制造业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">金融服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教育与科研<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">政府与公共服务<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">技术与创新<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">技术白皮书<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">研发团队<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">专利与知识产权<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">合作与生态<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">合作伙伴<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">生态体系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">合作模式<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">资源中心<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">产品手册<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教程与指南<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">行业洞察<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">算力知识库<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="contact-contacts.html">联系我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">销售咨询<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">技术支持<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">媒体联系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">办公地点<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
          </ul>
          <div class="menu-ip-down">
            <div class="online-shopp"> </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class="main-content-wrap">
    <div class="content-wrap">
      <div class="head-search">
        <form action="search-search.html" method="post">
          <input class="search-ipt" type="text" name="query" placeholder="输入关键词...">
          <input class="search-btn" type="submit" name="submit" value="搜索" title="搜索">
        </form>
      </div>
    </div>
  </div>
  <div class="mobile-body-mask"></div>
  <div class="pbanner">
    <div class="product-hide">
      <figure><img class="pc" src="static/picture/index-case6.jpg" alt=""/>
        <div class="sjimg" style="background-image:url(static/imagesindex-case6.jpg);"> </div>
      </figure>
    </div>
    <div class="series global-fix">
      <p class="article-block slidetop">JUSUAN Cloud</p>
      <div class="series-title article-block slidetop detay1 syht"> <strong>创新提供 · 无限可能</strong> </div>
    </div>
    <div class="sea-down defaul"> <i></i> <span>向下滑动</span> </div>
  </div>
  
  <!--pageTop-->
  <div class="pageTop">
    <div class="w1600 clearfix">
      <div class="pageNav">
        <ul class="navlist clearfix swiper-wrapper">
          <li class="on"><a href="#pageInfo"><span>公司简介</span></a></li>
          <li><a href="#pageYJ"><span>企业使命与愿景</span></a></li>
          <li><a href="#pageAdvInfo"><span>企业文化</span></a></li>
          <li><a href="#pageTeam"><span>团队介绍</span></a></li>
          <li><a href="#pageInvestor"><span>投资者关系</span></a></li>
        </ul>
      </div>
    </div>
  </div>
  <!--pageTop end--> 
  
  <!--page-->
  <div class="page clearfix"> 
    <!--pageInfo-->
    <div id="pageInfo" class="pageInfo clearfix">
      <div class="pageTit"><span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">About Us</span>
        <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"><span class="syht">公司介绍</span></div>
      </div>
      <link href="static/css/style_1.css" rel="stylesheet" type="text/css" />
      <div>
        <div class="pageText w1600 clearfix">
          <table border="0" cellpadding="0" class="pageT syht ke-zeroborder">
            <tbody>
              <tr>
                <td class="td1"><img src="static/picture/page-t1.jpg" alt="" /></td>
                <td class="td2"> 算力如水 创想无限 </td>
                <td class="td3"><img src="static/picture/page-t2.jpg" alt="" /></td>
              </tr>
            </tbody>
          </table>
          <br />
          <table width="100%" border="0" cellpadding="0" style="max-width:840px;margin:0 auto;" class="ke-zeroborder">
            <tbody>
              <tr>
                <td><p style="text-align:center;"> 聚算云是一家专注于算力资源智能调度的科技公司，致力于打造连接算力供应方与需求方的智能平台，通过「共享经济」模式重塑算力分配规则，让算力资源像水一样自由流动，为数字经济时代提供坚实的基础设施支持。 </p></td>
              </tr>
            </tbody>
          </table>
          <div class="pageCaselink">
            <ul>
              <li class="wow zoomInUp" data-wow-duration="1.2s" data-wow-delay="0.3s"> <a href="#" class="circle-bg">
                <p>
                  <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                    <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                    <line x1="6" y1="6" x2="6.01" y2="6"></line>
                    <line x1="6" y1="18" x2="6.01" y2="18"></line>
                    <path d="M12 6h4"></path>
                    <path d="M12 18h4"></path>
                  </svg>
                </p>
                <p> 算力调度 </p>
                </a> </li>
              <li class="wow zoomInUp" data-wow-duration="1.2s" data-wow-delay="0.5s"> <a href="#" class="circle-bg">
                <p>
                  <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>
                    <path d="M13 8a5 5 0 0 0-5 5"></path>
                    <line x1="11" y1="3" x2="11" y2="0"></line>
                    <line x1="18" y1="6" x2="20" y2="4"></line>
                    <line x1="20" y1="13" x2="23" y2="13"></line>
                  </svg>
                </p>
                <p> 算力共享 </p>
                </a> </li>
              <li class="wow zoomInUp" data-wow-duration="1.2s" data-wow-delay="0.7s"> <a href="#" class="circle-bg">
                <p>
                  <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                </p>
                <p> 算力投资 </p>
                </a> </li>
              <li class="wow zoomInUp" data-wow-duration="1.2s" data-wow-delay="0.9s"> <a href="#" class="circle-bg">
                <p>
                  <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
                    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
                    <circle cx="12" cy="10" r="2"></circle>
                    <path d="M12 14v4"></path>
                    <path d="M9 14h6"></path>
                  </svg>
                </p>
                <p> 智能应用 </p>
                </a> </li>
            </ul>
          </div>
        </div>
        <div class="pageAd clearfix" style="background-image:url(static/images/page-ad.jpg);">
          <video type="video/mp4; codecs=" avc1.4d401e,mp4a.40.0"" muted="" autoplay="autoplay" loop="loop" preload="auto" poster="Design/King-Tin/images/page-ad.jpg" id="video" data-setup="{autoplay:true}" webkit-playsinline="true" x-webkit-airplay="true" playsinline="true" x5-video-player-type="h5" x5-video-player-fullscreen="true" width="100%" height="100%">
            <source autoplay="" type="video/mp4; codecs=" avc1.4d401e,mp4a.40.0"" src="../Design/King-Tin/images/index-about.mp4 ">
          </video>
          <div class="wz syht">
            <p class="wow zoomIn" data-wow-duration="1.2s" data-wow-delay="0.3s"> 我们专注于算力系统优化与调度，让AI和数字创新的技术门槛更低，成本更可控。 </p>
          </div>
        </div>
        <div id="pageYJ" class="pageYJ w1600 ">
          <p style="text-align:center;"> 
            在当今AI与数字经济浪潮中，聚算云以独特的"铁锹哲学"，开启算力时代的新革命。<br/>
            正如创始人所言："在与众多AI创业团队接触过程中，我看到一个普遍痛点：算力成本飙升，往往占研发预算的一半以上，而自建服务器又面临高昂的硬件和运维成本。"<br/>
            聚算云选择深挖底层价值——不做数字淘金客，只做算力掘金者的装备供应商，打造最锋利的「铁锹」。<br/>
            我们致力于通过「共享经济」重塑算力分配规则，将闲置算力与急需算力的企业智能匹配，让算力像水电一样成为标准化公共服务。
          </p>
          <p> <br />
          </p>
          <div class="pageTit"> <span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">our vision</span>
            <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"> <span class="syht">我们的愿景</span> </div>
          </div>
          <ul class="clearfix">
            <li class="on wow fadeInRight" data-wow-duration="1.2" data-wow-delay="0.3s">
              <div class="item li1">
                <div class="tit">
                  <div class="wz"> <span class="en">Power<br />
                    Distribution</span><span class="cn syht">成为算力资源调度的国家电网</span> </div>
                  <div class="ImgBox"> <img alt="" src="static/picture/page-xy-icon1.png" /> </div>
                </div>
              </div>
            </li>
            <li class="wow fadeInRight" data-wow-duration="1.2" data-wow-delay="0.6s">
              <div class="item li2">
                <div class="tit">
                  <div class="wz"> <span class="en">Computing<br />
                    Democratization</span><span class="cn syht">让算力像水电一样成为标准化公共服务</span> </div>
                  <div class="ImgBox"> <img alt="" src="static/picture/page-xy-icon2.png" /> </div>
                </div>
              </div>
            </li>
            <li class="wow fadeInRight" data-wow-duration="1.2" data-wow-delay="0.9s">
              <div class="item li3">
                <div class="tit">
                  <div class="wz"> <span class="en">Digital<br />
                    Infrastructure</span><span class="cn syht">成为数字经济时代的<br />
                    基础设施建设者</span> </div>
                  <div class="ImgBox"> <img alt="" src="static/picture/page-xy-icon3.png" /> </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <br />
        <div class="pageAdv pageAdv4 clearfix">

          <div id="pageAdvInfo" class="pageAdvInfo w1600 clearfix">
            <div class="pageAdvInfoB">
              <div class="pageTit"> <span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">Market Size</span>
                <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"> <span class="syht">市场规模</span> </div>
              </div>
              <div class="market-content">
                <div class="market-left">
                  <div class="market-timeline">
                    <div class="timeline-item wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.3s">
                      <div class="timeline-number">01</div>
                      <div class="timeline-content">
                        <h3>全球市场</h3>
                        <p>全球AI算力市场2024年突破2000亿美元，年增长27.6%，中国占全球28%，增速18%，AI算力需求每18个月增长1000倍，市场潜力巨大。</p>
                      </div>
                    </div>
                    <div class="timeline-item wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.6s">
                      <div class="timeline-number red">02</div>
                      <div class="timeline-content">
                        <h3>政策红利</h3>
                        <p>政策红利体现在"东数西算"国家战略，算力租赁纳入战略储备，为公司发展提供了有力的政策支持和广阔的市场空间。</p>
                      </div>
                    </div>
                    <div class="timeline-item wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.9s">
                      <div class="timeline-number">03</div>
                      <div class="timeline-content">
                        <h3>市场痛点</h3>
                        <p>算力三难悖论是买不起、用不好、跟不上，头部垄断85%资源，中小企业支付40%-60%"算力税"，供需错配严重，市场痛点明显。</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="market-right">
                  <div class="market-image wow fadeInRight" data-wow-duration="1s" data-wow-delay="0.5s">
                    <img src="static/images/market-building.jpg" alt="市场规模示意图" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
  <!--End-->
  </div>
<!-- 团队介绍板块 -->
<div id="pageTeam" class="pageTeam w1600 clearfix">
  <div class="pageTit"> <span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">Our Team</span>
    <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"> <span class="syht">团队介绍</span> </div>
  </div>
  <div class="teamContent">
    <p style="text-align:center;">聚算云拥有一支由技术创新者、商业战略家和行业专家组成的多元化团队。我们汇集了来自互联网、云计算、人工智能和共享经济领域的人才，共同打造算力共享的未来。</p>
    <div class="teamMembers">
      <div class="team-nav team-nav-prev">&#10094;</div>
      <div class="team-nav team-nav-next">&#10095;</div>
      <ul class="clearfix" id="teamSlider">
        <li class="wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.3s">
          <div class="member">
            <div class="avatar">
              <img src="static/picture/team1.jpg" alt="创始人">
            </div>
            <div class="info">
              <h3>张明</h3>
              <p>创始人兼CEO</p>
              <div class="desc">
                互联网和共享经济领域资深创业者，曾成功创办多家科技公司。凭借敏锐的商业洞察力，提出"算力共享经济"理念，致力于通过创新商业模式降低数字经济门槛。
              </div>
            </div>
          </div>
        </li>
        <li class="wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.5s">
          <div class="member">
            <div class="avatar">
              <img src="static/picture/team2.jpg" alt="技术总监">
            </div>
            <div class="info">
              <h3>李强</h3>
              <p>技术总监</p>
              <div class="desc">
                前互联网巨头技术专家，拥有超过15年的云计算和分布式系统开发经验，主导开发了聚算云的核心调度算法和资源分配系统。
              </div>
            </div>
          </div>
        </li>
        <li class="wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.7s">
          <div class="member">
            <div class="avatar">
              <img src="static/picture/team3.jpg" alt="运营总监">
            </div>
            <div class="info">
              <h3>王丽</h3>
              <p>运营总监</p>
              <div class="desc">
                拥有丰富的互联网产品和平台运营经验，精通用户增长策略和社区建设，负责聚算云平台生态构建和合作伙伴拓展。
              </div>
            </div>
          </div>
        </li>
        <li class="wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.7s">
          <div class="member">
            <div class="avatar">
              <img src="static/picture/team1.jpg" alt="产品经理">
            </div>
            <div class="info">
              <h3>赵宁</h3>
              <p>产品经理</p>
              <div class="desc">
                拥有多年SaaS产品设计经验，擅长用户体验优化和产品迭代，主导设计了聚算云平台的用户界面和交互流程。
              </div>
            </div>
          </div>
        </li>
        <li class="wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.7s">
          <div class="member">
            <div class="avatar">
              <img src="static/picture/team2.jpg" alt="市场总监">
            </div>
            <div class="info">
              <h3>孙婷</h3>
              <p>市场总监</p>
              <div class="desc">
                前知名科技公司市场主管，对B2B市场有深入理解，负责聚算云品牌建设和市场推广策略。
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</div>

<!-- 核心团队板块 -->
<div id="pageCoreTeam" class="pageCoreTeam w1600 clearfix">
  <div class="pageTit"> <span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">Core Team</span>
    <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"> <span class="syht">核心团队</span> </div>
  </div>
  <div class="coreTeamContent">
    <p style="text-align:center;">聚算云核心团队由行业顶尖专家组成，拥有深厚的技术底蕴和丰富的商业实践经验，致力于推动算力共享经济的创新发展。</p>
    
    <!-- 核心团队卡片网格 -->
    <div class="core-team-container">
      <div class="core-team-grid">
        <!-- 01 技术支撑 -->
        <div class="core-team-card core-team-01 wow fadeInLeft" data-wow-duration="1.2" data-wow-delay="0.3s">
          <div class="card-number">01</div>
          <div class="card-content">
            <h3>技术支撑</h3>
            <p>技术构建采取"双轨并行"策略：与赋能集团深度战略合作，充分利用成熟技术平台和专家资源；积极组建自有核心技术团队，聚焦云计算架构优化与网络安全防护。已启动与多所顶尖高校的产学研合作，推动前沿技术探索。目前具备智能资源调度与混合云管理的核心能力。</p>
          </div>
        </div>
        
        <!-- 02 智囊顾问 -->
        <div class="core-team-card core-team-02 wow fadeInRight" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="card-number">02</div>
          <div class="card-content">
            <h3>智囊顾问</h3>
            <p>顾问团队汇聚全球顶尖学术资源，包括资深行业专家（如数学模型领域权威学者Villani）与前沿科研领军人物（如资深专业人士胡铁君教授）。在云计算架构优化、智能调度算法等核心方向提供战略指导，凭借深厚学术网络，为团队嫁接关键产学研资源。</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 投资者关系板块 -->
<div id="pageInvestor" class="pageInvestor w1600 clearfix">
  <div class="pageTit"> <span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">Investor Relations</span>
    <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"> <span class="syht">投资者关系</span> </div>
  </div>
  <div class="investorContent">
    <p style="text-align:center;">聚算云致力于为投资者创造长期稳定的价值。我们的算力共享模式不仅为社会带来更高效的资源配置，也为投资者提供了稳定且可持续的回报。</p>
    
    <div class="investorHighlights">
      <ul class="clearfix">
        <li class="wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.3s">
          <div class="highlight">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#2b64a7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 20v-6M6 20V10M18 20V4"></path>
              </svg>
            </div>
            <h3>增长潜力</h3>
            <p>算力市场以每年300%的速度增长，聚算云处于这一万亿级市场的前沿</p>
          </div>
        </li>
        <li class="wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.5s">
          <div class="highlight">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#2b64a7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                <line x1="9" y1="9" x2="9.01" y2="9"></line>
                <line x1="15" y1="9" x2="15.01" y2="9"></line>
              </svg>
            </div>
            <h3>稳定回报</h3>
            <p>通过收益保障机制和多元化客户基础，为投资者提供持续稳定的现金流</p>
          </div>
        </li>
        <li class="wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.7s">
          <div class="highlight">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#2b64a7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
                <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
                <line x1="6" y1="1" x2="6" y2="4"></line>
                <line x1="10" y1="1" x2="10" y2="4"></line>
                <line x1="14" y1="1" x2="14" y2="4"></line>
              </svg>
            </div>
            <h3>透明管理</h3>
            <p>基于区块链的透明结算系统，让每一次算力调度和费用结算都清晰可查</p>
          </div>
        </li>
      </ul>
    </div>
    
    <div class="investorCTA">
      <a href="#" class="btn">下载投资者手册</a>
      <a href="#" class="btn">联系投资者关系部</a>
    </div>
  </div>
</div>
  
  <!--footer-->
  <div class="footer clearfix"> 
    <!--footer-nav-->
    <div class="footer-nav">
      <div class="w1600 clearfix">
        <div class="left">
          <div class="leftBox clearfix">
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="#">企业信息</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="page-1751-1752.html">公司简介</a></li>
                    <li class="t2"><a href="contact-contacts.html">联系方式</a></li>
                    <li class="t2"><a href="#">办公地址</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="#">快速链接</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="page-abouts.html">关于我们</a></li>
                    <li class="t2"><a href="#">产品服务</a></li>
                    <li class="t2"><a href="solution-cases.html">解决方案</a></li>
                    <li class="t2"><a href="hyyy-hyyy.html">成功案例</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="#">资源链接</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="#">资源下载</a></li>
                    <li class="t2"><a href="#">技术支持</a></li>
                    <li class="t2"><a href="#">常见问题</a></li>
                    <li class="t2"><a href="#">使用指南</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="#">法律信息</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="#">用户协议</a></li>
                    <li class="t2"><a href="#">隐私政策</a></li>
                    <li class="t2"><a href="#">知识产权声明</a></li>
                    <li class="t2"><a href="#">免责声明</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="rightBox clearfix">
            <table width="100%">
              <tbody>
                <tr>
                  <td><h1> 服务热线 </h1></td>
                </tr>
                <tr>
                  <td class="tel"><h1> <span>+86 13800138000</span> </h1>
                    <p> 诚挚为您服务 </p></td>
                </tr>
              </tbody>
            </table>
            <div class="bottom-share"> <span>关注我们：</span> <a class="iconfont email" href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">&#xe6db;</a> <a class="iconfont qq" href="http://wpa.qq.com/msgrd?v=3&uin=395202420&site=kingtin&menu=yes" target="_blank" rel="noopener noreferrer">&#xe60f;</a> <a class="iconfont weix"  rel="noopener noreferrer">&#xe660;<span class="img"><img src="static/picture/202203011551003232.jpg" alt="" /><br />
              </span></a> 
              </span></a> </div>
          </div>
        </div>
      </div>
      <div class="bottom-wz">
        <div class="w1600 clearfix">聚算云，让算力如水般自由流动，为创新提供无限可能。</div>
      </div>
    </div>
    <!--footer-nav end-->
    <div class="bq clearfix">
      <div class="w1600 clearfix">
        <div class="bqname"> &copy;2023 广州聚算云科技有限公司 版权所有 <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener">粤ICP备2022022663号</a> </div>
        <div class="beian"> 网站设计: <a href="http://www.eczone.net/" target="_blank" rel="noopener">ECZONE</a>
        </div>
      </div>
    </div>
  </div>
  <!--End footer-->
  <div class="common-bott">
    <div class="customer-service commontop iconfont" title="联系客服">
      &#xe660;
      <div class="qr-code-popup">
        <img src="static/images/contact-qr.png" alt="联系客服二维码" />
        <p>扫码联系客服</p>
      </div>
    </div>
    <div class="back-top commontop iconfont">&#xe731;</div>
  </div>
</section>
<script type="text/javascript" src="static/js/wow.min2.js"></script> 
<script type="text/javascript" src="static/js/plugin.js"></script> 
<script type="text/javascript" src="static/js/page.js"></script> 
<script>
var scrolling = true;
var timer = null;
$(document).on('mousewheel DOMMouseScroll', function (e) {
        if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
        navigateUp();
        } else {
        if (e.pageY <= w_height && scrolling && !isMobile) {
        clearTimeout(timer);
        scrolling = false;
        timer = setTimeout(function () {
        jQuery("html,body").stop(true, true).animate({ scrollTop: w_height }, 1200, "easeOutQuint")
        }, 100)
        }
        }
});
function navigateUp() {
        scrolling = true;
}
</script> 
<script>
//pageYJ li
$(".pageYJ ul li:first").addClass("on");
$(".pageYJ ul li").hover(function(){
	  $(".pageYJ ul").find("li").removeClass("on");
	  $(this).addClass("on");
  })
</script>

<script>
// 团队成员轮播控制
$(document).ready(function() {
  var slider = $('#teamSlider');
  var sliderContainer = $('.teamMembers');
  var currentPosition = 0;
  var slideCount = slider.find('li').length;
  
  // 预加载团队成员图片
  slider.find('img').each(function() {
    var img = new Image();
    img.src = $(this).attr('src');
    img.onload = function() {
      // 图片加载完成后应用过渡效果
      $(this).css('opacity', 1);
    }
  });
  
  // 根据屏幕宽度设置可见项目数
  function updateVisibleItems() {
    if ($(window).width() <= 767) {
      return 1;
    } else if ($(window).width() <= 1200) {
      return 2;
    } else {
      return 3;
    }
  }
  
  // 获取单个元素的实际宽度（包括外边距）
  function getSingleItemWidth() {
    var itemWidth = 0;
    var firstItem = slider.find('li:first');
    
    // 获取实际宽度，包括margin
    itemWidth = firstItem.outerWidth(true);
    
    return itemWidth;
  }
  
  // 计算最大位置
  function getMaxPosition() {
    return Math.max(0, slideCount - updateVisibleItems());
  }
  
  // 调整所有卡片高度统一
  function adjustCardHeights() {
    // 重置高度
    slider.find('.member .info').css('height', 'auto');
    
    // 找出最高的卡片信息区域
    var maxInfoHeight = 0;
    slider.find('.member .info').each(function() {
      var infoHeight = $(this).outerHeight();
      if (infoHeight > maxInfoHeight) {
        maxInfoHeight = infoHeight;
      }
    });
    
    // 应用统一高度
    if (maxInfoHeight > 0) {
      slider.find('.member .info').css('height', maxInfoHeight + 'px');
    }
  }
  
  // 初始化
  updateNavButtons();
  adjustCardHeights();
  
  // 窗口大小改变时重新计算
  $(window).resize(function() {
    var maxPosition = getMaxPosition();
    
    // 调整当前位置，确保不超出范围
    if (currentPosition > maxPosition) {
      currentPosition = maxPosition;
    }
    
    updateSliderPosition();
    updateNavButtons();
    adjustCardHeights();
  });
  
  // 客服二维码点击事件（移动端）
  $('.customer-service').click(function(e) {
    e.preventDefault();
    e.stopPropagation();
    
    // 只在移动端执行点击逻辑
    if ($(window).width() <= 1024) {
      $(this).toggleClass('active');
      
      // 点击其他地方关闭二维码
      $(document).off('click.qrcode').on('click.qrcode', function(event) {
        if (!$(event.target).closest('.customer-service').length) {
          $('.customer-service').removeClass('active');
          $(document).off('click.qrcode');
        }
      });
    }
  });
  
  // 窗口大小改变时关闭二维码弹窗
  $(window).resize(function() {
    $('.customer-service').removeClass('active');
  });
  
  // 上一个按钮点击事件
  $('.team-nav-prev').click(function() {
    if (currentPosition > 0) {
      currentPosition--;
      updateSliderPosition();
      updateNavButtons();
    }
  });
  
  // 下一个按钮点击事件
  $('.team-nav-next').click(function() {
    if (currentPosition < getMaxPosition()) {
      currentPosition++;
      updateSliderPosition();
      updateNavButtons();
    }
  });
  
  // 更新滑块位置 - 修改为基于单个元素宽度移动
  function updateSliderPosition() {
    var itemWidth = getSingleItemWidth();
    slider.css('transform', 'translateX(' + (-currentPosition * itemWidth) + 'px)');
  }
  
  // 更新导航按钮状态
  function updateNavButtons() {
    var maxPosition = getMaxPosition();
    
    if (currentPosition <= 0) {
      currentPosition = 0;
      $('.team-nav-prev').addClass('disabled');
    } else {
      $('.team-nav-prev').removeClass('disabled');
    }
    
    if (currentPosition >= maxPosition) {
      currentPosition = maxPosition;
      $('.team-nav-next').addClass('disabled');
    } else {
      $('.team-nav-next').removeClass('disabled');
    }
    
    // 如果项目少于可见数量，隐藏导航
    if (slideCount <= updateVisibleItems()) {
      $('.team-nav').hide();
    } else {
      $('.team-nav').show();
    }
  }
});
</script>

<style>
/* 团队介绍样式 */
.pageTeam {
  padding: 50px 0;
  margin: 0 auto;
  position: relative;
}

.teamContent {
  margin-top: 30px;
}

.teamMembers {
  position: relative;
  margin-top: 40px;
  overflow: hidden;
  padding: 0 50px;
  box-sizing: border-box;
  max-width: 100%;
}

.teamMembers ul {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  transition: transform 0.5s ease;
  margin: 0;
  padding: 0;
  width: 100%;
  box-sizing: border-box;
}

.teamMembers ul li {
  flex: 0 0 calc(33.33% - 30px);
  min-width: calc(33.33% - 30px);
  width: calc(33.33% - 30px);
  margin: 0 15px;
  box-sizing: border-box;
  overflow: hidden; /* 确保内容不溢出 */
}

.member {
  background: #f9f9f9;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.member:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.member .avatar {
  width: 100%;
  height: 0;
  padding-bottom: 60%;
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
}

.member .avatar img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center 15%;
  transition: transform 0.3s ease;
  opacity: 0;
  animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.member .info {
  padding: 20px;
  box-sizing: border-box;
}

.member .info h3 {
  margin: 0;
  font-size: 18px;
  color: #2b64a7;
}

.member .info p {
  margin: 5px 0 15px;
  font-size: 14px;
  color: #666;
}

.member .info .desc {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
}

.team-nav {
  position: absolute;
  top: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  background: #2b64a7;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  font-size: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.team-nav:hover {
  background: rgb(255, 128, 0);
}

.team-nav-prev {
  left: 5px;
}

.team-nav-next {
  right: 5px;
}

/* 针对自适应设计添加更严格的尺寸控制 */
@media screen and (max-width: 1200px) {
  .teamMembers ul li {
    flex: 0 0 calc(50% - 20px);
    min-width: calc(50% - 20px);
    width: calc(50% - 20px);
    margin: 0 10px;
  }
  
  .member .avatar {
    padding-bottom: 70%; /* 调整图片容器高度比例 */
  }
  
  .investorHighlights ul li {
    width: 45%;
    margin: 0 2.5% 30px;
  }
}

@media screen and (max-width: 767px) {
  .teamMembers {
    padding: 0 30px;
    margin-left: 0;
    margin-right: 0;
  }
  
  .teamMembers ul li {
    flex: 0 0 calc(100% - 20px);
    min-width: calc(100% - 20px);
    width: calc(100% - 20px);
    margin: 0 10px;
  }
  
  .member .avatar {
    padding-bottom: 66%; /* 更适合小屏幕的比例 */
  }
  
  .investorHighlights ul li {
    width: 100%;
    margin: 0 0 20px;
  }
  
  .investorCTA .btn {
    display: block;
    margin: 10px auto;
    max-width: 80%;
  }
  
  .team-nav {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    font-size: 16px;
  }
}

/* 优化团队成员图片容器，修复溢出问题 */
#teamSlider {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

#teamSlider li {
  transition: transform 0.3s ease;
}

.member {
  height: auto;
  display: flex;
  flex-direction: column;
}

.member .avatar {
  max-height: 240px;
  padding-bottom: 65%; /* 更合适的比例，避免图片被过度拉伸 */
}

.member .info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.member .info .desc {
  flex: 1;
  overflow-y: auto;
  max-height: 100px; /* 限制描述文字高度，避免过长 */
}

/* 核心团队板块样式 */
.pageCoreTeam {
  padding: 50px 0;
  margin: 0 auto;
}

.coreTeamContent {
  margin-top: 30px;
}

.core-team-container {
  margin-top: 40px;
  padding: 0 20px;
}

.core-team-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  max-width: 900px;
  margin: 0 auto;
}

.core-team-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 35px 30px;
  box-shadow: 0 12px 35px rgba(0,0,0,0.08), 0 4px 15px rgba(0,0,0,0.04);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  min-height: 280px;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.core-team-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(43, 100, 167, 0.02) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.core-team-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 25px 60px rgba(43, 100, 167, 0.15), 0 8px 25px rgba(0,0,0,0.1);
  border-color: rgba(43, 100, 167, 0.2);
}

.core-team-card:hover::before {
  opacity: 1;
}

/* 不同卡片的特色背景 */
.core-team-01 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.core-team-02 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.core-team-card .card-number {
  font-size: 80px;
  font-weight: 900;
  line-height: 0.8;
  opacity: 0.3;
  position: absolute;
  top: 15px;
  right: 20px;
  z-index: 1;
  font-family: 'Arial Black', Arial, sans-serif;
  color: rgba(255, 255, 255, 0.4);
}

.core-team-card .card-content {
  position: relative;
  z-index: 2;
  padding-top: 20px;
  flex: 1;
}

.core-team-card h3 {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 18px;
  line-height: 1.3;
  letter-spacing: -0.3px;
}

.core-team-card p {
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

.core-team-card:hover h3 {
  transform: translateY(-3px);
  transition: transform 0.3s ease;
}

.core-team-card:hover p {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* 核心团队响应式布局 */

/* 桌面端大屏幕 */
@media screen and (min-width: 1025px) {
  .core-team-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    max-width: 900px;
  }
  
  .core-team-card {
    min-height: 280px;
    height: auto;
    padding: 35px 30px;
  }
  
  .core-team-card .card-number {
    font-size: 80px;
    top: 15px;
    right: 20px;
    opacity: 0.3;
  }
  
  .core-team-card .card-content {
    padding-top: 20px;
  }
  
  .core-team-card h3 {
    font-size: 26px;
  }
  
  .core-team-card p {
    font-size: 15px;
  }
}

/* 平板端布局 - 1列 */
@media screen and (max-width: 1024px) and (min-width: 769px) {
  .core-team-container {
    padding: 0 30px;
  }
  
  .core-team-grid {
    grid-template-columns: 1fr !important;
    gap: 30px;
    max-width: 600px;
  }
  
  .core-team-card {
    height: auto;
    min-height: 220px;
    padding: 35px 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .core-team-card .card-number {
    font-size: 60px !important;
    position: relative !important;
    top: auto !important;
    right: auto !important;
    margin-right: 25px;
    opacity: 0.4 !important;
    flex-shrink: 0;
  }
  
  .core-team-card .card-content {
    padding-top: 0 !important;
    flex: 1;
  }
  
  .core-team-card h3 {
    font-size: 24px !important;
    margin-bottom: 15px !important;
  }
  
  .core-team-card p {
    font-size: 15px !important;
    line-height: 1.6 !important;
  }
}

/* 手机端布局 - 1列 */
@media screen and (max-width: 768px) {
  .core-team-container {
    margin-top: 40px;
    padding: 0 20px;
  }
  
  .core-team-grid {
    grid-template-columns: 1fr !important;
    gap: 25px;
  }
  
  .core-team-card {
    height: auto;
    min-height: 200px;
    padding: 30px 25px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .core-team-card .card-number {
    font-size: 50px !important;
    position: relative !important;
    top: auto !important;
    right: auto !important;
    margin-bottom: 12px;
    opacity: 0.4 !important;
    align-self: flex-end;
  }
  
  .core-team-card .card-content {
    padding-top: 0 !important;
    width: 100%;
  }
  
  .core-team-card h3 {
    font-size: 22px !important;
    margin-bottom: 15px !important;
  }
  
  .core-team-card p {
    font-size: 14px !important;
    line-height: 1.6 !important;
  }
}

/* 小屏手机端优化 */
@media screen and (max-width: 480px) {
  .core-team-container {
    padding: 0 15px;
  }
  
  .core-team-card {
    padding: 25px 20px;
    min-height: 180px;
  }
  
  .core-team-card .card-number {
    font-size: 40px !important;
    margin-bottom: 8px;
    opacity: 0.5 !important;
  }
  
  .core-team-card h3 {
    font-size: 20px !important;
    margin-bottom: 12px !important;
  }
  
  .core-team-card p {
    font-size: 13px !important;
    line-height: 1.5 !important;
  }
}

/* 投资者关系样式 */
.pageInvestor {
  padding: 50px 0;
  margin: 0 auto;
}

.investorContent {
  margin-top: 30px;
}

.investorHighlights {
  margin-top: 40px;
}

.investorHighlights ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.investorHighlights ul li {
  width: 30%;
  margin: 0 1.5% 30px;
}

.highlight {
  background: #fff;
  border-radius: 10px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.highlight:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.highlight .icon {
  margin-bottom: 15px;
}

.highlight h3 {
  margin: 0 0 15px;
  font-size: 18px;
  color: #2b64a7;
}

.highlight p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
}

.investorCTA {
  margin-top: 40px;
  text-align: center;
}

.investorCTA .btn {
  display: inline-block;
  padding: 10px 25px;
  margin: 0 10px;
  background: #2b64a7;
  color: #fff;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.investorCTA .btn:hover {
  background: rgb(255, 128, 0);
  transform: translateY(-3px);
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .teamMembers ul li {
    flex: 0 0 calc(50% - 20px);
    min-width: calc(50% - 20px);
    width: calc(50% - 20px);
    margin: 0 10px;
  }
  
  .investorHighlights ul li {
    width: 45%;
    margin: 0 2.5% 30px;
  }
}

@media screen and (max-width: 767px) {
  .teamMembers {
    padding: 0 30px;
  }
  
  .teamMembers ul li {
    flex: 0 0 calc(100% - 20px);
    min-width: calc(100% - 20px);
    width: calc(100% - 20px);
    margin: 0 10px;
  }
  
  .investorHighlights ul li {
    width: 100%;
    margin: 0 0 20px;
  }
  
  .investorCTA .btn {
    display: block;
    margin: 10px auto;
    max-width: 80%;
  }
  
  .team-nav {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    font-size: 16px;
  }
}

.team-nav.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 修改团队成员的图片样式，解决溢出问题 */
.teamMembers li {
  height: auto !important;
  margin-bottom: 20px !important;
}

.member {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.member .avatar {
  width: 100%;
  height: 0;
  padding-bottom: 62%; /* 适合显示人像的更合理比例 */
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
}

.member .avatar img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center 15%; /* 更好地显示人物面部 */
  transition: transform 0.3s ease;
}

.member:hover .avatar img {
  transform: scale(1.05);
}

/* 确保团队成员内容容器高度一致 */
.teamMembers ul li .member .info {
  min-height: 150px;
  display: flex;
  flex-direction: column;
}

/* 市场规模板块样式 */
/* 市场规模标题样式与团队介绍一致 */
#pageAdvInfo .pageTit {
  text-align: center;
  margin-bottom: 30px;
  margin-top: 10px;
}

.market-content {
  display: flex;
  align-items: flex-start;
  gap: 40px;
  margin-top: 20px;
}

.market-left {
  flex: 3;
  max-width: 65%;
}

.market-right {
  flex: 2;
  max-width: 35%;
}

.market-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40px;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 25px;
  top: 50px;
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, #ddd, transparent);
}

.timeline-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #2b64a7;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-right: 20px;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.timeline-number.red {
  background: #d42c2c;
}

.timeline-content {
  flex: 1;
}

.timeline-content h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
  color: #2b64a7;
  font-weight: bold;
}

.timeline-content p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
}

.market-image {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  margin-top: 20px;
}

.market-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(43, 100, 167, 0.1), rgba(43, 100, 167, 0.3));
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.market-image:hover::before {
  opacity: 1;
}

.market-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.market-image:hover img {
  transform: scale(1.05);
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .market-content {
    gap: 30px;
  }
  
  .market-left {
    flex: 2;
    max-width: 60%;
  }
  
  .market-right {
    flex: 1;
    max-width: 40%;
  }
  
  .timeline-content h3 {
    font-size: 18px;
  }
  
  .timeline-content p {
    font-size: 13px;
  }
}

@media screen and (max-width: 992px) {
  .market-left {
    flex: 3;
    max-width: 70%;
  }
  
  .market-right {
    flex: 2;
    max-width: 30%;
  }
}

@media screen and (max-width: 767px) {
  .market-content {
    flex-direction: column;
    gap: 30px;
  }
  
  .market-left,
  .market-right {
    max-width: 100%;
  }
  
  .market-right {
    order: -1; /* 在移动端将图片放在上方 */
    margin-bottom: 20px;
  }
  
  .market-image {
    max-width: 80%;
    margin: 0 auto;
  }
  
  .timeline-item {
    margin-bottom: 30px;
  }
  
  .timeline-number {
    width: 40px;
    height: 40px;
    font-size: 16px;
    margin-right: 15px;
  }
  
  .timeline-item:not(:last-child)::after {
    left: 20px;
    top: 40px;
    height: 30px;
  }
  
  .timeline-content h3 {
    font-size: 16px;
  }
  
  .timeline-content p {
    font-size: 13px;
  }
}

/* 调整common-bott布局为垂直排列 */
.common-bott {
  flex-direction: column !important;
  align-items: flex-end !important;
}

/* 客服图标样式 */
.customer-service {
  display: block !important;
  text-align: center !important;
  border-radius: 50% !important;
  color: #fff !important;
  cursor: pointer !important;
  transition: 0.32s !important;
  width: 44px !important;
  height: 44px !important;
  line-height: 44px !important;
  font-size: 14px !important;
  margin-bottom: 10px !important;
  margin-right: 0 !important;
  background: rgba(255, 128, 0, 1) !important;
  box-shadow: 0 4px 15px rgba(255, 128, 0, 0.4);
  animation: pulse 2s infinite;
  position: relative;
  overflow: visible;
}

.customer-service:hover {
  background: rgba(255, 140, 20, 1) !important;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 128, 0, 0.6);
}

.customer-service::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
  transform: rotate(45deg);
  animation: shine 3s infinite;
  z-index: 1;
}

/* 二维码弹窗样式 */
.qr-code-popup {
  position: absolute;
  right: 70px;
  top: 50%;
  transform: translateY(-50%);
  background: #ffffff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-width: 180px;
}

.qr-code-popup::before {
  content: '';
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid #ffffff;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}

.qr-code-popup img {
  width: 150px;
  height: 150px;
  display: block;
  margin: 0 auto 10px;
  border-radius: 8px;
}

.qr-code-popup p {
  text-align: center;
  margin: 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* PC端悬浮显示 */
@media screen and (min-width: 1025px) {
  .customer-service:hover .qr-code-popup {
    opacity: 1;
    visibility: visible;
    transform: translateY(-50%) translateX(-5px);
  }
}

/* 移动端点击显示 */
@media screen and (max-width: 1024px) {
  .qr-code-popup {
    position: fixed;
    right: 15px;
    left: auto;
    top: auto;
    bottom: 140px;
    transform: none;
    transition: all 0.3s ease;
    z-index: 9999;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
  }
  
  .qr-code-popup::before {
    right: 25px;
    left: auto;
    top: auto;
    bottom: -8px;
    transform: none;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #ffffff;
    border-bottom: none;
  }
  
  .customer-service.active .qr-code-popup {
    opacity: 1;
    visibility: visible;
    transform: translateY(-10px);
  }
}

/* 小屏幕手机优化 */
@media screen and (max-width: 480px) {
  .qr-code-popup {
    right: 10px !important;
    bottom: 130px !important;
    min-width: 170px !important;
  }
  
  .qr-code-popup::before {
    right: 20px !important;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(255, 128, 0, 0.4);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 128, 0, 0.7);
  }
  100% {
    box-shadow: 0 4px 15px rgba(255, 128, 0, 0.4);
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 响应式调整 */
@media screen and (min-width: 1025px) {
  .customer-service {
    width: 0.44rem !important;
    height: 0.44rem !important;
    line-height: 0.44rem !important;
    font-size: 0.14rem !important;
    font-size: clamp(12px, 0.14rem, 24px) !important;
  }
  
  .customer-service:hover {
    line-height: 0.4rem !important;
  }
}

@media screen and (max-width: 1024px) {
  .customer-service {
    width: 44px !important;
    height: 44px !important;
    line-height: 44px !important;
    font-size: 14px !important;
    margin-bottom: 8px !important;
  }
  
  .customer-service:hover {
    line-height: 40px !important;
  }
}

@media screen and (max-width: 641px) {
  .customer-service {
    margin-bottom: 8px !important;
  }
}
</style>
</body>
</html>
