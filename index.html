<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8" />
<meta name="format-detection" content="telephone=no" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta name="HandheldFriendly" content="true" />
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
<!--[if lt IE 9]>
    <script type="text/javascript" src="static/js/css3-mediaqueries.js">
    </script>
    <![endif]-->
<meta name="Author" content="易思信网页工作室 www.eczone.net ">
    <!--
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
           网站设计制作 by 易思信网页工作室 www.eczone.net   
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
    --> 
<title>广州聚算云科技有限公司 - 算力如水 创想无限</title>
<meta name="keywords" content="算力调度平台、云服务解决方案、边缘计算服务、AI算力服务、算力资源池、算力共享网络">
<meta name="description" content="聚算云扎根粤港澳大湾区，以'让算力如水般自由流动'为使命，打造连接算力供需双方的智能平台，让普通人也能参与AI基建红利，让企业以低成本获取高性能算力。">
    <link rel="shortcut icon" href="favicon.ico" />
<link rel="stylesheet" href="static/css/swiper.min.css" type="text/css">
    <link rel="stylesheet" href="static/css/common.css" type="text/css" >
    <link rel="stylesheet" href="static/css/style.css" type="text/css" >
<link rel="stylesheet" href="static/css/index.css" type="text/css" >
    <script type="text/javascript" src="static/js/jquery.min.js"></script>
<style>
/* 解决方案轮播导航按钮样式 */
.solution-prev, .solution-next {
	display: block;
	text-align: center;
	border-radius: 50%;
	color: #fff;
	background: rgba(0,0,0,0.2);
	cursor: pointer;
	transition: 0.32s;
	width: 44px;
	height: 44px;
	line-height: 44px;
	font-size: 14px;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	z-index: 10;
}
.solution-prev:hover, .solution-next:hover {
	background: rgba(0,0,0,0.4);
	line-height: 40px;
}
.solution-prev {
	left: 20px;
}
.solution-next {
	right: 20px;
}
.solution-prev:after, .solution-next:after {
	content: "";
	font-family: "iconfont" !important;
}
.solution-prev:after {
	content: "\e63c"; /* 使用icon-danjiantouzuo图标 */
}
.solution-next:after {
	content: "\e629"; /* 使用icon-danjiantouyou图标 */
}

@media screen and (max-width: 768px) {
.solution-prev, .solution-next {
	width: 36px;
	height: 36px;
	line-height: 36px;
	font-size: 12px;
}
.solution-prev:hover, .solution-next:hover {
	line-height: 34px;
}
.solution-prev {
	left: 10px;
}
.solution-next {
	right: 10px;
}
}
/* 确保轮播项目不受导航按钮影响 */
.indexCaselist .item {
	overflow: hidden;
}
</style>
<style>
/* 合作伙伴轮播样式修复 */
.indexPartners {
    padding: 70px 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #e6eaf0 100%);
    position: relative;
    overflow: hidden;
}
.indexPartners:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, transparent 20%, #f7f7f7 20%, #f7f7f7 21%, transparent 21%, transparent 34%, #f7f7f7 34%, #f7f7f7 35%, transparent 35%);
    background-size: 25px 25px;
    opacity: 0.3;
    pointer-events: none;
}
.partners-tech-container {
    padding: 40px 20px;
    max-width: 1200px;
    margin: 0 auto;
}
.partners-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    justify-content: center;
}
.partner-item {
    perspective: 1000px;
    -webkit-perspective: 1000px;
    -moz-perspective: 1000px;
    height: 154px;
    width: 100%;
    cursor: pointer;
    position: relative;
    margin-bottom: 30px;
    transition: transform 0.3s ease;
}
.partner-item:hover {
    transform: translateY(-5px);
}
.partner-inner {
    position: relative;
    width: 100%;
    height: 70%;
    text-align: center;
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border-radius: 15px;
    background: rgba(255,255,255,0.92);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}
.partner-item:hover .partner-inner {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
.partner-item:hover .partner-inner {
    transform: rotateY(180deg);
    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
}
.partner-item.flipped .partner-inner {
    transform: rotateY(180deg);
    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
}
.partner-front, .partner-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    backface-visibility: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}
.partner-front {
    background-color: white;
    border-radius: 15px;
    z-index: 2;
    display: block;
    padding: 0;
    margin: 0;
    overflow: hidden;
}
.partner-back {
    background: linear-gradient(135deg, #2b64a7 0%, #164785 100%);
    color: white;
    transform: rotateY(180deg);
    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
    border-radius: 15px;
    z-index: 1;
}
.partner-logo {
    width: 100%;
    height: 100%;
    display: block;
    overflow: hidden;
    margin: 0;
    padding: 0;
    position: relative;
}
.partner-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease, filter 0.5s ease;
    filter: grayscale(30%);
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
}
.partner-item:hover .partner-logo img {
    transform: scale(1.1);
    filter: grayscale(0%);
}
.partner-back h4 {
    font-size: 18px;
    margin: 0 0 15px 0;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
.partner-back p {
    font-size: 14px;
    margin: 0 0 15px 0;
    opacity: 0.9;
    line-height: 1.5;
}
.partner-back .theme-btn {
    background-color: white;
    color: #2b64a7;
    padding: 6px 12px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: inline-block;
}
.partner-back .theme-btn:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
/* 添加科技感动画效果 */
.partner-item:after {
    content: '';
    position: absolute;
    top: 0;
    left: -150%;
    width: 120%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255,255,255,0.3), transparent);
    transform: skewX(-20deg);
    transition: 0.75s;
    z-index: 3;
    pointer-events: none;
}
.partner-item:hover:after {
    left: 150%;
}
/* 添加3D悬停效果 */
.partner-item:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    z-index: 1;
    opacity: 0;
    transition: 0.5s;
    pointer-events: none;
}
.partner-item:hover:before {
    opacity: 1;
}
.partner-item.animated {
    animation: fadeInScale 0.6s ease forwards;
}
@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}
/* 降级支持 */
.no-3d-support .partner-back {
    opacity: 0;
    transform: none;
    transition: opacity 0.5s ease;
}
.no-3d-support .partner-item:hover .partner-inner {
    transform: none;
}
/* 图片加载状态 */
.partner-item:not(.image-loaded) .partner-logo {
    background: linear-gradient(90deg, #f0f0f0, #e0e0e0, #f0f0f0);
    background-size: 200% 100%;
    animation: loadingPulse 1.5s infinite;
}
@keyframes loadingPulse {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .partners-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .partner-front h4, .partner-front p {
        display: none;
    }
    .partners-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }
    .partner-item {
        height: 150px;
    }
    .partner-logo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        margin: 0;
        padding: 0;
    }
}

@media (max-width: 480px) {
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    .partner-item {
        height: 120px;
    }
}
.partner-item:hover .partner-logo img {
    transform: scale(1.15);
    filter: grayscale(0%);
}
/* 移除原有翻转卡片的效果 */
.partner-item:hover .partner-inner {
    transform: none;
    -webkit-transform: none;
    -moz-transform: none;
}
.partner-back {
    display: none;
}

/* 移动设备触摸提示 */
@media (max-width: 768px) {
    .partner-item::after {
        content: '';
        position: absolute;
        right: 10px;
        top: 10px;
        width: 30px;
        height: 30px;
        background: rgba(255,255,255,0.9);
        border-radius: 50%;
        z-index: 10;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: flex;
        justify-content: center;
        align-items: center;
        pointer-events: none;
    }
    .partner-item::before {
        content: '↺';
        position: absolute;
        right: 18px;
        top: 14px;
        color: #2b64a7;
        font-size: 14px;
        z-index: 11;
        pointer-events: none;
    }
    .partner-item.flipped::before {
        content: '✕';
        right: 19px;
        top: 15px;
    }
    .partner-front h4, .partner-front p {
        padding-right: 35px;
    }
    .partner-item:hover .partner-inner {
        transform: none;
        -webkit-transform: none;
        -moz-transform: none;
    }
    .partner-inner {
        transform-style: preserve-3d;
        -webkit-transform-style: preserve-3d;
        -moz-transform-style: preserve-3d;
    }
}

/* 触摸设备检测 */
@media (hover: none) {
    .partner-item:hover .partner-inner {
        transform: none;
        -webkit-transform: none;
        -moz-transform: none;
    }
}
/* 添加动画效果 */
.partner-item {
    animation: fadeIn 0.8s ease-in-out;
}
@keyframes fadeIn {
from {
opacity: 0;
transform: translateY(20px);
}
to {
opacity: 1;
transform: translateY(0);
}
}
/* 新闻中心样式 */
.news-container {
    display: flex;
    margin-top: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    border-radius: 12px;
    overflow: hidden;
    background-color: #fff;
    transition: all 0.3s ease;
}
.news-container:hover {
    box-shadow: 0 15px 40px rgba(43, 100, 167, 0.15);
    transform: translateY(-5px);
}
.news-image-container {
    flex: 0 0 55%;
    position: relative;
    min-height: 450px;
    border-right: 1px solid rgba(0,0,0,0.03);
    overflow: hidden;
}
.news-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    transition: all 0.8s ease;
}
.news-image:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.02), transparent);
    z-index: 1;
}
.news-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 35px;
    background: linear-gradient(to top, rgba(0,0,0,0.85), rgba(0,0,0,0));
    color: #fff;
    transition: all 0.4s ease;
    z-index: 2;
}
.news-image-container:hover .news-image {
    transform: scale(1.05);
}
.news-content .news-title {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 12px;
    text-shadow: 0 2px 3px rgba(0,0,0,0.2);
    transform: translateY(0);
    transition: all 0.4s ease;
}
.news-content .news-date {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 15px;
    color: #e6e6e6;
    position: relative;
    display: inline-block;
    padding-left: 20px;
    transform: translateY(0);
    transition: all 0.4s ease 0.05s;
}
.news-content .news-date:before {
    content: '\e6b9';
    font-family: 'iconfont';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #2b64a7;
}
.news-content .news-desc {
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 25px;
    opacity: 0.8;
    transform: translateY(0);
    transition: all 0.4s ease 0.1s;
}
.news-content .theme-btn {
    display: inline-block;
    padding: 10px 24px;
    background-color: #2b64a7;
    color: #fff;
    border-radius: 30px;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(43, 100, 167, 0.3);
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    transition: all 0.4s ease 0.15s;
}
.news-content .theme-btn:hover {
    background-color: #1e4d8c;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(43, 100, 167, 0.4);
}
.news-content .theme-btn:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150%;
    height: 200%;
    background: rgba(255,255,255,0.15);
    transform: translate(-50%, -50%) rotate(45deg);
    opacity: 0;
    transition: all 0.5s ease;
}
.news-content .theme-btn:hover:after {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(45deg) scale(1.5);
}
.news-items-container {
    flex: 0 0 45%;
    padding: 0;
    border-left: 1px solid #f5f5f5;
    background: #fdfdfd;
    display: flex;
    flex-direction: column;
}
.news-item {
    padding: 20px 25px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.news-item:last-child {
    border-bottom: none;
}
.news-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: transparent;
    transition: all 0.3s ease;
}
.news-item:hover, .news-item.active {
    background-color: #f7fafd;
}
.news-item.active:before, .news-item:hover:before {
    background-color: #2b64a7;
}
.news-item .item-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    transition: all 0.3s ease;
    line-height: 1.4;
}
.news-item:hover .item-title, .news-item.active .item-title {
    color: #2b64a7;
}
.news-item .item-date {
    font-size: 13px;
    color: #999;
    display: flex;
    align-items: center;
}
.news-item .item-date:before {
    content: '\e6b9';
    font-family: 'iconfont';
    margin-right: 6px;
    font-size: 12px;
    color: #2b64a7;
    opacity: 0.7;
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .news-container {
        flex-direction: column;
    }
    .news-image-container, .news-items-container {
        flex: 0 0 100%;
    }
    .news-image-container {
        min-height: 300px;
    }
    .news-items-container {
        border-left: none;
        border-top: 1px solid #f0f0f0;
    }
.logo img.hide {
	display: none !important;
}
.logo img.show {
	display: block !important;
	opacity: 1 !important;
	visibility: visible !important;
}
}

@media (max-width: 768px) {
    .news-image-container {
        min-height: 250px;
    }
    .news-content {
        padding: 25px;
    }
    .news-content .news-title {
        font-size: 18px;
    }
    .news-item {
        padding: 15px 20px;
    }
}
/* SVG背景动画 */
.indexNews {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
	background-color: transparent;
    z-index: 1;
}
.svg-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
.svg-background svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
/* 网格和点阵动画 */
.grid-pattern {
    animation: movePattern1 40s linear infinite;
}
.dot-pattern {
    animation: movePattern2 30s linear infinite;
}

/* 线条动画 */
@keyframes lineDash {
    to {
        stroke-dashoffset: -1000;
    }
}
@keyframes lineFloat {
0% {
transform: translateY(0) translateX(0);
}
50% {
transform: translateY(10px) translateX(5px);
}
100% {
transform: translateY(0) translateX(0);
}
}
@keyframes pointPulse {
0% {
r: 3;
opacity: 0.4;
}
50% {
r: 5;
opacity: 0.55;
}
100% {
r: 3;
opacity: 0.4;
}
}
@keyframes verticalSway {
0% {
transform: translateX(0);
}
50% {
transform: translateX(15px);
}
100% {
transform: translateX(0);
}
}

/* 图案动画 */
@keyframes movePattern1 {
0% {
transform: translateX(0) translateY(0);
}
100% {
transform: translateX(40px) translateY(40px);
}
}
@keyframes movePattern2 {
0% {
transform: translateX(0) translateY(0);
}
100% {
transform: translateX(-20px) translateY(-20px);
}
}
.animated-line {
    stroke-dasharray: 10, 15;
    animation: lineDash 40s linear infinite, lineFloat 20s ease-in-out infinite;
}
.line1 {
    animation-delay: 0s;
}
.line2 {
    animation-delay: -5s;
}
.line3 {
    animation-delay: -10s;
}
.curve1, .curve2 {
    stroke-dasharray: 10, 20;
    animation: lineDash 60s linear infinite;
}
.curve1 {
    animation-delay: 0s;
}
.curve2 {
    animation-delay: -30s;
}
.vertical-line {
    stroke-dasharray: 5, 8;
    animation: verticalSway 25s ease-in-out infinite;
}
.vline1 {
	animation-delay: -2s;
}
.vline2 {
	animation-delay: -6s;
}
.vline3 {
	animation-delay: -10s;
}
.vline4 {
	animation-delay: -14s;
}
.vline5 {
	animation-delay: -18s;
}
.point {
    animation: pointPulse 8s ease-in-out infinite;
}
.point1 {
	animation-delay: 0s;
}
.point2 {
	animation-delay: -1.5s;
}
.point3 {
	animation-delay: -3s;
}
.point4 {
	animation-delay: -4.5s;
}
.point5 {
	animation-delay: -6s;
}
@keyframes movePattern3 {
0% {
transform: translateX(0) translateY(0);
}
50% {
transform: translateX(10px) translateY(-15px);
}
100% {
transform: translateX(0) translateY(0);
}
}
@keyframes movePattern4 {
0% {
transform: translateX(0) translateY(0);
}
50% {
transform: translateX(-5px) translateY(12px);
}
100% {
transform: translateX(0) translateY(0);
}
}
@keyframes moveWave {
0% {
transform: translateX(0);
}
100% {
transform: translateX(-50%);
}
}
.animated-pattern {
    animation: movePattern1 15s ease-in-out infinite;
}
.animated-pattern2 {
    animation: movePattern2 20s ease-in-out infinite;
}
.animated-pattern3 {
    animation: movePattern3 25s ease-in-out infinite;
}
.animated-pattern4 {
    animation: movePattern4 18s ease-in-out infinite;
}
.wave {
    animation: moveWave 20s linear infinite;
}
.wave2 {
    animation: moveWave 15s linear infinite reverse;
}
/* 蜂窝网格动画 */
.hex-grid {
    animation: fadeMove 80s linear infinite;
}
/* 电路板动画 */
.circuit-board {
    animation: fadeMove2 90s linear infinite;
}
.circuit {
    stroke-dasharray: 10, 4;
    animation: circuitFlow 30s linear infinite;
}

/* 节点脉动动画 */
@keyframes nodePulse {
0% {
r: 2;
opacity: 0.5;
}
50% {
r: 3.5;
opacity: 0.7;
}
100% {
r: 2;
opacity: 0.5;
}
}
.node {
    animation: nodePulse 5s ease-in-out infinite;
}
.node1 {
	animation-delay: -0.5s;
}
.node2 {
	animation-delay: -1.0s;
}
.node3 {
	animation-delay: -1.5s;
}
.node4 {
	animation-delay: -2.0s;
}
.node5 {
	animation-delay: -2.5s;
}
.node6 {
	animation-delay: -3.0s;
}
.node7 {
	animation-delay: -3.5s;
}
.node8 {
	animation-delay: -4.0s;
}

/* 连接线动画 */
@keyframes connectFade {
0%, 100% {
opacity: 0;
}
50% {
opacity: 0.3;
}
}
.connection {
    stroke-dasharray: 5, 5;
    animation: connectFade 8s ease-in-out infinite, circuitFlow 30s linear infinite;
}
.connection:nth-child(1) {
	animation-delay: -0.5s, 0s;
}
.connection:nth-child(2) {
	animation-delay: -1.0s, -3s;
}
.connection:nth-child(3) {
	animation-delay: -1.5s, -6s;
}
.connection:nth-child(4) {
	animation-delay: -2.0s, -9s;
}
.connection:nth-child(5) {
	animation-delay: -2.5s, -12s;
}
.connection:nth-child(6) {
	animation-delay: -3.0s, -15s;
}
.connection:nth-child(7) {
	animation-delay: -3.5s, -18s;
}
.connection:nth-child(8) {
	animation-delay: -4.0s, -21s;
}

/* 数据流动画 */
@keyframes flowLine1 {
0% {
transform: translate(15%, 20%);
opacity: 0;
}
5% {
opacity: 0.8;
}
45% {
opacity: 0.8;
}
50% {
transform: translate(35%, 65%);
opacity: 0;
}
100% {
transform: translate(35%, 65%);
opacity: 0;
}
}
@keyframes flowLine2 {
0% {
transform: translate(35%, 65%);
opacity: 0;
}
5% {
opacity: 0.8;
}
45% {
opacity: 0.8;
}
50% {
transform: translate(55%, 25%);
opacity: 0;
}
100% {
transform: translate(55%, 25%);
opacity: 0;
}
}
@keyframes flowLine3 {
0% {
transform: translate(55%, 25%);
opacity: 0;
}
5% {
opacity: 0.8;
}
45% {
opacity: 0.8;
}
50% {
transform: translate(75%, 55%);
opacity: 0;
}
100% {
transform: translate(75%, 55%);
opacity: 0;
}
}
@keyframes flowLine4 {
0% {
transform: translate(75%, 55%);
opacity: 0;
}
5% {
opacity: 0.8;
}
45% {
opacity: 0.8;
}
50% {
transform: translate(45%, 35%);
opacity: 0;
}
100% {
transform: translate(45%, 35%);
opacity: 0;
}
}
@keyframes flowLine5 {
0% {
transform: translate(45%, 35%);
opacity: 0;
}
5% {
opacity: 0.8;
}
45% {
opacity: 0.8;
}
50% {
transform: translate(65%, 80%);
opacity: 0;
}
100% {
transform: translate(65%, 80%);
opacity: 0;
}
}
@keyframes flowLine6 {
0% {
transform: translate(65%, 80%);
opacity: 0;
}
5% {
opacity: 0.8;
}
45% {
opacity: 0.8;
}
50% {
transform: translate(15%, 20%);
opacity: 0;
}
100% {
transform: translate(15%, 20%);
opacity: 0;
}
}
/* 数据流点 */
.flow1 {
	animation: flowLine1 8s linear infinite;
}
.flow2 {
	animation: flowLine2 8s linear infinite;
	animation-delay: -1.3s;
}
.flow3 {
	animation: flowLine3 8s linear infinite;
	animation-delay: -2.6s;
}
.flow4 {
	animation: flowLine4 8s linear infinite;
	animation-delay: -3.9s;
}
.flow5 {
	animation: flowLine5 8s linear infinite;
	animation-delay: -5.2s;
}
.flow6 {
	animation: flowLine6 8s linear infinite;
	animation-delay: -6.5s;
}

/* 数字码浮动 */
@keyframes floatCode {
0% {
transform: translateY(0);
opacity: 0.2;
}
50% {
transform: translateY(-10px);
opacity: 0.3;
}
100% {
transform: translateY(0);
opacity: 0.2;
}
}
.code {
    animation: floatCode 10s ease-in-out infinite;
}
.code1 {
	animation-delay: -1s;
}
.code2 {
	animation-delay: -3s;
}
.code3 {
	animation-delay: -5s;
}
.code4 {
	animation-delay: -7s;
}

/* 电路流动 */
@keyframes circuitFlow {
to {
stroke-dashoffset: -100;
}
}

/* 背景移动 */
@keyframes fadeMove {
0% {
transform: translateX(0) translateY(0);
opacity: 0.4;
}
50% {
opacity: 0.5;
}
100% {
transform: translateX(30px) translateY(30px);
opacity: 0.4;
}
}
@keyframes fadeMove2 {
0% {
transform: translateX(0) translateY(0);
opacity: 0.4;
}
50% {
opacity: 0.5;
}
100% {
transform: translateX(-30px) translateY(-30px);
opacity: 0.4;
}
}
/* 新闻背景样式 */
.indexNews {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
}
.tech-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 0.4;
}
.tech-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(#2b64a7 1px, transparent 1px), radial-gradient(#2b64a7 1px, transparent 1px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
}
.tech-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.tech-lines div {
    position: absolute;
    top: 20%;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(43, 100, 167, 0.5) 20%, rgba(43, 100, 167, 0.5) 80%, transparent);
    animation: moveLine 10s infinite linear;
}
.tech-lines div:nth-child(2) {
    top: 40%;
    animation: moveLine 15s infinite linear;
}
.tech-lines div:nth-child(3) {
    top: 60%;
    animation: moveLine 12s infinite linear;
}
.tech-lines div:nth-child(4) {
    top: 80%;
    animation: moveLine 8s infinite linear;
}
.tech-lines div:nth-child(5) {
    left: 30%;
    height: 100%;
    width: 1px;
    background: linear-gradient(180deg, transparent, rgba(43, 100, 167, 0.5) 30%, rgba(43, 100, 167, 0.5) 70%, transparent);
    animation: moveVertical 20s infinite linear;
}
.tech-lines div:nth-child(6) {
    left: 70%;
    height: 100%;
    width: 1px;
    background: linear-gradient(180deg, transparent, rgba(43, 100, 167, 0.5) 30%, rgba(43, 100, 167, 0.5) 70%, transparent);
    animation: moveVertical 25s infinite linear;
}
/* 合作伙伴轮播样式修复 */
.partners-container .partner-item .partner-front img {
    width: auto;
    height: 100%;
}
/* 机房枢纽样式 - 科技感背景 */
.indexDataCenter {
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #051428 0%, #0a2645 50%, #051428 100%);
    position: relative;
}
/* 添加地图浮动效果 */
.china-map {
    position: relative;
    width: 60%;
    margin-right: 5%;
    transition: transform 0.5s ease; /* 添加过渡效果使之平滑 */
}
.china-map-img {
    width: 100%;
    height: auto;
    display: block;
}
/* 科技感背景元素 */
.indexDataCenter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
	background-image: radial-gradient(circle at 20% 30%, rgba(0, 162, 255, 0.15) 0%, rgba(0, 162, 255, 0) 50%), radial-gradient(circle at 80% 70%, rgba(0, 247, 255, 0.15) 0%, rgba(0, 247, 255, 0) 50%);
    pointer-events: none;
    z-index: 1;
}
/* 数字电路线条效果 */
.indexDataCenter::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
	background-image: linear-gradient(90deg, transparent 49.5%, rgba(0, 162, 255, 0.15) 49.5%, rgba(0, 162, 255, 0.15) 50.5%, transparent 50.5%), linear-gradient(0deg, transparent 49.5%, rgba(0, 162, 255, 0.1) 49.5%, rgba(0, 162, 255, 0.1) 50.5%, transparent 50.5%);
    background-size: 60px 60px;
    pointer-events: none;
    opacity: 0.4;
    z-index: 1;
}

/* 动态扫描线效果 */
@keyframes techScanLine {
0% {
transform: translateY(-100%);
opacity: 0;
}
50% {
opacity: 0.5;
}
100% {
transform: translateY(100%);
opacity: 0;
}
}
.indexDataCenter .tech-scanline {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(0, 162, 255, 0.5), transparent);
    opacity: 0;
    z-index: 2;
    pointer-events: none;
    animation: techScanLine 8s linear infinite;
}
/* 科技感光点效果 */
.indexDataCenter .tech-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;
}
.indexDataCenter .particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background-color: rgba(0, 162, 255, 0.7);
    border-radius: 50%;
    box-shadow: 0 0 4px rgba(0, 162, 255, 0.7);
    pointer-events: none;
}
.datacenter-map-container {
    position: relative;
    width: 100%;
    margin-top: 40px;
    z-index: 2;
    background: rgba(10, 20, 30, 0.3);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}
.map-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}
.china-map {
    position: relative;
    width: 60%;
    margin-right: 5%;
}
.china-map-img {
    width: 100%;
    height: auto;
    display: block;
}
.map-point {
    position: absolute;
    width: 12px;
    height: 12px;
    cursor: pointer;
    z-index: 3;
}
/* 确保名称标签不会覆盖信息框 */
.map-point {
    --after-visibility: visible;
    --after-opacity: 1;
    --after-z-index: 5;
    --after-display: block;
    transition: opacity 0.3s ease, transform 0.3s ease;
}
/* 添加隐藏其他点的样式 */
.map-point.other-hidden {
    opacity: 0.2;
    pointer-events: none;
    transform: scale(0.8);
}
.map-point::after {
    content: attr(data-location);
    position: absolute;
    white-space: nowrap;
    font-size: 12px;
    color: #FF8000;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
    pointer-events: none;
    left: 12px;  /* 固定距离 */
    top: 50%;    /* 垂直居中 */
    transform: translateY(-50%); /* 确保垂直居中 */
    z-index: var(--after-z-index, 5);  /* 使用变量控制z-index */
    transition: opacity 0.3s, visibility 0.3s;  /* 添加过渡效果包括visibility */
    opacity: var(--after-opacity, 1);
    visibility: var(--after-visibility, visible);
    display: var(--after-display, block);
}
/* 鼠标悬停时立即完全隐藏名称标签 */
.map-point:hover {
    --after-opacity: 0 !important;
    --after-visibility: hidden !important;
    --after-display: none !important;
    --after-z-index: -10 !important;
}
.map-point:hover::after {
    opacity: 0 !important;
    visibility: hidden !important;
    display: none !important; /* 添加多重保险 */
    z-index: -10 !important;
}
/* 当信息框可见时立即完全隐藏名称标签 */
.map-point.info-visible {
    --after-opacity: 0 !important;
    --after-visibility: hidden !important;
    --after-display: none !important;
    --after-z-index: -10 !important;
}
.map-point.info-visible::after {
    opacity: 0 !important;
    visibility: hidden !important;
    display: none !important; /* 添加多重保险 */
    z-index: -10 !important;
}
.point-dot {
    position: absolute;
    width: 7.2px;  /* 原来12px的60% */
    height: 7.2px; /* 原来12px的60% */
    background-color: #FF8000;
    border-radius: 50%;
    z-index: 2;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transform-origin: center center;
}
.point-pulse {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 128, 0, 0.6);
    z-index: 1;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
}
.pulse1 {
    width: 18px;  /* 原来30px的60% */
    height: 18px; /* 原来30px的60% */
    animation: pulse 2s infinite;
}
.pulse2 {
    width: 27px;  /* 原来45px的60% */
    height: 27px; /* 原来45px的60% */
    animation: pulse 2s infinite 0.5s;
}
@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0.8;
    }
    70% {
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}
.point-info {
    position: absolute;
    width: 220px;
    background: rgba(0, 20, 45, 0.7);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: 10px;
    padding: 15px;
    color: white;
    font-size: 14px;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
    z-index: 9999;
    pointer-events: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(0, 162, 255, 0.3);
    text-align: left;
    transform: translateY(10px);
    /* 科技感边框效果 */
    overflow: hidden;
    position: relative;
}
.point-info::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid transparent;
    border-radius: 10px;
    background-image: linear-gradient(45deg, #00a2ff, #00f7ff, #00a2ff);
    background-size: 400% 400%;
    background-position: 0 0;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    animation: borderAnimation 3s linear infinite;
    opacity: 0.7;
    z-index: -1;
}
.point-info::after {
    content: '';
    position: absolute;
    width: 150%;
    height: 10px;
    background: rgba(0, 162, 255, 0.5);
    left: -25%;
    top: 45%;
    transform: rotate(-45deg);
    animation: scanline 2s linear infinite;
    filter: blur(3px);
    opacity: 0.5;
}
.point-info h4 {
    margin: 0 0 10px;
    font-size: 16px;
    color: #00f7ff;
    border-bottom: 1px solid rgba(0, 162, 255, 0.3);
    padding-bottom: 6px;
    font-weight: 500;
    text-shadow: 0 0 5px rgba(0, 247, 255, 0.5);
}
.point-info p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
    opacity: 0.9;
    position: relative;
}
/* 数据区 添加小点统计数字 */
.point-info .stats {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    border-top: 1px solid rgba(0, 162, 255, 0.2);
    padding-top: 10px;
}
.point-info .stat-item {
    text-align: center;
    width: 33%;
}
.point-info .stat-number {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #00f7ff;
    position: relative;
    display: inline-block;
}
.point-info .stat-label {
    font-size: 12px;
    opacity: 0.8;
}
/* 数字计数动画 */
.point-info .stat-number.counting {
    counter-reset: num 0;
    animation: counting 2s forwards ease-out;
}
.point-info .stat-number.counting::after {
    content: counter(num);
}
.point-info .stat-number.percent::after {
    content: counter(num) '%';
}
.point-info .stat-number.plus::after {
    content: counter(num) '+';
}

/* 动画关键帧 */
@keyframes borderAnimation {
0% {
background-position: 0 0;
}
100% {
background-position: 400% 0;
}
}
@keyframes scanline {
0% {
top: -10%;
opacity: 0.1;
}
50% {
opacity: 0.5;
}
100% {
top: 100%;
opacity: 0.1;
}
}
@keyframes counting {
from {
counter-increment: num 0;
}
to {
counter-increment: num var(--target-num);
}
}
.map-point:hover .point-info {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}
/* 根据类名决定信息框位置，替换之前的奇偶选择器 */
.map-point.info-left .point-info {
    right: 30px;
    top: -80px;
}
.map-point.info-right .point-info {
    left: 30px;
    top: -80px;
}
/* 移除或注释掉原来的奇偶选择器样式 */
/*
.map-point:nth-child(odd) .point-info {
    left: 30px;
    top: -80px;
}

.map-point:nth-child(even) .point-info {
    right: 30px;
    top: -80px;
}
*/

.datacenter-info {
    width: 35%;
    color: white;
    position: relative;
    z-index: 2;
    padding: 35px;
    background: rgba(5, 15, 25, 0.6); /* 更透明的背景 */
    border-radius: 15px;
    border: 1px solid rgba(0, 162, 255, 0.5);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    box-shadow: 0 0 40px rgba(0, 162, 255, 0.2), inset 0 0 20px rgba(0, 162, 255, 0.05);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transform: translateY(0);
}
/* 添加发光边框动画 */
.datacenter-info::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid transparent;
    border-radius: 15px;
    background-image: linear-gradient(45deg, #00a2ff, #00f7ff, #0057ff, #00a2ff);
    background-size: 400% 400%;
    background-position: 0 0;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    animation: datacenterBorder 6s linear infinite;
    opacity: 0.7;
    z-index: -1;
}
/* 添加扫描线效果 */
.datacenter-info::after {
    content: '';
    position: absolute;
    width: 200%;
    height: 200px;
    background: linear-gradient(90deg, transparent, rgba(0, 162, 255, 0.1), rgba(0, 247, 255, 0.2), rgba(0, 162, 255, 0.1), transparent);
    left: -50%;
    top: -50%;
    transform: rotate(45deg);
    animation: datacenterScan 6s linear infinite;
    animation-delay: 1s;
}
/* 数据中心卡片悬停效果 */
.datacenter-info:hover {
    box-shadow: 0 0 30px rgba(0, 162, 255, 0.25), inset 0 0 20px rgba(0, 162, 255, 0.05);
    transform: translateY(-5px);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}
/* 科技网格背景 */
.tech-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
	background-image: linear-gradient(rgba(0, 162, 255, 0.07) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 162, 255, 0.07) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: -1;
    opacity: 0.6;
    pointer-events: none;
}
/* 增加一些小圆点交叉点 */
.tech-grid::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(rgba(0, 247, 255, 0.3) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 10px 10px;
    opacity: 0.2;
}
/* 添加数据点连接到卡片的数据线 */
.datacenter-info::before, .datacenter-info::after {
    pointer-events: none;
}
@keyframes datacenterBorder {
0% {
background-position: 0 0;
}
100% {
background-position: 400% 0;
}
}
@keyframes datacenterScan {
0% {
top: -50%;
opacity: 0;
}
50% {
opacity: 0.5;
}
100% {
top: 150%;
opacity: 0;
}
}
.info-title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 25px;
    color: #00f7ff;
    text-align: center;
    text-shadow: 0 0 12px rgba(0, 247, 255, 0.6);
    position: relative;
    letter-spacing: 2px;
}
.info-title::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, rgba(0, 162, 255, 0), rgba(0, 247, 255, 0.8), rgba(0, 162, 255, 0));
    bottom: -10px;
    left: calc(50% - 30px);
    box-shadow: 0 0 10px rgba(0, 247, 255, 0.8);
}
.info-content {
    font-size: 16px;
    line-height: 1.7;
    margin-bottom: 35px;
    color: rgba(255, 255, 255, 0.85);
    text-align: center;
    position: relative;
    padding: 0 10px;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}
.tech-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
    margin-top: 30px;
    background: rgba(0, 20, 45, 0.4);
    padding: 25px 10px 20px;
    border-radius: 10px;
    border: 1px solid rgba(0, 162, 255, 0.2);
    position: relative;
    box-shadow: inset 0 0 15px rgba(0, 88, 138, 0.3);
}
.tech-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, rgba(0, 162, 255, 0), rgba(0, 247, 255, 0.6), rgba(0, 162, 255, 0));
    opacity: 0.7;
}
.stat-number {
    font-size: 36px;
    font-weight: 600;
    color: #00f7ff;
    margin-bottom: 8px;
    text-shadow: 0 0 10px rgba(0, 247, 255, 0.6);
    position: relative;
    display: inline-block;
    counter-reset: num var(--num, 0);
}
/* 移除CSS计数器动画，完全由JS实现 */
.stat-number {
    transition: all 0.3s;
}
.stat-label {
    font-size: 15px;
    color: rgba(168, 198, 224, 0.85);
    position: relative;
    padding-bottom: 3px;
}
.stat-label::after {
    content: '';
    position: absolute;
    width: 15px;
    height: 1px;
    background: rgba(0, 162, 255, 0.5);
    bottom: 0;
    left: calc(50% - 7.5px);
    box-shadow: 0 0 5px rgba(0, 162, 255, 0.5);
}

@media screen and (max-width: 1200px) {
    .map-wrapper {
        flex-direction: column;
    }
    .china-map, .datacenter-info {
        width: 90%;
        margin: 0 auto 30px;
    }
}

@media screen and (max-width: 768px) {
    .point-info {
        width: 150px;
    }
    /* 替换原有的奇偶选择器 */
.map-point.info-left .point-info, .map-point.info-right .point-info {
        left: -75px;
        top: -120px;
    }
}

/* 合作伙伴轮播样式修复 */
/* ... existing code ... */

/* 修正：当信息框显示时强制隐藏名称标签 */
.map-point:has(.point-info[style*="visibility: visible"]):after, .map-point:has(.point-info[style*="opacity: 1"]):after {
    opacity: 0 !important;
    visibility: hidden !important;
    display: none !important;
}
/* 兼容性写法：使用info-visible类确保所有浏览器都能隐藏标签 */
.map-point.info-visible::after {
    opacity: 0 !important;
    visibility: hidden !important;
    display: none !important;
    z-index: -1 !important;
}
/* 信息框显示时，强制设置其z-index高于所有元素 */
.map-point .point-info[style*="visibility: visible"], .map-point .point-info[style*="opacity: 1"] {
    z-index: 9999 !important;
    pointer-events: auto !important;
}
</style>
</head>
<body class="animated home"  >
<div class="audios"></div>
  <header class="header"> <a href="/" class="logo"> <img class="hide" src="static/picture/logo.png" alt="" /> <img class="show" src="static/picture/logo2.png" alt="" /> </a>
            <ul class="navs">
                <li class=""><a href="/">首页</a></li>
      <li ><a href="page-abouts.html">关于我们<i class="iconfont v">&#xe6b9;</i></a>
                    <div class="navs-menus-box">
                        <ol class="navs-menus">
            <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业使命与愿景<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业文化<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">团队介绍<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">投资者关系<i class="iconfont">&#xe631;</i></a></li>
                        </ol>
                    </div>
                </li>
      <li ><a href="products-services.html">产品与服务<i class="iconfont v">&#xe6b9;</i></a>
                    <div class="navs-menus-box">
                        <ol class="navs-menus">
            <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe631;</i></a></li>
                        </ol>
                    </div>
                </li>
      <li ><a href="solution-cases.html">解决方案<i class="iconfont v">&#xe6b9;</i></a>
                    <div class="navs-menus-box">
                        <ol class="navs-menus">
            <li><a href="#">电竞云网吧<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">AI眼镜<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业数字化转型<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">科研机构高性能计算<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">数据分析与大数据处理<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">媒体内容制作与渲染<i class="iconfont">&#xe631;</i></a></li>
                        </ol>
                    </div>
                </li>
      <li ><a href="hyyy-hyyy.html">成功案例<i class="iconfont v">&#xe6b9;</i></a>
                    <div class="navs-menus-box">
                        <ol class="navs-menus">
            <li><a href="#">互联网行业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">人工智能<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">制造业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">金融服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教育与科研<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">政府与公共服务<i class="iconfont">&#xe631;</i></a></li>
                        </ol>
                    </div>
                </li>
      <li ><a href="#">技术与创新<i class="iconfont v">&#xe6b9;</i></a>
                    <div class="navs-menus-box">
                        <ol class="navs-menus">
            <li><a href="#">技术白皮书<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">研发团队<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">专利与知识产权<i class="iconfont">&#xe631;</i></a></li>
                        </ol>
                    </div>
                </li>
      <li ><a href="#">合作与生态<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">合作伙伴<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">生态体系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">合作模式<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">资源中心<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">产品手册<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教程与指南<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">行业洞察<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">算力知识库<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li><a href="contact-contacts.html">联系我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">销售咨询<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">技术支持<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">媒体联系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">办公地点<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
            </ul>
            <div class="header-menu">
      <div class="header-search"> <span class="iconfont">&nbsp;</span> </div>
                <div class="menu-btn">
                    <p>MENU</p>
        <div class="menubtn"> <span></span> </div>
                </div>
                <div class="menu-flex">
                    <div class="menu-bg"></div>
                    <div class="menu-right">
                        <ul class="menu-list">
                    <li><a href="/">首页</a></li>
            <li ><a href="page-abouts.html">关于我们<em></em></a>
                        <ol class="menu-leval">
                <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业使命与愿景<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业文化<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">团队介绍<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">投资者关系<i class="iconfont">&#xe624;</i></a></li>
                        </ol>
                    </li>
            <li ><a href="products-services.html">产品与服务<em></em></a>
                        <ol class="menu-leval">
                <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe624;</i></a></li>
                        </ol>
                    </li>
            <li ><a href="solution-cases.html">解决方案<em></em></a>
                        <ol class="menu-leval">
                <li><a href="#">电竞云网吧<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">AI眼镜<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业数字化转型<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">科研机构高性能计算<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">数据分析与大数据处理<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">媒体内容制作与渲染<i class="iconfont">&#xe624;</i></a></li>
                        </ol>
                    </li>
            <li ><a href="hyyy-hyyy.html">成功案例<em></em></a>
                        <ol class="menu-leval">
                <li><a href="#">互联网行业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">人工智能<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">制造业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">金融服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教育与科研<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">政府与公共服务<i class="iconfont">&#xe624;</i></a></li>
                        </ol>
                    </li>
            <li ><a href="#">技术与创新<em></em></a>
                        <ol class="menu-leval">
                <li><a href="#">技术白皮书<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">研发团队<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">专利与知识产权<i class="iconfont">&#xe624;</i></a></li>
                        </ol>
                    </li>
            <li ><a href="#">合作与生态<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">合作伙伴<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">生态体系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">合作模式<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">资源中心<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">产品手册<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教程与指南<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">行业洞察<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">算力知识库<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="contact-contacts.html">联系我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">销售咨询<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">技术支持<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">媒体联系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">办公地点<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
                </ul>
                        <div class="menu-ip-down">
            <div class="online-shopp"> </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
  <div class="main-content-wrap">
    <div class="content-wrap">
      <div class="head-search">
     <form action="search-search.html" method="post">
          <input class="search-ipt" type="text" name="query" placeholder="输入关键词...">
          <input class="search-btn" type="submit" name="submit" value="搜索" title="搜索">
     </form>
      </div>
    </div>
  </div>
<div class="mobile-body-mask"></div>

<!--content-->
<div id="content" class="clearfix">
      
    <!--ind-banner-->
  <div class="ind-banner">
        <div class="swiper-container2">
            <div class="swiper-wrapper2">
          <div class="swiper-slide">
            <div class="img">
               <div class="shipin changeImgBg js-fullheight" style="background: url(static/images/banner.jpg) center center / cover;">
                <video type="video/mp4; codecs=" avc1.4d401e,mp4a.40.0""="" muted="" autoplay loop preload="auto" poster="static/images/banner.jpg" id="video" data-setup="{'autoplay':true}" webkit-playsinline="true" x-webkit-airplay="true" playsinline="true" x5-video-player-type="h5" x5-video-player-fullscreen="true" width="100%" height="100%">
                  <source autoplay="" type="video/mp4; codecs=" avc1.4d401e,mp4a.40.0""="" src="https://video.eczone.net/eczone/f0.mp4">
                </video>
                   </div>
              <img src="static/picture/banner-wz.png" alt="">
              <div class="bannershadow"></div>
            </div>
          </div>
            </div>
        <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>
            <div class="swiper-pagination"></div>
            <div class="swiper-btndown"><a href="#indexContent" class="btndown"><img src="static/picture/scroll.png" alt=""></a></div>
        </div>
    </div>
    <!--ind-banner end-->
    <div class="indexContent clearfix" id="indexContent">

<!--indexAbout-->
      <div class="indexAbout clearfix">
        <div class="w1400 clearfix">
          <div class="AboutBox clearfix">
            <div class="AboutBoxNr clearfix">
              <div class="pageTit  wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s"><span class="en">Who we are</span>
                <div class="cn wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.5s"><span class="syht">关于我们</span></div>
              </div>
                <div class="titleWzBox">
                    <div class="wz wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.6s">
                  <p> 广州聚算云科技有限公司扎根粤港澳大湾区，以"让算力如水般自由流动"为使命，正在重写算力资源分配的规则。

                        在这个算力决定一切的时代，我们看到一个反常现象：大量算力资源被闲置，而无数创新项目却因算力不足而受阻。聚算云选择了一条不同寻常的路径——不做挖金者，只为数字淘金客提供最好的"铁锹"。
                        
                        我们打造了一个连接算力供需双方的智能平台，让普通人也能参与AI基建红利，让企业以低成本获取高性能算力。从电竞云网吧到智能眼镜，我们已帮助多个行业客户实现了技术创新与成本优化的双赢。
                        
                        聚算云独创的"三横三纵"生态运营模式，横向连接设备供应商、数据中心与终端客户，纵向打通算力调度、运维保障与增值服务，实现分钟级资源匹配，助力客户显著提升算力获取效率，降低综合使用成本。
                        
                    我们坚信，算力服务的终极形态并非资源垄断，而是智慧连接。聚算云正以生态枢纽的角色，秉承"共享、普惠、可持续"的核心理念，让每一份算力资源在需求之地绽放价值，让每一次智能转型都始于轻装简行。 </p>
                    </div>
                    <div class="btn-view wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s"><a href="page-abouts.html" class="cube"><i>■ ■ ■</i><span>了解更多</span></a></div>
                </div>
            </div>
          </div>
        </div>
      </div>

<!--indexCase--> 
<div class="indexCase">   
<div class="indexCaseBox">     
     <div class="indexCaseInfo clearfix">       
               <div class="indexCaselist clearfix" >
                    <div class="item clearfix swiper-container">
                    <ul class="list clearfix swiper-wrapper">
                  <li class="li swiper-slide"><a href="#"  target='_blank' ><img class="img" src="static/picture/index-case1.jpg" alt="电竞云网吧"  />
                    <div class="text">
                      <div class="textBox">
                        <div class="t1">电竞云网吧<span class="en">Cloud Gaming</span></div>
                        <div class="wz">让普通终端设备摇身一变成为高性能游戏平台，网吧降低硬件投入，玩家获得更流畅的游戏体验。</div>
    <div class="line"></div>
                      </div>
                    </div>
                               </a></li>
                  <li class="li swiper-slide"><a href="#"  target='_blank' ><img class="img" src="static/picture/index-case2.jpg" alt="AI眼镜"  />
                    <div class="text">
                      <div class="textBox">
                        <div class="t1">AI眼镜<span class="en">Smart Glasses</span></div>
                        <div class="wz">眼镜只负责数据采集和显示，复杂AI运算由云端完成，保持轻便形态，实现强大功能，续航显著提升。</div>
    <div class="line"></div>
                      </div>
                    </div>
                               </a></li>
                  <li class="li swiper-slide"><a href="#"  target='_blank' ><img class="img" src="static/picture/index-case3.jpg" alt="企业数字化转型"  />
                    <div class="text">
                      <div class="textBox">
                        <div class="t1">企业数字化<span class="en">Digital Transformation</span></div>
                        <div class="wz">为企业提供按需算力服务，帮助中小企业以低成本实现AI和大数据应用，加速数字化转型。</div>
    <div class="line"></div>
                      </div>
                    </div>
                               </a></li>
                  <li class="li swiper-slide"><a href="#"  target='_blank' ><img class="img" src="static/picture/index-case4.jpg" alt="科研机构高性能计算"  />
                    <div class="text">
                      <div class="textBox">
                        <div class="t1">科研计算<span class="en">Scientific Computing</span></div>
                        <div class="wz">为科研机构提供高性能计算资源，支持复杂模拟和数据分析，加速科研突破与创新。</div>
    <div class="line"></div>
                      </div>
                    </div>
                               </a></li>
                  <li class="li swiper-slide"><a href="#"  target='_blank' ><img class="img" src="static/picture/index-case6.jpg" alt="数据分析与大数据处理"  />
                    <div class="text">
                      <div class="textBox">
                        <div class="t1">大数据分析<span class="en">Big Data Analytics</span></div>
                        <div class="wz">提供集群式计算资源和分析工具，助力企业从海量数据中挖掘价值，实现精准决策和业务增长。</div>
    <div class="line"></div>
                      </div>
                    </div>
                               </a></li>
                  <li class="li swiper-slide"><a href="#"  target='_blank' ><img class="img" src="static/picture/index-case5.jpg" alt="媒体内容制作与渲染"  />
                    <div class="text">
                      <div class="textBox">
                        <div class="t1">媒体渲染<span class="en">Media Rendering</span></div>
                        <div class="wz">提供高效渲染算力，帮助媒体内容创作者快速完成图像和视频处理，降低设备成本，提高制作效率。</div>
                        <div class="line"></div>
                      </div>
                    </div>
                    </a></li>
                    </ul>
                <!-- 添加导航按钮 -->
                <div class="swiper-button-prev solution-prev"></div>
                <div class="swiper-button-next solution-next"></div>
                    </div>
              <div class="itemBg" style="background-image:url(static/picture/index-case1.jpg)"></div>
              </div> 
     </div>
</div>                                  
</div>
        
<!--indexFangan-->
<div class="indexFangan clearfix">
    <div class="indexFanganBox clearfix">
        <div class="wbox1">
            <div class="module-hotsale">
              <div class="pageTit"><span class="en">Solution</span>
                <div class="cn"><span class="syht">解决方案</span></div>
              </div>
                <div class="index-products-swiper-container1">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide stop-swiping">
                    <div class="box"> <a href="javascript:void(0);" >
                      <div class="title syht">算力调度<br>
                        平台</div>
                      </a>
                            <div class="bjb">
                        <p> 我们独创的"三横三纵"生态运营模式，横向连接设备供应商、数据中心与终端客户，纵向打通算力调度、运维保障与增值服务，实现分钟级资源匹配。 </p>
                            </div>
                            <div class="btn-view"><a href="javascript:void(0);"  class="cube"><i>■ ■ ■</i><span>了解更多</span></a></div>
                        </div>
                    </div>
                    <div class="swiper-slide stop-swiping">
                    <div class="box"> <a href="javascript:void(0);" >
                      <div class="title syht">铁锹革命</div>
                      </a>
                            <div class="bjb">
                        <p> 普通人也能参与AI基建红利。投资者只需购买标准化算力设备，委托我们全流程托管，即可获得稳定收益，远超传统投资渠道。 </p>
                            </div>
                            <div class="btn-view"><a href="javascript:void(0);"  class="cube"><i>■ ■ ■</i><span>了解更多</span></a></div>
                        </div>
                    </div>
                    <div class="swiper-slide stop-swiping">
                    <div class="box"> <a href="javascript:void(0);" >
                      <div class="title syht">算力共享<br/>
                        网络</div>
                      </a>
                            <div class="bjb">
                        <p> 通过分布式算力网络，企业获得成本更低、更灵活的算力服务。我们的混合计费模式、全谱系设备覆盖和分钟级资源匹配，让企业不再为算力不足而延误项目。 </p>
                            </div>
                            <div class="btn-view"><a href="javascript:void(0);"  class="cube"><i>■ ■ ■</i><span>了解更多</span></a></div>
                        </div>
                    </div>
                  </div>
                </div>
            </div>
        </div>
        <div class="wbox2">
            <div class="module-hotsaleImg">
                <div class="index-products-swiper-container2">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide stop-swiping">
                    <div class="box"> <a href="solution-1805-1809.html"  target='_blank'><img src="static/picture/index-fangan1.jpg" alt="算力调度" /></a> </div>
                        </div>
                    <div class="swiper-slide stop-swiping">
                    <div class="box"> <a href="solution-1805-1810.html"  target='_blank'><img src="static/picture/index-fangan2.jpg" alt="铁锹革命" /></a> </div>
                        </div>
                    <div class="swiper-slide stop-swiping">
                    <div class="box"> <a href="solution-1805-1811.html"  target='_blank'><img src="static/picture/index-fangan3.jpg" alt="算力共享" /></a> </div>
                        </div>
                  </div>
                </div>
            </div>
        </div>
    </div>
   <div class="wbox3 clearfix">
          <div class="module-hotsaleDian">
            <div class="swiper-pagination"></div>
   </div>
</div>
      </div>
     </div>
</div>

<!-- 机房枢纽 Data Center Hub -->
<div class="indexDataCenter clearfix">
    <div class="tech-scanline"></div>
    <div class="tech-particles" id="techParticles"></div>
    <div class="w1400 clearfix">
      <div class="pageTit wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s"> <span class="en">Data Center Hub</span>
        <div class="cn wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.5s"> <span class="syht">机房枢纽</span> </div>
        </div>
        <div class="datacenter-map-container">
            <div class="map-wrapper">
          <div class="china-map" id="chinaMapContainer"> <img src="static/images/china-map.png" alt="中国地图" class="china-map-img" id="chinaMapImage"> 
                    <!-- 地图点将通过JavaScript动态添加 -->
                </div>
                <div class="datacenter-info">
                    <div class="tech-grid"></div>
                    <div class="info-title">全国机房分布</div>
            <div class="info-content"> 我们在全国建立了八大数据中心枢纽，构建了覆盖全国的高性能计算网络，为客户提供低延迟、高可靠的云计算和数据处理服务。 </div>
                    <div class="tech-stats">
                        <div class="stat-item">
                            <div class="stat-number">8</div>
                            <div class="stat-label">数据中心</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">99.99%</div>
                            <div class="stat-label">服务可用性</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100+</div>
                            <div class="stat-label">PB级存储</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 合作伙伴 Partners -->
<div class="indexPartners clearfix">
    <div class="w1400 clearfix">
      <div class="pageTit wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s"> <span class="en">Partners</span>
        <div class="cn wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.5s"> <span class="syht">合作伙伴</span> </div>
        </div>
        <div class="partners-tech-container">
            <div class="partners-grid">
                <div class="partner-item wow fadeInUp" data-wow-duration="0.8s" data-wow-delay="0.1s">
                    <div class="partner-inner">
                        <div class="partner-front">
                <div class="partner-logo"> <img src="static/images/index-youshi-img1.jpg" alt="合作伙伴1"> </div>
                        </div>
                    </div>
                </div>
                <div class="partner-item wow fadeInUp" data-wow-duration="0.8s" data-wow-delay="0.2s">
                    <div class="partner-inner">
                        <div class="partner-front">
                <div class="partner-logo"> <img src="static/images/adv-video.jpg" alt="合作伙伴2"> </div>
                        </div>
                    </div>
                </div>
                <div class="partner-item wow fadeInUp" data-wow-duration="0.8s" data-wow-delay="0.3s">
                    <div class="partner-inner">
                        <div class="partner-front">
                <div class="partner-logo"> <img src="static/images/adv-bg.jpg" alt="合作伙伴3"> </div>
                        </div>
                    </div>
                </div>
                <div class="partner-item wow fadeInUp" data-wow-duration="0.8s" data-wow-delay="0.4s">
                    <div class="partner-inner">
                        <div class="partner-front">
                <div class="partner-logo"> <img src="static/images/page-ad.jpg" alt="合作伙伴4"> </div>
                        </div>
                    </div>
                </div>
                <div class="partner-item wow fadeInUp" data-wow-duration="0.8s" data-wow-delay="0.5s">
                    <div class="partner-inner">
                        <div class="partner-front">
                <div class="partner-logo"> <img src="static/images/banner.jpg" alt="合作伙伴5"> </div>
                        </div>
                    </div>
                </div>
                <div class="partner-item wow fadeInUp" data-wow-duration="0.8s" data-wow-delay="0.6s">
                    <div class="partner-inner">
                        <div class="partner-front">
                <div class="partner-logo"> <img src="static/images/index-case1.jpg" alt="合作伙伴6"> </div>
                        </div>
                    </div>
                </div>
                <div class="partner-item wow fadeInUp" data-wow-duration="0.8s" data-wow-delay="0.7s">
                    <div class="partner-inner">
                        <div class="partner-front">
                <div class="partner-logo"> <img src="static/images/adv1-tu.jpg" alt="合作伙伴7"> </div>
                        </div>
                    </div>
                </div>
                <div class="partner-item wow fadeInUp" data-wow-duration="0.8s" data-wow-delay="0.8s">
                    <div class="partner-inner">
                        <div class="partner-front">
                <div class="partner-logo"> <img src="static/images/2024082631370357.jpg" alt="合作伙伴8"> </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新闻中心 News Center -->
<div class="indexNews clearfix">
    <div class="w1400 clearfix">
      <div class="pageTit wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s"> <span class="en">News</span>
        <div class="cn wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.5s"> <span class="syht">新闻中心</span> </div>
        </div>
        <div class="newsList clearfix">
            <div class="news-container">
                <div class="news-image-container">
                    <div id="newsImage" class="news-image" style="background-image: url('static/picture/index-case1.jpg');"></div>
                    <div id="newsContent" class="news-content">
              <div class="pageTit2">
                <div class="en">News Detail</div>
                <div class="cn"><span class="syht">聚算云发布"算力共享网络"构建算力平民化新生态</span></div>
              </div>
              <div class="news-date">2023-09-15</div>
              <div class="news-desc">聚算云创始人在发布会上表示：当算力决定一切的时代，我们看到一个反常现象：大量算力资源被闲置，而无数创新项目却因算力不足而受阻。聚算云选择了一条不同寻常的路径——不做挖金者，只为数字淘金客提供最好的"铁锹"...</div>
              <a href="javascript:void(0);" class="theme-btn">查看详情</a> </div>
                </div>
                <div class="news-items-container">
            <div class="news-item active" data-image="static/picture/index-case1.jpg" data-title="聚算云发布"算力共享网络"构建算力平民化新生态" data-date="2023-09-15" data-desc="聚算云创始人在发布会上表示：当算力决定一切的时代，我们看到一个反常现象：大量算力资源被闲置，而无数创新项目却因算力不足而受阻。聚算云选择了一条不同寻常的路径——不做挖金者，只为数字淘金客提供最好的"铁锹"...">
              <div class="item-title">聚算云发布"算力共享网络"构建算力平民化新生态</div>
              <div class="item-date">2023-09-15</div>
                    </div>
            <div class="news-item" data-image="static/picture/index-case2.jpg" data-title="聚算云助力电竞云网吧落地，普通网吧摇身变身高端游戏平台" data-date="2023-07-20" data-desc="聚算云日前成功为广州某连锁网吧运营商提供电竞云网吧解决方案，彻底改变了传统网吧面临的硬件投入高、升级成本大的困境。通过接入聚算云的分布式算力网络，普通终端设备摇身一变成为高性能游戏平台...">
              <div class="item-title">聚算云助力电竞云网吧落地，普通网吧摇身变身高端游戏平台</div>
              <div class="item-date">2023-07-20</div>
                    </div>
            <div class="news-item" data-image="static/picture/index-case3.jpg" data-title="聚算云与多家数据中心达成战略合作，算力网络覆盖再扩大" data-date="2023-05-28" data-desc="聚算云近日宣布与华东、华南多家数据中心达成战略合作，进一步扩大算力网络覆盖范围。聚算云CEO表示，算力平民化战略的核心在于打造一个无处不在的算力网络，让企业和开发者能随时随地获取所需的计算资源...">
              <div class="item-title">聚算云与多家数据中心达成战略合作，算力网络覆盖再扩大</div>
              <div class="item-date">2023-05-28</div>
                    </div>
            <div class="news-item" data-image="static/picture/index-case4.jpg" data-title="聚算云完成A轮融资，加速推进'算力如水'战略" data-date="2023-04-10" data-desc="聚算云正式宣布完成A轮融资，投资方包括多家知名机构。本轮融资将主要用于扩建算力网络基础设施、优化智能调度系统以及构建更加完善的算力服务生态。聚算云创始人表示，本轮融资是资本市场对'算力民主化'理念的认可...">
              <div class="item-title">聚算云完成A轮融资，加速推进"算力如水"战略</div>
              <div class="item-date">2023-04-10</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--footer-->   
<div class="footer clearfix">
    <!--footer-nav-->
    <div class="footer-nav">
    <div class="w1600 clearfix">
        <div class="left">
          <div class="leftBox clearfix">
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="#">企业信息</a></div>
                     <div class="listB"> 
                     <ul class="ulB">
                    <li class="t2"><a href="page-1751-1752.html">公司简介</a></li>
                    <li class="t2"><a href="contact-contacts.html">联系方式</a></li>
                    <li class="t2"><a href="#">办公地址</a></li>
                     </ul>
                     </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="#">快速链接</a></div>
                     <div class="listB"> 
                     <ul class="ulB">
                    <li class="t2"><a href="page-abouts.html">关于我们</a></li>
                    <li class="t2"><a href="#">产品服务</a></li>
                    <li class="t2"><a href="solution-cases.html">解决方案</a></li>
                    <li class="t2"><a href="hyyy-hyyy.html">成功案例</a></li>
                     </ul>
                     </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="#">资源链接</a></div>
                     <div class="listB">     
                     <ul class="ulB">
                    <li class="t2"><a href="#">资源下载</a></li>
                    <li class="t2"><a href="#">技术支持</a></li>
                    <li class="t2"><a href="#">常见问题</a></li>
                    <li class="t2"><a href="#">使用指南</a></li>
                     </ul>
                     </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="#">法律信息</a></div>
                     <div class="listB"> 
                     <ul class="ulB">
                    <li class="t2"><a href="#">用户协议</a></li>
                    <li class="t2"><a href="#">隐私政策</a></li>
                    <li class="t2"><a href="#">知识产权声明</a></li>
                    <li class="t2"><a href="#">免责声明</a></li>
                     </ul>
                     </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="rightBox clearfix">
<table width="100%">
	<tbody>
		<tr>
                  <td><h1> 服务热线 </h1></td>
		</tr>
		<tr>
                  <td class="tel"><h1> <span>+86 13800138000</span> </h1>
                    <p> 诚挚为您服务 </p></td>
		</tr>
	</tbody>
</table>
            <div class="bottom-share"> <span>关注我们：</span> <a class="iconfont email" href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">&#xe6db;</a> <a class="iconfont qq" href="http://wpa.qq.com/msgrd?v=3&uin=395202420&site=kingtin&menu=yes" target="_blank" rel="noopener noreferrer">&#xe60f;</a> <a class="iconfont weix"  rel="noopener noreferrer">&#xe660;<span class="img"><img src="static/picture/202203011551003232.jpg" alt="" /><br />
              </span></a> 
              </span></a> </div>
</div>
     </div>
     </div>
      <div class="bottom-wz">
        <div class="w1600 clearfix">聚算云，让算力如水般自由流动，为创新提供无限可能。</div>
            </div>
            </div> 
    <!--footer-nav end-->
    <div class="bq clearfix">
      <div class="w1600 clearfix">
        <div class="bqname"> &copy;2023 广州聚算云科技有限公司 版权所有 <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener">粤ICP备2022022663号</a> </div>
        <div class="beian"> 网站设计: <a href="http://www.eczone.net/" target="_blank" rel="noopener">ECZONE</a>
    </div>
    </div>
    </div>
</div>
<!--End footer-->     
<div class="common-bott">
    <div class="back-top commontop iconfont">&#xe731;</div>
</div>
</section>
<script type="text/javascript" src="static/js/swiper.min.js"></script>
<script type="text/javascript" src="static/js/wow.min2.js"></script>
<script type="text/javascript" src="static/js/plugin.js"></script>
<script type="text/javascript" src="static/js/page.js"></script>
<script>
//pcbanner
var my = new Swiper('.ind-banner .swiper-container', {				
		speed: 1500,
		//loop: true,
//		effect: 'fade',
//		autoplay: {
//			delay: 4000, //3秒切换一次
//			disableOnInteraction: false,
//		},
//		autoplayDisableOnInteraction: true,
		pagination: {
		  el: '.ind-banner .swiper-pagination',
		  clickable: true,
		},
		preventClicks : false,
		preventClicksPropagation: true,
        paginationClickable: true,

		navigation: {
			nextEl: '.ind-banner .swiper-button-next',
			prevEl: '.ind-banner .swiper-button-prev',
		}
	
});
//indexCase
var indexCase = new Swiper('.index-products-swiper-container1', {
	loop : true,
	noSwipingClass : 'stop-swiping',
})
//indexCaseimg
var indexCaseimg = new Swiper('.index-products-swiper-container2', {
	speed:700,
	loop : true,
	autoplay: {
		delay: 5000,
		stopOnLastSlide: false,
		disableOnInteraction: false,
	},
	//noSwipingClass : 'stop-swiping',
	controller: {
	    control: indexCase,
	},
	pagination:{
		el: '.swiper-pagination',
		bulletClass : 'my-bullet',
		bulletActiveClass: 'my-bullet-active',
		clickable :true,
	  },
})
//indexPro
var indexPro = new Swiper('.indexPro .Probox',{
	slidesPerView:5,
	spaceBetween:0,
	paginationClickable :true,
	preventClicks : false,//默认true
	speed:2000,
	loop :true,
	autoplay: {
		delay: 8000, //3秒切换一次
		disableOnInteraction: false,
	},
	autoplayDisableOnInteraction: true,
	navigation: {
		nextEl: '.indexPro .swiper-button-next',
		prevEl: '.indexPro .swiper-button-prev',
	},
	breakpoints: { 
		//当宽度小于等于320
		1024: {
		  slidesPerView:5,
		  spaceBetween:0
		},
		992: {
		  slidesPerView:3,
		},
		768: {
		  slidesPerView:2,
		}
	}
});

// 新闻中心轮播
var newsSwiper = new Swiper('.newsSwiper', {
    effect: 'fade',
    fadeEffect: {
        crossFade: true
    },
    loop: true,
    autoplay: {
        delay: 5000,
        disableOnInteraction: false,
    },
    pagination: {
        el: '.news-pagination',
        clickable: true,
    },
    on: {
        slideChange: function() {
            // 当轮播切换时，可以在这里添加额外的效果
        }
    }
});
</script>
<script type="text/javascript">
//indexCaselist
var galleryThumbs = new Swiper('.indexCaselist .item', {
	paginationClickable :true,
	preventClicks : false,//默认true
	spaceBetween: 0,
	slidesPerView: 5,
	speed:1000,
	freeMode: true,loop :false,
	watchSlidesProgress: true,
	navigation: {
		nextEl: '.solution-next',
		prevEl: '.solution-prev',
	},
	breakpoints: { 
       1024: {
		  slidesPerView:3,	
		freeMode: false,watchSlidesProgress: false,loop :true,
		},
		992: {
		  slidesPerView:2,
		freeMode: false,watchSlidesProgress: false,loop :true,
		},
        768: {
		  slidesPerView:1,
		freeMode: false,watchSlidesProgress: false,loop :true,
		}
		
		
		
	}
});
//indexCaselist li hover
$(".indexCaselist .item .li:first").addClass("swiper-slide-thumb-active");
$(".indexCaselist .item .li").hover(function(){
	  $(".indexCaselist .item").find(".li").removeClass("swiper-slide-thumb-active");
	  $(this).addClass("swiper-slide-thumb-active");
})

$('.indexCaselist .item .li').mouseover(function(){
	if($(this).find('.img').attr('src')){
		$(".itemBg").css({ 'background-image':'url(' + $(this).find('.img').attr('src') +')' }, 300);
	}
})

$(".indexYoushilist ul li").mouseover(function(){
	  $(".indexYoushilist ul").find("li").removeClass("on");
	  $(this).addClass("on");
})
</script>
<script>
// 添加合作伙伴图片加载和3D效果
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化合作伙伴区域效果');
    
    // 获取所有合作伙伴项
    const partnerItems = document.querySelectorAll('.partner-item');
    if (!partnerItems.length) {
        console.log('未找到合作伙伴元素');
        return;
    }
    
    console.log(`找到 ${partnerItems.length} 个合作伙伴卡片`);
    
    // 检查是否支持3D变换
    function supports3DTransforms() {
        var el = document.createElement('p'),
            has3d,
            transforms = {
                'webkitTransform': '-webkit-transform',
                'OTransform': '-o-transform',
                'msTransform': '-ms-transform',
                'MozTransform': '-moz-transform',
                'transform': 'transform'
            };
        
        document.body.appendChild(el);
        
        for (var t in transforms) {
            if (el.style[t] !== undefined) {
                el.style[t] = 'translate3d(1px,1px,1px)';
                has3d = window.getComputedStyle(el).getPropertyValue(transforms[t]);
            }
        }
        
        document.body.removeChild(el);
        return (has3d !== undefined && has3d.length > 0 && has3d !== "none");
    }
    
    // 检测3D支持
    const supports3D = supports3DTransforms();
    if (!supports3D) {
        console.warn('浏览器不完全支持3D变换，降级处理');
        document.querySelector('.partners-grid').classList.add('fallback-mode');
        
        // 添加降级CSS
        const fallbackCSS = document.createElement('style');
        fallbackCSS.textContent = `
            .fallback-mode .partner-item {
              perspective: none;
            }
            .fallback-mode .partner-inner {
              transition: opacity 0.5s ease;
            }
            .fallback-mode .partner-item:hover .partner-inner {
              transform: none;
            }
            .fallback-mode .partner-back {
              opacity: 0;
              transform: none;
              transition: opacity 0.5s ease;
            }
            .fallback-mode .partner-item:hover .partner-back {
              opacity: 1;
            }
            .fallback-mode .partner-item:hover .partner-front {
              opacity: 0;
            }
        `;
        document.head.appendChild(fallbackCSS);
        document.documentElement.classList.add('no-3d-support');
        return;
    }
    
    const partnerImages = document.querySelectorAll('.partner-logo img');
    
    // 图片加载处理
    partnerImages.forEach(img => {
        if (img.complete) {
            console.log('图片已加载:', img.src);
            img.closest('.partner-item').classList.add('image-loaded');
        } else {
            img.addEventListener('load', function() {
                console.log('合作伙伴图片加载成功:', this.src);
                this.closest('.partner-item').classList.add('image-loaded');
                this.classList.add('loaded');
            });
          
            img.addEventListener('error', function() {
                console.error('合作伙伴图片加载失败:', this.src);
                // 加载失败时使用默认图片
                this.src = 'static/images/banner-pagebig.jpg';
                this.closest('.partner-item').classList.add('image-error');
            });
        }
    });
    
    // 为每个合作伙伴卡片添加事件监听器
    partnerItems.forEach((item, index) => {
        const partnerInner = item.querySelector('.partner-inner');
        
        // 为每个项目添加动画效果，错开时间
        setTimeout(() => {
            item.classList.add('animated');
        }, index * 150);
        
        // 添加鼠标移动事件以创建3D效果
        item.addEventListener('mousemove', function(e) {
            if (window.innerWidth < 768) return; // 在移动设备上禁用鼠标移动效果
            
            const rect = item.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = e.clientX;
            const mouseY = e.clientY;
            
            // 计算鼠标位置相对于卡片中心的偏移
            const percentX = (mouseX - centerX) / (rect.width / 2);
            const percentY = (mouseY - centerY) / (rect.height / 2);
            
            // 限制倾斜角度
            const tiltX = Math.max(Math.min(percentY * 15, 15), -15);
            const tiltY = Math.max(Math.min(percentX * -15, 15), -15);
            
            // 应用3D变换
            partnerInner.style.transform = `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg) scale3d(1.02, 1.02, 1.02)`;
            partnerInner.style.transition = 'none'; // 移动时取消过渡以获得更平滑的效果
            
            // 添加光影效果
            const glarePosition = `${50 + percentX * 10}% ${50 + percentY * 10}%`;
            partnerInner.style.backgroundPosition = glarePosition;
        });
        
        // 添加鼠标离开事件以重置3D效果
        item.addEventListener('mouseleave', function() {
            partnerInner.style.transform = '';
            partnerInner.style.transition = 'transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            partnerInner.style.backgroundPosition = '50% 50%';
        });
        
        // 添加点击事件以在移动设备上翻转卡片
        item.addEventListener('click', function() {
            if (window.innerWidth < 768) {
                this.classList.toggle('flipped');
                
                // 关闭其他翻转的卡片
                partnerItems.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('flipped')) {
                        otherItem.classList.remove('flipped');
                    }
                });
            }
        });
    });
    
    // 添加窗口大小变化事件以重置效果
    window.addEventListener('resize', function() {
        partnerItems.forEach(item => {
            const partnerInner = item.querySelector('.partner-inner');
            partnerInner.style.transform = '';
            
            // 如果是桌面模式，取消所有翻转状态
            if (window.innerWidth >= 768) {
                item.classList.remove('flipped');
            }
        });
    });
    
    // 添加滚动动画
    if (typeof WOW !== 'undefined') {
        new WOW().init();
        console.log('WOW动画初始化成功');
    } else {
        console.warn('WOW.js未加载，将使用基本动画');
    }
});
</script>
<script>
// 新闻切换功能
document.addEventListener('DOMContentLoaded', function() {
    const newsItems = document.querySelectorAll('.news-item');
    const newsImage = document.getElementById('newsImage');
    const newsTitle = document.querySelector('.news-content .pageTit2 .cn span');
    const newsDate = document.querySelector('.news-content .news-date');
    const newsDesc = document.querySelector('.news-content .news-desc');
    
    newsItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            // 移除所有激活状态
            newsItems.forEach(i => i.classList.remove('active'));
            
            // 添加当前激活状态
            this.classList.add('active');
            
            // 更新图片和内容，添加淡入效果
            newsImage.style.opacity = '0';
            setTimeout(() => {
                newsImage.style.backgroundImage = `url('${this.dataset.image}')`;
                newsTitle.textContent = this.dataset.title;
                newsDate.textContent = this.dataset.date;
                newsDesc.textContent = this.dataset.desc;
                newsImage.style.opacity = '1';
            }, 300);
        });
    });
    
    // 初始化透明度过渡效果
    newsImage.style.transition = 'opacity 0.5s ease';
    newsImage.style.opacity = '1';
});
</script>
<script>
// 机房枢纽交互效果
document.addEventListener('DOMContentLoaded', function() {
    // 初始化科技感粒子效果
    initTechParticles();
    
    // 数据中心卡片数字动画 - 修复版
    function initNumberCounters() {
        console.log('初始化数字计数动画');
        
        // 直接选取固定区域的统计数字
        const datacenterStatsContainer = document.querySelector('.datacenter-info .tech-stats');
        if (!datacenterStatsContainer) {
            console.error('未找到数据中心统计容器');
            return;
        }
        
        const datacenterStats = datacenterStatsContainer.querySelectorAll('.stat-number');
        console.log('找到统计数字元素:', datacenterStats.length);
        
        if (datacenterStats.length === 0) {
            console.error('未找到任何数字元素');
            return;
        }
        
        // 先保存原始值，以便后续动画使用
        datacenterStats.forEach(el => {
            if (!el.hasAttribute('data-target')) {
                let value = el.textContent.trim();
                // 判断是否包含特殊符号
                let isPercent = value.includes('%');
                let isPlus = value.includes('+');
                
                // 提取纯数字
                value = value.replace(/[^0-9.]/g, '');
                let numValue = parseInt(value) || 0; // 防止NaN
                
                // 存储目标值和格式
                el.setAttribute('data-target', numValue);
                if (isPercent) el.setAttribute('data-format', 'percent');
                if (isPlus) el.setAttribute('data-format', 'plus');
                
                // 初始化为0
                if (numValue > 0) {
                    if (isPercent) el.textContent = '0%';
                    else if (isPlus) el.textContent = '0+';
                    else el.textContent = '0';
                }
            }
        });
        
        // 开始新的动画
        datacenterStats.forEach(el => {
            const targetValue = parseInt(el.getAttribute('data-target')) || 0;
            const format = el.getAttribute('data-format') || '';
            const isPercent = format === 'percent';
            const isPlus = format === 'plus';
            
            // 清除可能正在进行的动画
            if (el._animationTimer) {
                clearTimeout(el._animationTimer);
                delete el._animationTimer;
            }
            
            // 激活新的动画
            animateNumberFixed(el, targetValue, isPercent, isPlus);
        });
    }
    
    // 完全重写的数字动画函数 - 使用setTimeout替代requestAnimationFrame避免可能的冲突
    function animateNumberFixed(element, targetValue, isPercent, isPlus) {
        if (!element || targetValue === undefined) return;
        
        // 确保目标值是数字
        targetValue = parseInt(targetValue) || 0;
        
        // 格式化函数
        function formatValue(value) {
            let roundedValue = Math.round(value);
            if (isPercent) {
                return roundedValue + '%';
            } else if (isPlus) {
                return roundedValue + '+';
            }
            return roundedValue.toString();
        }
        
        // 当前值
        let currentValue = 0;
        // 当currentValue到达0.95倍目标值时快速结束
        const threshold = targetValue * 0.95;
        // 每次增加的量，使得动画总时间约为2秒
        const frameTime = 30; // 毌秒
        
        // 计算每次递增的值 (使用指数减速函数)
        const totalSteps = Math.min(30, targetValue); // 最多30步
        let increment = Math.max(1, Math.ceil(targetValue / totalSteps));
        
        // 防止空白内容
        element.textContent = formatValue(0);
        
        function step() {
            // 增加当前值
            currentValue += increment;
            
            // 加速完成动画
            if (currentValue >= threshold) {
                element.textContent = formatValue(targetValue);
                return;
            }
            
            // 防止超过目标值
            if (currentValue > targetValue) {
                currentValue = targetValue;
            }
            
            // 显示当前值
            element.textContent = formatValue(currentValue);
            
            // 继续动画
            if (currentValue < targetValue) {
                element._animationTimer = setTimeout(step, frameTime);
            }
        }
        
        // 开始递增动画
        element._animationTimer = setTimeout(step, frameTime);
    }
    
    // 确保页面加载后立即执行数字动画
    // 立即执行一次
    initNumberCounters();
    
    // 每次都连续尝试几次，确保在所有情况下都能正常运行
    for (let i = 1; i <= 5; i++) {
        setTimeout(initNumberCounters, i * 500); // 500ms, 1000ms, 1500ms, 2000ms, 2500ms
    }
    
    // 监听滚动事件，当滚动到数据中心区域时触发动画
    window.addEventListener('scroll', function() {
        const datacenterInfo = document.querySelector('.datacenter-info');
        if (datacenterInfo && isElementInViewport(datacenterInfo)) {
            initNumberCounters();
        }
    });
    
    // 监听窗口进入可见状态
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            setTimeout(initNumberCounters, 100); // 当页面变为可见状态时执行
        }
    });
    
    // 检查元素是否在视口中
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
    
    // 初始化科技感粒子效果
    function initTechParticles() {
        const container = document.getElementById('techParticles');
        if (!container) return;
        
        // 创建20-30个粒子
        const particleCount = 20 + Math.floor(Math.random() * 10);
        
        for (let i = 0; i < particleCount; i++) {
            createParticle(container);
        }
    }
    
    // 创建单个粒子并设置动画
    function createParticle(container) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // 随机位置
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        particle.style.left = x + '%';
        particle.style.top = y + '%';
        
        // 随机大小和亮度
        const size = 1 + Math.random() * 2;
        const opacity = 0.3 + Math.random() * 0.7;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.opacity = opacity;
        
        // 添加到容器
        container.appendChild(particle);
        
        // 应用动画
        animateParticle(particle);
    }
    
    // 粒子动画
    function animateParticle(particle) {
        // 闪烁动画
        const duration = 2 + Math.random() * 3;
        particle.style.animation = `flicker ${duration}s infinite`;
        
        // 添加闪烁动画关键帧
        if (!document.querySelector('#particle-keyframes')) {
            const style = document.createElement('style');
            style.id = 'particle-keyframes';
            style.textContent = `
                @keyframes flicker {
                    0% { opacity: 0.3; }
                    50% { opacity: 0.8; }
                    100% { opacity: 0.3; }
                }
            `;
            document.head.appendChild(style);
        }
        
        // 每隔10-20秒移动到新位置
        setInterval(() => {
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            particle.style.transition = 'left 8s ease, top 8s ease';
            particle.style.left = x + '%';
            particle.style.top = y + '%';
        }, 10000 + Math.random() * 10000);
    }
    
    // 初始化机房枢纽
    console.log('初始化机房枢纽区域效果');
    
    // 数据中心信息配置
    const dataCenters = [
        {
            id: 'beijing',
            name: '京津冀枢纽',
            desc: '京津冀枢纽：张家口集群高性能计算中心，提供AI训练和推理服务'
        },
        {
            id: 'shanghai',
            name: '长三角枢纽',
            desc: '长三角枢纽：芜湖集群、长三角生态绿色一体化发展示范集团'
        },
        {
            id: 'guangzhou',
            name: '粤港澳枢纽',
            desc: '粤港澳枢纽：韶关集群，提供全面云服务和边缘计算能力'
        },
        {
            id: 'chengdu',
            name: '贵州枢纽',
            desc: '贵州枢纽：集安集群，算力中心，支持大规模并行计算'
        },
        {
            id: 'xian',
            name: '内蒙古枢纽',
            desc: '内蒙古枢纽：和林格尔集群核心节点，提供高可靠性灾备服务'
        },
        {
            id: 'wuhan',
            name: '宁夏枢纽',
            desc: '宁夏枢纽：中卫集群，专注科研计算和数据分析'
        },
        {
            id: 'shenyang',
            name: '成渝枢纽',
            desc: '成渝枢纽：重庆集群，工业互联网和智能制造支持'
        },
        {
            id: 'wulumuqi',
            name: '甘肃枢纽',
            desc: '甘肃枢纽：天府集群处理中心，服务一带一路战略'
        }
    ];
    
    // 等待中国地图图片完全加载
    const chinaMapImg = document.getElementById('chinaMapImage');
    const chinaMapContainer = document.getElementById('chinaMapContainer');
    
    if (!chinaMapImg || !chinaMapContainer) {
        console.error('未找到中国地图或容器元素');
        return;
    }
    
    // 橙色点的精确坐标，基于图片尺寸的比例
    // 这些坐标点是相对于图片尺寸的百分比位置，可以适应不同尺寸的图片
    const pointCoordinates = [
        { x: 64.8, y: 45.5 }, // 北京
        { x: 71.0, y: 59.5 }, // 上海
        { x: 61.0, y: 71 }, // 广州
        { x: 52.5, y: 66.0 }, // 成都
        { x: 55.0, y: 44.5 }, // 西安
        { x: 51.5, y: 50.0 }, // 武汉
        { x: 51.0, y: 59.0 }, // 沈阳
        { x: 49.0, y: 53.5 }  // 乌鲁木齐
    ];
    
    function createMapPoints() {
        // 清空现有点
        const existingPoints = chinaMapContainer.querySelectorAll('.map-point');
        existingPoints.forEach(point => point.remove());
        
        // 为每个数据中心创建点
        dataCenters.forEach((center, index) => {
            if (index < pointCoordinates.length) {
                const point = document.createElement('div');
                point.className = 'map-point';
                point.setAttribute('data-location', center.name);
                point.setAttribute('data-desc', center.desc);
                
                // 根据id添加特定的类名，以控制信息框弹出的位置
                if (center.id === 'beijing' || center.id === 'guangzhou') {
                    point.classList.add('info-right');
                } else if (center.id === 'chengdu') {
                    point.classList.add('info-left');
                } else {
                    // 其他数据中心根据索引决定位置
                    if (index % 2 === 0) {
                        point.classList.add('info-right');
                    } else {
                        point.classList.add('info-left');
                    }
                }
                
                // 设置点的位置
                point.style.top = pointCoordinates[index].y + '%';
                point.style.left = pointCoordinates[index].x + '%';
                
                // 创建点的内部结构
                point.innerHTML = `
                    <div class="point-dot"></div>
                    <div class="point-pulse pulse1"></div>
                    <div class="point-pulse pulse2"></div>
                    <div class="point-info">
                        <h4>${center.name}数据中心</h4>
                        <p>${center.desc.split('：')[1]}</p>
                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-number" data-value="${95 + Math.floor(Math.random() * 5)}">0</div>
                                <div class="stat-label">可用率</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" data-value="${Math.floor(50 + Math.random() * 50)}">0</div>
                                <div class="stat-label">节点数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number plus" data-value="${Math.floor(10 + Math.random() * 90)}">0</div>
                                <div class="stat-label">服务器</div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加点到容器
                chinaMapContainer.appendChild(point);
                
                // 添加事件监听器
                point.addEventListener('mouseenter', function(e) {
                    // 阻止事件冒泡以确保其他元素不会影响我们的效果
                    e.stopPropagation();
                    
                    // 立即添加info-visible类，确保名称被隐藏
                    this.classList.add('info-visible');
                    setTimeout(() => {
                        // 再次确保类被添加（双重保险）
                        this.classList.add('info-visible');
                    }, 10);
                    
                    // 隐藏所有其他地图点
                    document.querySelectorAll('.map-point').forEach(p => {
                        if (p !== this) {
                            // 将其他点隐藏
                            p.classList.add('other-hidden');
                            
                            // 隐藏信息框
                            const otherInfo = p.querySelector('.point-info');
                            if (otherInfo) {
                                otherInfo.style.visibility = 'hidden';
                                otherInfo.style.opacity = '0';
                                otherInfo.style.transform = 'translateY(10px)';
                            }
                            p.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1)';
                            p.classList.remove('info-visible'); // 移除类名
                        }
                    });
                    
                    // 美化当前点
                    this.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1.2)';
                    const info = this.querySelector('.point-info');
                    if (info) {
                        info.style.visibility = 'visible';
                        info.style.opacity = '1';
                        info.style.transform = 'translateY(0)';
                        
                        // 启动数字计数动画
                        const statNumbers = info.querySelectorAll('.stat-number');
                        statNumbers.forEach(el => {
                            const value = parseInt(el.getAttribute('data-value'));
                            el.style.setProperty('--target-num', value);
                            el.classList.add('counting');
                            
                            // 检查是否为百分比格式
                            if (el.closest('.stat-item').querySelector('.stat-label').textContent.includes('率')) {
                                el.classList.add('percent');
                            }
                        });
                    }
                });
                
                point.addEventListener('mouseleave', function() {
                    // 恢复当前点的状态
                    this.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1)';
                    const info = this.querySelector('.point-info');
                    if (info) {
                        info.style.visibility = 'hidden';
                        info.style.opacity = '0';
                        info.style.transform = 'translateY(10px)';
                        this.classList.remove('info-visible'); // 移除类名
                        
                        // 重置数字计数状态
                        const statNumbers = info.querySelectorAll('.stat-number');
                        statNumbers.forEach(el => {
                            el.classList.remove('counting');
                            // 等动画结束后再清除
                            setTimeout(() => {
                                el.textContent = '0';
                            }, 500);
                        });
                    }
                    
                    // 恢复所有其他点的显示
                    document.querySelectorAll('.map-point.other-hidden').forEach(p => {
                        p.classList.remove('other-hidden');
                    });
                });
                
                // 触摸事件支持
                point.addEventListener('touchstart', function(e) {
                    e.preventDefault();
                    e.stopPropagation(); // 阻止事件冒泡
                    
                    // 检查是否已经处于激活状态
                    const isActive = this.classList.contains('info-visible');
                    
                    // 如果已经激活，则恢复所有点，否则隐藏其他点
                    if (isActive) {
                        // 恢复所有点
                        document.querySelectorAll('.map-point').forEach(p => {
                            p.classList.remove('other-hidden');
                            p.classList.remove('info-visible');
                            
                            const pointInfo = p.querySelector('.point-info');
                            if (pointInfo) {
                                pointInfo.style.visibility = 'hidden';
                                pointInfo.style.opacity = '0';
                                pointInfo.style.transform = 'translateY(10px)';
                                
                                // 重置数字计数状态
                                const statNumbers = pointInfo.querySelectorAll('.stat-number');
                                statNumbers.forEach(el => {
                                    el.classList.remove('counting');
                                    // 等动画结束后再清除
                                    setTimeout(() => {
                                        el.textContent = '0';
                                    }, 500);
                                });
                            }
                            p.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1)';
                        });
                    } else {
                        // 立即添加info-visible类，确保名称被隐藏
                        this.classList.add('info-visible');
                        
                        // 用延时再次添加，确保样式生效
                        setTimeout(() => {
                            this.classList.add('info-visible');
                            // 手动设置名称标签为隐藏
                            if (this.querySelector('::after')) {
                                try {
                                    this.style.setProperty('--after-visibility', 'hidden');
                                    this.style.setProperty('--after-opacity', '0');
                                } catch(e) {}
                            }
                        }, 10);
                        
                        // 隐藏其他所有点
                        const allPoints = document.querySelectorAll('.map-point');
                        allPoints.forEach(p => {
                            if (p !== this) {
                                // 隐藏该点
                                p.classList.add('other-hidden');
                                
                                // 隐藏该点的信息框
                                const otherInfo = p.querySelector('.point-info');
                                if (otherInfo) {
                                    otherInfo.style.visibility = 'hidden';
                                    otherInfo.style.opacity = '0';
                                    otherInfo.style.transform = 'translateY(10px)';
                                }
                                p.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1)';
                                p.classList.remove('info-visible'); // 移除类名
                            }
                        });
                        
                        // 美化当前点
                        this.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1.2)';
                        const info = this.querySelector('.point-info');
                        if (info) {
                            info.style.visibility = 'visible';
                            info.style.opacity = '1';
                            info.style.transform = 'translateY(0)';
                            
                            // 启动数字计数动画
                            const statNumbers = info.querySelectorAll('.stat-number');
                            statNumbers.forEach(el => {
                                const value = parseInt(el.getAttribute('data-value'));
                                el.style.setProperty('--target-num', value);
                                el.classList.add('counting');
                                
                                // 检查是否为百分比格式
                                if (el.closest('.stat-item').querySelector('.stat-label').textContent.includes('率')) {
                                    el.classList.add('percent');
                                }
                            });
                        }
                    }
                });
            }
        });
        
        console.log(`成功创建了 ${dataCenters.length} 个地图点`);
    }
    
    // 当图片加载完成后创建点
    if (chinaMapImg.complete) {
        console.log('地图已加载，立即创建点');
        createMapPoints();
    } else {
        chinaMapImg.onload = function() {
            console.log('地图加载完成，创建点');
            createMapPoints();
        };
    }
    
    // 添加全局点击事件，点击其他区域时关闭所有信息框
    document.addEventListener('click', function(e) {
        // 检查点击是否在地图点之外
        if (!e.target.closest('.map-point')) {
            // 隐藏所有信息框并恢复所有点
            document.querySelectorAll('.map-point').forEach(point => {
                // 恢复点的显示
                point.classList.remove('other-hidden');
                
                // 隐藏信息框
                const info = point.querySelector('.point-info');
                if (info) {
                    info.style.visibility = 'hidden';
                    info.style.opacity = '0';
                    info.style.transform = 'translateY(10px)';
                    
                    // 重置数字计数状态
                    const statNumbers = info.querySelectorAll('.stat-number');
                    statNumbers.forEach(el => {
                        el.classList.remove('counting');
                        // 等动画结束后再清除
                        setTimeout(() => {
                            el.textContent = '0';
                        }, 500);
                    });
                }
                point.querySelector('.point-dot').style.transform = 'translate(-50%, -50%) scale(1)';
                point.classList.remove('info-visible'); // 移除类名
            });
        }
    });
    
    
    // 窗口大小改变时，重新创建点以保持位置准确
    window.addEventListener('resize', function() {
        console.log('窗口大小改变，重新创建点');
        setTimeout(createMapPoints, 100);
    });
});
</script>
</body>
</html>
