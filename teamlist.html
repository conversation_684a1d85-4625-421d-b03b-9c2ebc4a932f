<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8" />
<meta name="format-detection" content="telephone=no" />
<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta name="HandheldFriendly" content="true" />
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon" />
<!--[if lt IE 9]>
    <script type="text/javascript" src="static/js/css3-mediaqueries.js">
    </script>
    <![endif]-->
<meta name="Author" content="易思信网页工作室 www.eczone.net ">
<!--
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
           网站设计制作 by 易思信网页工作室 www.eczone.net   
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
    -->
<title>聚算云技术白皮书 - 算力共享实践</title>
<meta name="keywords" content='算力调度平台,云服务解决方案,边缘计算服务,AI算力服务,算力资源池,聚算云' />
<meta name="description" content='聚算云致力于用共享经济重塑算力分配规则，建立一个连接算力供应方与需求方的智能调度平台，实现算力资源的自由流动和高效利用。' />
<link rel="shortcut icon" href="favicon.ico" />
<link rel="stylesheet" href="static/css/animate.css" type="text/css" >
<link rel="stylesheet" href="static/css/common.css" type="text/css" >
<link rel="stylesheet" href="static/css/style.css" type="text/css" >
<script type="text/javascript" src="static/js/jquery.min.js"></script> 
</head>
<body>
<section class="wrapper">
  <header class="header"> <a href="/" class="logo"> <img class="hide" src="static/picture/logo.png" alt="" /> <img class="show" src="static/picture/logo2.png" alt="" /> </a>
    <ul class="navs">
      <li class=""><a href="/">首页</a></li>
      <li ><a href="page-abouts.html">关于我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业使命与愿景<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">企业文化<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">团队介绍<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">投资者关系<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="products-services.html">产品与服务<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="solution-cases.html">解决方案<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="solution-cases.html#gaming-cafe">电竞云网吧<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#ai-glasses">AI眼镜<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#digital-transformation">企业数字化转型<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#hpc-research">科研机构高性能计算<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#big-data-analytics">数据分析与大数据处理<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="solution-cases.html#media-rendering">媒体内容制作与渲染<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="hyyy-hyyy.html">成功案例<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">互联网行业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">人工智能<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">制造业<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">金融服务<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教育与科研<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">政府与公共服务<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">技术与创新<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">技术白皮书<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">研发团队<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">专利与知识产权<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">合作与生态<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">合作伙伴<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">生态体系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">合作模式<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li ><a href="#">资源中心<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">产品手册<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">教程与指南<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">行业洞察<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">算力知识库<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
      <li><a href="contact-contacts.html">联系我们<i class="iconfont v">&#xe6b9;</i></a>
        <div class="navs-menus-box">
          <ol class="navs-menus">
            <li><a href="#">销售咨询<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">技术支持<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">媒体联系<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">加入我们<i class="iconfont">&#xe631;</i></a></li>
            <li><a href="#">办公地点<i class="iconfont">&#xe631;</i></a></li>
          </ol>
        </div>
      </li>
    </ul>
    <div class="header-menu">
      <div class="header-search"> <span class="iconfont">&nbsp;</span> </div>
      <div class="menu-btn">
        <p>MENU</p>
        <div class="menubtn"> <span></span> </div>
      </div>
      <div class="menu-flex">
        <div class="menu-bg"></div>
        <div class="menu-right">
          <ul class="menu-list">
            <li><a href="/">首页</a></li>
            <li ><a href="page-abouts.html">关于我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="page-1751-1752.html">公司简介<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业使命与愿景<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">企业文化<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">团队介绍<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">投资者关系<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="products-services.html">产品与服务<em></em></a>
              <ol class="menu-leval">
                <li><a href="products-services.html#compute-platform">算力调度平台<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#cloud-solutions">云服务解决方案<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#edge-computing">边缘计算服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#ai-compute">AI算力服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="products-services.html#compute-pool">算力资源池<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="solution-cases.html">解决方案<em></em></a>
              <ol class="menu-leval">
                <li><a href="solution-cases.html#gaming-cafe">电竞云网吧<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#ai-glasses">AI眼镜<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#digital-transformation">企业数字化转型<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#hpc-research">科研机构高性能计算<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#big-data-analytics">数据分析与大数据处理<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="solution-cases.html#media-rendering">媒体内容制作与渲染<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="hyyy-hyyy.html">成功案例<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">互联网行业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">人工智能<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">制造业<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">金融服务<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教育与科研<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">政府与公共服务<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">技术与创新<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">技术白皮书<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">研发团队<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">专利与知识产权<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">合作与生态<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">合作伙伴<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">生态体系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">合作模式<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="#">资源中心<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">白皮书与研究报告<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">产品手册<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">教程与指南<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">常见问题(FAQ)<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">行业洞察<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">算力知识库<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
            <li ><a href="contact-contacts.html">联系我们<em></em></a>
              <ol class="menu-leval">
                <li><a href="#">销售咨询<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">技术支持<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">媒体联系<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">加入我们<i class="iconfont">&#xe624;</i></a></li>
                <li><a href="#">办公地点<i class="iconfont">&#xe624;</i></a></li>
              </ol>
            </li>
          </ul>
          <div class="menu-ip-down">
            <div class="online-shopp"> </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class="main-content-wrap">
    <div class="content-wrap">
      <div class="head-search">
        <form action="search-search.html" method="post">
          <input class="search-ipt" type="text" name="query" placeholder="输入关键词...">
          <input class="search-btn" type="submit" name="submit" value="" title="搜索">
        </form>
      </div>
    </div>
  </div>
  <div class="mobile-body-mask"></div>
  <div class="pbanner">
    <div class="product-hide">
      <figure><img class="pc" src="static/picture/casebanner.jpg" alt="聚算云技术白皮书"/>
        <div class="sjimg" style="background-image:url(static/images/casebanner.jpg);"> </div>
      </figure>
    </div>
    <div class="series global-fix">
      <p class="article-block slidetop">Technical Whitepaper</p>
      <div class="series-title article-block slidetop detay1 syht"> <strong>算力共享实践 · 创新价值共赢</strong> </div>
    </div>
    <div class="sea-down defaul"> <i></i> <span>向下滑动</span> </div>
  </div>
  
  <!--pageTop-->
  <div class="pageTop">
    <div class="w1600 clearfix">
      <div class="pageNav">
        <ul class="navlist clearfix swiper-wrapper">
          <li ><a href="#" ><span>技术白皮书</span></a></li>
          <li class="on"><a href="#" ><span>研发团队</span></a></li>
          <li><a href="#" ><span>专利与知识产权</span></a></li>
        </ul>
      </div>
    </div>
  </div>
  <!--pageTop end--> 
  
  <!--page-->
  <div class="page clearfix"> 
    <!--pageInfo-->
    <div class="pageInfo clearfix">
      <div class="pageTit"><span class="en wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">R&D Team</span>
        <div class="cn wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s"><span class="syht">聚算云研发团队</span></div>
      </div>
      <div class="team-intro wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s">
        <p>聚算云汇聚了来自人工智能、云计算、分布式系统和高性能计算领域的顶尖人才，拥有多年行业经验和深厚的技术积累。我们的研发团队致力于突破算力调度的技术边界，打造更高效、更智能的算力共享平台。</p>
      </div>
      
      <!-- 团队技术能力展示 -->
      <div class="tech-ability-section">
        <h3 class="section-title wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">核心技术能力</h3>
        <div class="tech-ability-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="tech-ability-item">
            <svg class="tech-ability-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 11c0 5.55-3.84 10.74-9 12-5.16-1.26-9-6.45-9-12V5l9-4 9 4v6m-9 10c3.75-1 7-5.46 7-9.78V6.3l-7-3.12L5 6.3v4.92C5 15.54 8.25 20 12 21z"></path>
            </svg>
            <h4>分布式系统架构</h4>
            <div class="ability-bar">
              <div class="ability-progress" style="width:95%"></div>
            </div>
          </div>
          <div class="tech-ability-item">
            <svg class="tech-ability-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 16.47V7.39l-6 4.54-6-4.54v9.08l6 4.54 6-4.54M16 8.1l-5 3.79-5-3.79v7.08l5 3.79 5-3.79V8.1z"></path>
            </svg>
            <h4>云原生技术</h4>
            <div class="ability-bar">
              <div class="ability-progress" style="width:92%"></div>
            </div>
          </div>
          <div class="tech-ability-item">
            <svg class="tech-ability-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M5 2c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1M1 4h22v2H1V4m0 3h22v10H1V7m18 12c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m-4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m-4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m-4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m-4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1z"></path>
            </svg>
            <h4>高性能计算</h4>
            <div class="ability-bar">
              <div class="ability-progress" style="width:90%"></div>
            </div>
          </div>
          <div class="tech-ability-item">
            <svg class="tech-ability-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 4C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 12.5c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"></path>
            </svg>
            <h4>人工智能与机器学习</h4>
            <div class="ability-bar">
              <div class="ability-progress" style="width:88%"></div>
            </div>
          </div>
          <div class="tech-ability-item">
            <svg class="tech-ability-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 3C6.95 3 3.15 4.85 0 7.23L12 22 24 7.25C20.85 4.87 17.05 3 12 3zm1 13h-2v-6h2v6zm-2-8V6h2v2h-2z"></path>
            </svg>
            <h4>网络安全与隐私保护</h4>
            <div class="ability-bar">
              <div class="ability-progress" style="width:86%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 团队成员展示 -->
      <div class="team-member-section">
        <h3 class="section-title wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">核心研发成员</h3>
        <div class="team-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="team-member">
            <div class="member-avatar">
              <img src="static/picture/team1.jpg" alt="张明" class="member-photo">
              <div class="member-avatar-overlay"></div>
            </div>
            <div class="member-info">
              <h4 class="member-name">张明</h4>
              <p class="member-title">首席技术官</p>
              <p class="member-desc">前谷歌高级研究员，分布式系统专家，拥有10余年大规模计算资源调度经验。主导设计了聚算云核心调度引擎。</p>
              <div class="member-skills">
                <span>分布式系统</span>
                <span>资源调度</span>
                <span>高可用架构</span>
              </div>
            </div>
          </div>
          
          <div class="team-member">
            <div class="member-avatar">
              <img src="static/picture/team1.jpg" alt="李婷" class="member-photo">
              <div class="member-avatar-overlay"></div>
            </div>
            <div class="member-info">
              <h4 class="member-name">李婷</h4>
              <p class="member-title">算法研究主管</p>
              <p class="member-desc">人工智能与机器学习专家，曾任职于英伟达AI研究院。负责聚算云智能预测与资源优化算法设计。</p>
              <div class="member-skills">
                <span>机器学习</span>
                <span>深度学习</span>
                <span>计算优化</span>
              </div>
            </div>
          </div>
          
          <div class="team-member">
            <div class="member-avatar">
              <img src="static/picture/team1.jpg" alt="王杰" class="member-photo">
              <div class="member-avatar-overlay"></div>
            </div>
            <div class="member-info">
              <h4 class="member-name">王杰</h4>
              <p class="member-title">云架构师</p>
              <p class="member-desc">云原生技术专家，精通Kubernetes与容器编排，前亚马逊AWS架构师。负责聚算云的基础设施设计与实现。</p>
              <div class="member-skills">
                <span>Kubernetes</span>
                <span>微服务</span>
                <span>DevOps</span>
              </div>
            </div>
          </div>
          
          <div class="team-member">
            <div class="member-avatar">
              <img src="static/picture/team1.jpg" alt="陈颖" class="member-photo">
              <div class="member-avatar-overlay"></div>
            </div>
            <div class="member-info">
              <h4 class="member-name">陈颖</h4>
              <p class="member-title">网络与安全主管</p>
              <p class="member-desc">网络安全与隐私保护专家，拥有CISSP认证。负责聚算云安全架构设计与数据隐私保护方案实施。</p>
              <div class="member-skills">
                <span>安全架构</span>
                <span>数据加密</span>
                <span>隐私计算</span>
              </div>
            </div>
          </div>

          <div class="team-member">
            <div class="member-avatar">
              <img src="static/picture/team1.jpg" alt="刘强" class="member-photo">
              <div class="member-avatar-overlay"></div>
            </div>
            <div class="member-info">
              <h4 class="member-name">刘强</h4>
              <p class="member-title">高性能计算专家</p>
              <p class="member-desc">GPU计算与并行计算领域专家，曾就职于国家超算中心。主导聚算云的异构计算资源调度与优化。</p>
              <div class="member-skills">
                <span>CUDA</span>
                <span>并行计算</span>
                <span>性能优化</span>
              </div>
            </div>
          </div>

          <div class="team-member">
            <div class="member-avatar">
              <img src="static/picture/team1.jpg" alt="赵宇" class="member-photo">
              <div class="member-avatar-overlay"></div>
            </div>
            <div class="member-info">
              <h4 class="member-name">赵宇</h4>
              <p class="member-title">前端技术总监</p>
              <p class="member-desc">WebGL与交互设计专家，前字节跳动技术专家。负责聚算云用户界面与可视化系统的设计与实现。</p>
              <div class="member-skills">
                <span>可视化</span>
                <span>WebGL</span>
                <span>交互设计</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 团队理念部分 -->
      <div class="team-philosophy-section">
        <h3 class="section-title wow fadeInDown" data-wow-duration="1.2s" data-wow-delay="0.3s">研发理念</h3>
        <div class="philosophy-container wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.4s">
          <div class="philosophy-item">
            <svg class="philosophy-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3m6.82 6L12 12.72 5.18 9 12 5.28 18.82 9M17 16l-5 2.72L7 16v-3.73L12 15l5-2.73V16z"></path>
            </svg>
            <h4>开放创新</h4>
            <p>我们相信技术的开放性与创新性，积极推动行业技术交流与开源共享，加速算力技术的普及与创新。</p>
          </div>
          <div class="philosophy-item">
            <svg class="philosophy-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2c5.53 0 10 4.47 10 10s-4.47 10-10 10S2 17.53 2 12 6.47 2 12 2m0 2c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8m2 3.5L14 9l-1.22-1.28h-1.87c-.54 0-1 .2-1.38.57L8.1 9.66c-.16.17-.26.39-.26.64 0 .24.1.46.26.63l4.39 4.39c.17.16.39.26.64.26.24 0 .46-.1.63-.26l1.38-1.38c.37-.38.57-.84.57-1.38v-1.88L14 9l-.01-.01L16 7m-6.03 4.5c-.55 0-1.01-.45-1.01-1s.46-1 1.01-1 1 .45 1 1-.45 1-1 1z"></path>
            </svg>
            <h4>极致性能</h4>
            <p>追求每一行代码的极致优化与性能提升，为用户提供最高效率的算力调度与资源利用体验。</p>
          </div>
          <div class="philosophy-item">
            <svg class="philosophy-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4m0 2.18l7 3.12v4.92C19 16.67 15.97 21.1 12 22.47c-3.97-1.37-7-5.8-7-11.25V6.3l7-3.12M11 7v2h2V7h-2m0 4v6h2v-6h-2z"></path>
            </svg>
            <h4>安全可靠</h4>
            <p>以安全为基石，构建可靠的系统架构，通过严格的安全审核和持续的风险评估，确保每位用户的算力资源安全。</p>
          </div>
          <div class="philosophy-item">
            <svg class="philosophy-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 12c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5m0 2a3 3 0 0 1 3 3 3 3 0 0 1-3 3 3 3 0 0 1-3-3 3 3 0 0 1 3-3M2 7h4v2H2V7m0 4h4v2H2v-2m0 4h4v2H2v-2m0 4h4v2H2v-2m9-16h2v22h-2V3m4 0h2v5h-2V3m4 0h2v5h-2V3z"></path>
            </svg>
            <h4>持续迭代</h4>
            <p>采用敏捷开发与持续集成方法，快速响应市场需求，不断优化产品体验，保持技术领先性。</p>
          </div>
        </div>
      </div>
      
      <!-- 加入我们部分 -->
      <div class="join-us-section wow fadeInUp" data-wow-duration="1.2" data-wow-delay="0.3s">
        <div class="join-us-container">
          <div class="join-us-content">
            <h3>加入我们</h3>
            <p>如果你热爱技术，渴望挑战，希望参与改变算力分配规则的伟大事业，聚算云期待你的加入！</p>
            <a href="#" class="btn-join">查看职位</a>
          </div>
          <div class="join-us-bg"></div>
        </div>
      </div>
    </div>
    <!--End--> 
  </div>
  <!--page End--> 
  <!--footer--> 
  <!--footer-->
  <div class="footer clearfix"> 
    <!--footer-nav-->
    <div class="footer-nav">
      <div class="w1600 clearfix">
        <div class="left">
          <div class="leftBox clearfix">
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="solution-cases.html">蒸汽节能解决方案</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="solution-1805-1809.html">ESOS-蒸汽系统节能优化</a></li>
                    <li class="t2"><a href="solution-1805-1810.html">蒸汽疏水阀调查</a></li>
                    <li class="t2"><a href="solution-1805-1811.html">蒸汽系统诊断调查</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="advantage-ys.html">我们的优势</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="advantage-ys.html">一体式蒸汽工程解决方案</a></li>
                    <li class="t2"><a href="advantage-ys.html#adBox2">创新节能技术</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1"><a href="page-abouts.html">关于我们</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="page-1751-1752.html">公司介绍</a></li>
                    <li class="t2"><a href="page2-1751-1753.html">常问问题</a></li>
                    <li class="t2"><a href="solution-1751-1832.html">服务客户</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="ul2">
              <div class="ul2B">
                <div class="t1 clearfix"><a href="hyyy-hyyy.html">行业应用案例</a></div>
                <div class="listB">
                  <ul class="ulB">
                    <li class="t2"><a href="hylist-1808-1814.html">化工</a></li>
                    <li class="t2"><a href="hylist-1808-1815.html">石化</a></li>
                    <li class="t2"><a href="hylist-1808-1816.html">制药</a></li>
                    <li class="t2"><a href="hylist-1808-1817.html">食品饮料</a></li>
                    <li class="t2"><a href="hylist-1808-1818.html">其它</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="rightBox clearfix">
            <table width="100%">
              <tbody>
                <tr>
                  <td><h1> 服务热线 </h1></td>
                </tr>
                <tr>
                  <td class="tel"><h1> <span>+86 13321109027</span> </h1>
                    <p> 诚挚为您服务 </p></td>
                </tr>
              </tbody>
            </table>
            <div class="bottom-share"> <span>关注我们：</span> <a class="iconfont email" href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">&#xe6db;</a> <a class="iconfont qq" href="http://wpa.qq.com/msgrd?v=3&uin=393202489&site=kingtin&menu=yes" target="_blank" rel="noopener noreferrer">&#xe60f;</a> <a class="iconfont weix"  rel="noopener noreferrer">&#xe660;<span class="img"><img src="static/picture/202203011551003232.jpg" alt="" /><br />
              </span></a> </div>
          </div>
        </div>
      </div>
      <div class="bottom-wz">
        <div class="w1600 clearfix">思德乐，提供卓越的蒸汽工程解决方案。</div>
      </div>
    </div>
    <!--footer-nav end-->
    <div class="bq clearfix">
      <div class="w1600 clearfix">
        <div class="bqname"> &copy;2021 佛山市思德乐能源科技有限公司 版权所有 <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">粤ICP备2022022663号</a> </div>
        <div class="beian"> 网站设计: <a href="http://www.king-tin.com/" target="_blank" rel="noopener noreferrer">KINGTIN</a><script charset="UTF-8" id="LA_COLLECT" src="static/js/js-sdk-pro.min.js"></script><script>LA.init({id: "Jh6tR8OE31b5wc6v",ck: "Jh6tR8OE31b5wc6v"})</script> 
        </div>
      </div>
    </div>
  </div>
  <!--End footer-->
  <div class="common-bott">
    <div class="back-top commontop iconfont">&#xe731;</div>
  </div>
</section>
<script type="text/javascript" src="static/js/wow.min2.js"></script> 
<script type="text/javascript" src="static/js/plugin.js"></script> 
<script type="text/javascript" src="static/js/page.js"></script> 
<script>
var scrolling = true;
var timer = null;
$(document).on('mousewheel DOMMouseScroll', function (e) {
        if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
        navigateUp();
        } else {
        if (e.pageY <= w_height && scrolling && !isMobile) {
        clearTimeout(timer);
        scrolling = false;
        timer = setTimeout(function () {
        jQuery("html,body").stop(true, true).animate({ scrollTop: w_height }, 1200, "easeOutQuint")
        }, 100)
        }
        }
});
function navigateUp() {
        scrolling = true;
}
</script>
<style>
/* 团队介绍样式 */
.team-intro {
  max-width: 1200px;
  margin: 30px auto;
  text-align: center;
  font-size: 18px;
  line-height: 1.8;
  color: #333;
  padding: 0 20px;
}

/* 核心技术能力部分 */
.tech-ability-section {
  margin: 50px auto;
  max-width: 1200px;
  padding: 20px;
}

.section-title {
  text-align: center;
  color: #2b64a7;
  font-size: 28px;
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 15px;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 60px;
  height: 3px;
  background: #2b64a7;
  transform: translateX(-50%);
}

.tech-ability-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 30px;
}

.tech-ability-item {
  width: calc(33% - 30px);
  min-width: 300px;
  padding: 25px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.tech-ability-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.15);
}

.tech-ability-icon {
  width: 48px;
  height: 48px;
  fill: #2b64a7;
  margin-bottom: 15px;
}

.tech-ability-item h4 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

.ability-bar {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 15px;
}

.ability-progress {
  height: 100%;
  background: linear-gradient(90deg, #2b64a7, #5b94d7);
  border-radius: 4px;
}

/* 团队成员部分 */
.team-member-section {
  margin: 50px auto;
  max-width: 1200px;
  padding: 20px;
}

.team-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.team-member {
  width: calc(33% - 30px);
  min-width: 300px;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  position: relative;
}

.team-member:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.15);
}

.member-avatar {
  height: 180px;
  background: linear-gradient(135deg, #2b64a7, #5b94d7);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.member-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: relative;
  z-index: 1;
}

.member-avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('static/picture/circuit-pattern.png') center/cover;
  opacity: 0.15;
}

.member-info {
  padding: 25px;
}

.member-name {
  font-size: 22px;
  font-weight: 600;
  color: #2b64a7;
  margin-bottom: 5px;
}

.member-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
  font-weight: 500;
}

.member-desc {
  font-size: 14px;
  line-height: 1.6;
  color: #444;
  margin-bottom: 15px;
}

.member-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.member-skills span {
  background: rgba(43, 100, 167, 0.1);
  color: #2b64a7;
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 20px;
}

/* 团队理念部分 */
.team-philosophy-section {
  margin: 50px auto;
  max-width: 1200px;
  padding: 20px;
}

.philosophy-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 25px;
}

.philosophy-item {
  width: calc(25% - 25px);
  min-width: 250px;
  padding: 30px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  text-align: center;
  transition: all 0.3s ease;
}

.philosophy-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(43, 100, 167, 0.15);
  background: linear-gradient(135deg, rgba(43, 100, 167, 0.03), rgba(91, 148, 215, 0.07));
}

.philosophy-icon {
  width: 60px;
  height: 60px;
  fill: #2b64a7;
  margin-bottom: 20px;
}

.philosophy-item h4 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #2b64a7;
  font-weight: 600;
}

.philosophy-item p {
  font-size: 14px;
  line-height: 1.6;
  color: #444;
}

/* 加入我们部分 */
.join-us-section {
  margin: 50px auto;
  max-width: 1200px;
  padding: 20px;
}

.join-us-container {
  height: 250px;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.join-us-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #2b64a7, #5b94d7);
  z-index: 1;
}

.join-us-bg:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('static/picture/ai-pattern.png') center/cover;
  opacity: 0.1;
}

.join-us-content {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 0 30px;
  color: #fff;
}

.join-us-content h3 {
  font-size: 36px;
  margin-bottom: 20px;
  font-weight: 600;
}

.join-us-content p {
  font-size: 18px;
  margin-bottom: 30px;
  max-width: 600px;
  line-height: 1.6;
}

.btn-join {
  display: inline-block;
  padding: 12px 30px;
  background: #2B64AD;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 10px rgba(43, 100, 173, 0.15);
}

.btn-join:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(255, 128, 0, 0.2);
  background: rgba(255, 128, 0, 1);
  color: #fff;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .tech-ability-item, .team-member, .philosophy-item {
    width: calc(50% - 20px);
    min-width: auto;
  }
  
  .join-us-content h3 {
    font-size: 28px;
  }
  
  .join-us-content p {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .tech-ability-item, .team-member, .philosophy-item {
    width: 100%;
  }
  
  .join-us-container {
    height: 300px;
  }
  
  .section-title {
    font-size: 24px;
  }
}
</style>
</body>
</html>
